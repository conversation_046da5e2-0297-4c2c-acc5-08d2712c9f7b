const {
  NEXT_PUBLIC_API_PREFIX_REST,
  NEXT_PUBLIC_API,
  NEXT_PUBLIC_SIGN_IN_URL,
} = process?.env;

export const config = {
  apiService: process.env.NEXT_PUBLIC_API,
  restPrefix: process.env.NEXT_PUBLIC_API_PREFIX_REST,
  signIn: process.env.NEXT_PUBLIC_SIGN_IN_URL,
  limit: 5,
  map_api: process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY,
  mapApiURL: process.env.NEXT_PUBLIC_GOOGLE_MAP_API_URL,
  wpBlogDashboard: process.env.NEXT_PUBLIC_WP_BLOG_DASHBOARD
};
