import Link from 'next/link';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import Table from '@/components/global/table/table';
import { config } from 'config';
import myImageLoader from 'image/loader';
import { ICreateBaseExcercise } from 'models';
import Image from 'next/image';
import { handlePagination } from 'utils/handlePagination';
import { toast } from 'react-toastify';

interface Props {
  userList: any;
  setSkip: Function;
  skip: number;
  setFitmarketUser: Function;
  getAllFitmarketUsers: Function;
  // showSeeMore: boolean;
  // setLoadData: Function;
  // loadData: boolean;
  // setShowSeeMore: Function;
}

const FitmarketUserList: FC<Props> = ({
  userList,
  setSkip,
  skip,
  // showSeeMore,
  // setLoadData,
  // loadData,
  // setShowSeeMore,
  setFitmarketUser,
  getAllFitmarketUsers,
}) => {
  const [modal, setModal] = useState({
    delete: false,
  });
  const [user, setUser] = useState<any>();

  const onChangeForList = async (skip: number) => {
    try {
      const res = await userAPI.getUserList(skip, 20);
      if ('data' in res) {
        setFitmarketUser(res.data);
      } else {
        toast.error(res.error.message);
      }
      setSkip(skip);
    } catch (error) {}
  };

  const handleRemove = async () => {
    try {
      const res = await userAPI.deleteUser(user?.userName, user?.email);
      if ('data' in res) {
        toast.success(res.data);
        onChangeForList(skip);
      } else {
        toast.error(res?.error.message);
      }
      setModal({
        ...modal,
        delete: false,
      });
    } catch (error) {}
  };

  const onClickForDelete = (user: any) => {
    setUser(user);
    setModal({ ...modal, delete: true });
  };

  const columns = [
    {
      label: 'Attachment',
      path: 'profile',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data.image.profile ? (
            <Image
              loader={myImageLoader}
              height={70}
              width={70}
              src={data.image[key]}
              alt="User's Profile Picture"
            />
          ) : (
            'No Image Available'
          )}
        </td>
      ),
    },
    {
      label: 'Name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Email',
      path: 'email',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Remove User',
      path: 'email',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <button
            className="btn btn-outline-danger"
            onClick={() =>
              onClickForDelete({ userName: data.name, email: data.email })
            }
          >
            Remove
          </button>
        </td>
      ),
    },
  ];

  const onClickForSort = () => {};

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={userList}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>
      {modal.delete ? (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              // close modal when outside of modal is clicked
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                // do not close modal if anything inside modal content is clicked
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this item?</p>
                <br />

                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{
                      border: '1px solid gray',
                      backgroundColor: 'gray',
                      color: 'white',
                      marginRight: '10px',
                    }}
                    onClick={() =>
                      setModal({
                        ...modal,
                        delete: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => handleRemove()}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default FitmarketUserList;
