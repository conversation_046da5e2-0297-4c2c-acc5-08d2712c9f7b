import { config } from 'config';
import { Manufacturer } from 'models';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { userAPI } from '../../APIs/index';
import ManufactureList from './List/manufactureList';
import PrevNextPagination from '../common/newPagination';

const List = () => {
  const [manufactureData, setManufactureData] = useState<Manufacturer[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  const [showSeeMore, setShowSeeMore] = useState(true);
  const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getAllManufacturers = async () => {
    try {
      const res = await userAPI.getManufacturer(skip, limit);
      if ('data' in res!) {
        //const newArray = manufactureData.concat(res.data.manufacturers);
        if (res.data.manufacturers.length === 0) {
          setDisableNext(true);
          if (skip === 0) setManufactureData(res.data.manufacturers);
        } else {
          setDisableNext(false);
          setManufactureData(res.data.manufacturers);
        }
      } else {
        toast.error(res?.error.message);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getAllManufacturers();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Manufacturers</div>
          <Link href={'/Manufacturer/add-new'} className="btn btn-primary">
            Add new
          </Link>
        </div>
        <div>
          {manufactureData ? (
            <>
              <ManufactureList
                manufactureData={manufactureData}
                setManufactureData={setManufactureData}
                setSkip={setSkip}
                skip={skip}
                // showSeeMore={showSeeMore}
                // setLoadData={setLoadData}
                // loadData={loadData}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'No data found'
          )}
        </div>
      </main>
    </>
  );
};

export default List;
