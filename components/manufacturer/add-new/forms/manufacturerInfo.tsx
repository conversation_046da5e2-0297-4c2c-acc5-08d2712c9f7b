import { useAppSelector } from '@/redux-hooks';
import { ErrorMessage, Field } from 'formik';
import myImageLoader from 'image/loader';
import Image from 'next/image';
import { FC, useState } from 'react';
import { handleMediaUpload } from 'utils/handleMediaUpload';
interface Props {
  setFieldValue: Function;
}

const CreateNewManufacturer: FC<Props> = ({ setFieldValue }) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');
  const [file, setFile] = useState<any>();
  const token = useAppSelector((state) => state.persistedReducer.auth.token);

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="product-info"
        data-hideattribute="ProductPage.HideInfoBlock"
        id="Manufacturer-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#prouctInfoTab"
            aria-expanded="true"
            aria-controls="prouctInfoTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-info-lg col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col px-3 text-start">Manufacturer Info</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="prouctInfoTab">
          <div className="card-body">
            <div id="product-details-area">
              <div className="form-group row my-2">
                <div className="col-md-3">
                  <div className="label-wrapper row row-cols-auto float-md-end">
                    <label className="col-form-label col px-1" htmlFor="Name">
                      Manufacturer Name
                    </label>
                    <span className="required text-danger ">*</span>
                  </div>
                </div>
                <div className="col-md-9">
                  <div className="input-group input-group-required">
                    <Field
                      className="border-bottom form-control rounded-0 border-2 border border-0 shadow-none"
                      id="name"
                      name="name"
                      type="text"
                    />
                  </div>
                  <div className="errMsg text-danger text-red-600">
                    <ErrorMessage name="name" />
                  </div>
                </div>
              </div>
              <div className="form-group row my-2">
                <div className="col-md-3">
                  <div className="label-wrapper row row-cols-auto float-md-end">
                    <label
                      className="col-form-label col px-1"
                      htmlFor="FullDescription"
                    >
                      Full description
                    </label>
                    <span className="required text-danger ">*</span>
                  </div>
                </div>
                <div className="col-md-9">
                  <div className="input-group pe-3 ">
                    <Field
                      as="textarea"
                      className="border-bottom form-control rounded-0 border-2 border border-0 shadow-none"
                      id="description"
                      name="description"
                    />
                  </div>
                  <div className="errMsg text-danger text-red-600">
                    <ErrorMessage name="description" />
                  </div>
                </div>
              </div>
            </div>
            <div className="form-group row my-2">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end">
                  <label className="col-form-label col px-1" htmlFor="Name">
                    Picture
                    <span className="required text-danger ">*</span>
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className="input-group input-group-required mt-2">
                  <input
                    id="picture"
                    name="picture"
                    type="file"
                    onChange={async (
                      event: React.ChangeEvent<HTMLInputElement>
                    ) => {
                      const fileData = {
                        featureName: 'manufacturer',
                        filenames: [event?.target?.files![0].name],
                      };
                      const fileInfo = await handleMediaUpload(
                        fileData,
                        event?.target?.files![0],
                        token,
                        true
                      );
                      setFieldValue('picture', fileInfo);
                      setFile({
                        src: URL.createObjectURL(event?.target?.files![0]),
                        type: event?.target?.files![0].type,
                      });
                    }}
                  />
                  <br />
                  {file && (
                    <>
                      {file.type.includes('image') ? (
                        <Image
                          loader={myImageLoader}
                          className="mt-3"
                          src={file.src}
                          height={200}
                          width={200}
                          alt="animation"
                        />
                      ) : (
                        <div className="embed-responsive embed-responsive-16by9">
                          <video
                            width="240"
                            height="200"
                            controls={true}
                            className="embed-responsive-item"
                          >
                            <source src={file.src} type="video/mp4" />
                          </video>
                        </div>
                      )}
                    </>
                  )}
                  {/* <Field
                    className="border-bottom form-control rounded-0 border-2 border border-0 shadow-none"
                    id="picture"
                    name="picture"
                    type="text"
                  /> */}
                </div>
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="picture" />
                </div>
              </div>
            </div>
            <div className="form-group row">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end py-2">
                  <label
                    className="col-form-label col px-1"
                    htmlFor="showOnHomePage"
                  >
                    Published?
                    <span className="required text-danger ">*</span>
                  </label>
                </div>
              </div>
              <div className="col-md-9 mt-md-3 mx-md-0 mx-2">
                <Field
                  className="check-box mt-2"
                  id="isPublished"
                  name="isPublished"
                  type="checkbox"
                />
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="isPublished" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CreateNewManufacturer;
