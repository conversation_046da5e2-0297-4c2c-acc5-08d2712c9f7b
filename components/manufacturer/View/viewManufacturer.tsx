import Link from 'next/link';
import { FC } from 'react';
import DisplayOrderCard from './viewCards/displayOrder';
import ManufacturerInfoCard from './viewCards/manufacturerInfo';
import SeoCard from './viewCards/seo';
interface Props {
  manufacturer: any;
}
const ViewManufacturerComp: FC<Props> = ({ manufacturer }) => {
  return (
    <>
      {manufacturer ? (
        <div>
          <div className="content-header clearfix mt-3">
            <h1 className="float-start fs-2">
              View Manufacturer details
              <span className="fs-5 p-3">
                <Link href={'/Manufacturer'} className="text-decoration-none">
                  <i className="bi bi-arrow-left-circle-fill p-2" />
                  Back to Manufacturer list
                </Link>
              </span>
            </h1>
          </div>

          <div className="mt-4">
            <ManufacturerInfoCard manufacturer={manufacturer} />
            <DisplayOrderCard manufacturer={manufacturer} />
            <SeoCard manufacturer={manufacturer} />
          </div>
        </div>
      ) : (
        ''
      )}
    </>
  );
};

export default ViewManufacturerComp;
