import { useRouter } from 'next/router';
import { FC, useEffect, useState } from 'react';
import { userAPI } from '../../../APIs';
import ViewManufacturerComp from './viewManufacturer';
import { toast } from 'react-toastify';
const ViewSingleManufacturer: FC = () => {
  const router = useRouter();
  const [ready, setReady] = useState(false);
  const [manufacturer, setManufacturer] = useState<any>();
  const id = '' + `${router.query.id}`;

  const getManufacturer = async () => {
    try {
      const res = await userAPI.getSingleManufacturer(id);
      if ('data' in res) setManufacturer(res.data.manufacturer);
      else toast.error(res.error.message);
    } catch (error) {}
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getManufacturer();
    }
  }, [router.isReady]);

  if (!ready) {
    return null;
  }

  return (
    <>
      <div className="bg-light px-5">
        <main>
          {manufacturer ? (
            <ViewManufacturerComp manufacturer={manufacturer} />
          ) : (
            'No Manufacturers Found'
          )}
        </main>
      </div>
    </>
  );
};

export default ViewSingleManufacturer;
