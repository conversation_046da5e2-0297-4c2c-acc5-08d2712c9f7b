import { Form, Formik } from 'formik';

const ManufacturerSearchWindow = ({ setProducts, allProducts }: any) => {
  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          name: '',
          published: '',
        }}
        onSubmit={(values, actions) => {
          const data = {
            name: values.name,
            published: values.published,
          };
        }}
        //    actions.setSubmitting(false);
      >
        {(formikprops) => {
          return (
            <Form
              onSubmit={formikprops.handleSubmit}
              onKeyDown={onKeyDown}
            ></Form>
          );
        }}
      </Formik>
    </>
  );
};

export default ManufacturerSearchWindow;
