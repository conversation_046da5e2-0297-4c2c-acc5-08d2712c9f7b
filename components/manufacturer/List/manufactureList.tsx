import { config } from 'config';
import myImageLoader from 'image/loader';
import { Manufacturer } from 'models';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';
import { userAPI } from '../../../APIs';
import Table from '../../global/table/table';
import { handlePagination } from 'utils/handlePagination';
import { toast } from 'react-toastify';

interface Props {
  manufactureData: Manufacturer[];
  setManufactureData: Function;
  setSkip: Function;
  skip: number;
  // showSeeMore: boolean;
  // setLoadData: Function;
  // loadData: boolean;
}

const ManufactureList: FC<Props> = ({
  manufactureData,
  setManufactureData,
  setSkip,
  skip,
  // showSeeMore,
  // setLoadData,
  // loadData,
}) => {
  const router = useRouter();
  const [ProductID, setProductID] = useState('');

  const onChangeForList = async (skip: number) => {
    try {
      const manufactureData = await userAPI.getManufacturer(skip, config.limit);
      if ('data' in manufactureData) setManufactureData(manufactureData);
      else {
        toast.error(manufactureData.error.message);
      }
    } catch (error) {}
    setSkip(0);
  };

  const deleteProductFunction = async () => {
    const res = await userAPI.deleteManufacturer(ProductID, router);
    if ('data' in res) {
      toast.success('Successfully deleted');
      onChangeForList(0);
    } else {
      toast.error(res.error.message);
    }
    setModal({
      ...modal,
      delete: false,
    });
  };

  const onClickForDelete = (id: string) => {
    setProductID(id);
    setModal({ ...modal, delete: true });
  };
  const [modal, setModal] = useState({
    delete: false,
  });

  const columns = [
    {
      label: 'Picture',
      path: 'url',
      content: (data: any, key: any, index: any) => (
        <td className="text-center">
          <Image
            loader={myImageLoader}
            src={data.picture}
            height={75}
            width={75}
            alt="..."
          />
        </td>
      ),
    },
    {
      label: 'Manufacturer Name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">{data.name}</td>
      ),
    },
    {
      label: 'Display Order',
      path: 'displayOrder',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          {data?.displayOrder}
        </td>
      ),
    },
    {
      label: 'Published',
      path: 'published',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          {data?.isPublished ? <i className="bi bi-check-lg"></i> : '-'}
        </td>
      ),
    },
    {
      label: 'Edit',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          <Link
            href={{
              pathname: `/Manufacturer/Edit/[id]`,
              query: { id: data?.id },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-outline-info">
              <span>
                <i className="bi bi-pencil me-2 align-middle"></i>
              </span>
              Edit
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'View',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          <Link
            href={{
              pathname: `/Manufacturer/View/[id]`,
              query: { id: data?.[key] },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Delete',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          <button
            className="btn btn-outline-danger"
            onClick={() => onClickForDelete(data.id)}
          >
            <i className="bi bi-pencil me-2 align-middle"></i>
            Delete
          </button>
        </td>
      ),
    },
  ];
  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          {manufactureData?.length > 0 ? (
            <>
              <Table items={manufactureData} columns={columns} />
              {/* {showSeeMore && (
                <button
                  className="text-primary float-end border-0 bg-transparent"
                  onClick={() =>
                    handlePagination(skip, setSkip, loadData, setLoadData)
                  }
                  type="button"
                >
                  See More...
                </button>
              )} */}
            </>
          ) : (
            ''
          )}
        </div>
      </div>
      {modal.delete ? (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '30%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this item?</p>
                <br />

                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{
                      border: '1px solid gray',
                      backgroundColor: 'gray',
                      color: 'white',
                      marginRight: '10px',
                    }}
                    onClick={() =>
                      setModal({
                        ...modal,
                        delete: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => deleteProductFunction()}
                  >
                    <span id="deleteModal"> Delete</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default ManufactureList;
