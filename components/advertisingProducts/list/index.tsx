import Link from 'next/link';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import Table from '@/components/global/table/table';
import { config } from 'config';
import myImageLoader from 'image/loader';
import Image from 'next/image';
import { toast } from 'react-toastify';
import { handlePagination } from 'utils/handlePagination';

interface Props {
  productsList: any;
  setProducts: Function;
  setSkip: Function;
  skip: number;
  // showSeeMore: boolean;
  // setLoadData: Function;
  // loadData: boolean;
}

const AdvertisingProductsList: FC<Props> = ({
  productsList,
  setProducts,
  setSkip,
  skip,
}) => {
  // const [currentPage, setCurrentPage] = useState(1);
  // const [PageSize, setPageSize] = useState(7);
  const [ProductID, setProductID] = useState('');

  const onChangeForList = async (skip: number) => {
    const productsList = await userAPI.getAdvertisingProducts(
      skip,
      config.limit
    );
    setProducts(productsList.data);
    setSkip(0);
  };

  const deleteProductFunction = async () => {
    try {
      const res = await userAPI.deleteAdvertisingProduct(ProductID);
      if ('data' in res) {
        onChangeForList(0);
        toast.success(res.data.message);
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
    setModal({
      ...modal,
      delete: false,
    });
  };

  const onClickForDelete = (id: string) => {
    setProductID(id);
    setModal({ ...modal, delete: true });
  };

  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const [modal, setModal] = useState({
    delete: false,
  });

  // const currentTableData = useMemo(() => {
  //   const firstPageIndex = (currentPage - 1) * PageSize;
  //   const lastPageIndex = firstPageIndex + PageSize;
  //   return productsList?.slice(firstPageIndex, lastPageIndex);
  // }, [currentPage, PageSize, productsList]);

  const columns = [
    {
      label: 'Picture',
      path: 'url',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.product.photos[0][key] && (
            <Image
              loader={myImageLoader}
              src={`${data?.product.photos[0][key]}`}
              height={75}
              width={75}
              alt="..."
            />
          )}
        </td>
      ),
    },
    {
      label: 'Product Name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.product.info[key]}</td>
      ),
    },
    {
      label: 'SKU',
      path: 'sku',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.product.info[key]}</td>
      ),
    },
    {
      label: 'Price',
      path: 'price',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.product.info[key]}</td>
      ),
    },
    {
      label: 'Manufacturer',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.product.manufacturer && data?.product.manufacturer[key]
            ? data?.product.manufacturer[key]
            : '---'}
        </td>
      ),
    },
    {
      label: 'Categories',
      path: 'categories',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.product.info[key]}
          {data?.product.categories[0]
            ? data?.product.categories[0].name
            : '---'}
          {data?.product.categories?.map((category: any, index: any) =>
            index > 0 ? ` , ${category?.name}` : ''
          )}
        </td>
      ),
    },
    {
      label: 'Published',
      path: 'published',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          {data?.product.info[key] ? <i className="bi bi-check-lg"></i> : 'X'}
        </td>
      ),
    },
    {
      label: 'View',
      path: 'productId',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/Product/View/[id]`,
              query: { id: data?.[key] },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Delete',
      path: 'productId',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <button
            className="btn btn-default btn-outline-danger"
            onClick={() => onClickForDelete(data?.[key])}
          >
            <i className="bi bi-trash3-fill me-2 align-middle"></i>
            Delete
          </button>
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          {productsList?.length > 0 ? (
            <>
              <Table
                items={productsList}
                columns={columns}
                onClickForSort={onClickForSort}
              />
            </>
          ) : (
            'No product to show'
          )}
          {/* <div className="">
            {productsList?.length > 0 ? (
              <Pagination
                currentPage={currentPage}
                totalCount={productsList.length}
                pageSize={PageSize}
                setCurrentPage={setCurrentPage}
                setPageSize={setPageSize}
              />
            ) : null}
          </div> */}
        </div>
      </div>
      {modal.delete ? (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              // close modal when outside of modal is clicked
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                // do not close modal if anything inside modal content is clicked
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this item?</p>
                <br />

                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{
                      border: '1px solid gray',
                      backgroundColor: 'gray',
                      color: 'white',
                      marginRight: '10px',
                    }}
                    onClick={() =>
                      setModal({
                        ...modal,
                        delete: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => deleteProductFunction()}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default AdvertisingProductsList;
