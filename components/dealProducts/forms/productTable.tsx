import Link from 'next/link';
import { FC } from 'react';

import Table from '@/components/global/table/table';
import { config } from 'config';
import { Field } from 'formik';
import myImageLoader from 'image/loader';
import { Product } from 'models';
import Image from 'next/image';
import { handlePagination } from 'utils/handlePagination';

interface Props {
  productList: Product[];
  setSkip: Function;
  skip: number;
  showSeeMore: boolean;
  setLoadData: Function;
  loadData: boolean;
}

const ProductTable: FC<Props> = ({
  productList,
  setSkip,
  skip,
  showSeeMore,
  setLoadData,
  loadData,
}) => {
  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const columns = [
    {
      label: 'Select One',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Field type="radio" name="productId" value={data[key]} />
        </td>
      ),
    },
    {
      label: 'Picture',
      path: 'url',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.photos![0][key] && (
            <Image
              loader={myImageLoader}
              src={`${data?.photos![0][key]}`}
              height={75}
              width={75}
              alt="..."
            />
          )}
        </td>
      ),
    },
    {
      label: 'Product name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.info[key]}</td>
      ),
    },
    {
      label: 'Price',
      path: 'price',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.info[key]}</td>
      ),
    },
    {
      label: 'Categories',
      path: 'categories',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.info[key]}
          {data?.categories[0] ? data?.categories[0].name : '---'}
          {data?.categories?.map((category: any, index: any) =>
            index > 0 ? ` , ${category?.name}` : ''
          )}
        </td>
      ),
    },

    {
      label: 'View',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/Product/View/[id]`,
              query: { id: data?.[key] },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={productList}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>
    </>
  );
};

export default ProductTable;
