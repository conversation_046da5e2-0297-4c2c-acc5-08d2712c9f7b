import { userAP<PERSON> } from '@/APIs';
import { Form, Formik } from 'formik';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC } from 'react';
import { toast } from 'react-toastify';
import ProductSelectionForm from '../forms/productSelection';
import { dealProductSchema } from '../schema';

const CreateDealProduct: FC = () => {
  const router = useRouter();

  const handleSubmit = async (data: any) => {
    try {
      const res = await userAPI.createDealProduct(data);
      if ('data' in res) {
        toast.success(res.data.message);
        router.push('/deal-products');
      } else {
        toast.error(res.error.message);
      }
    } catch (error: any) {}
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          productId: '',
        }}
        onSubmit={(values, actions) => {
          handleSubmit(values.productId);
          actions.setSubmitting(false);
        }}
        validationSchema={dealProductSchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix"
                style={{ paddingTop: '10px' }}
              >
                <h3 className="float-start fs-2">
                  Create A Deal Product
                  <span className="fs-5 p-3">
                    <Link
                      href="/deal-products"
                      className="text-decoration-none"
                    >
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      <span style={{ fontSize: '14px' }}>
                        Back to deal products list
                      </span>
                    </Link>
                  </span>
                </h3>
                <div className="float-end">
                  <button
                    type="submit"
                    name="save"
                    className="btn btn-primary m-1"
                  >
                    {/* <i className="bi bi-save" /> */}
                    <p className="float-end mx-1 my-0">Save</p>
                  </button>
                </div>
              </div>
              <div className="mt-4">
                <ProductSelectionForm />
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default CreateDealProduct;
