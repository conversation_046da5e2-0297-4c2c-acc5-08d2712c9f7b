import moment from 'moment';
import Link from 'next/link';
import { useState } from 'react';
import { trimDescription } from 'utils/trim';
import Modal from './modal';

interface Props {
  data: any;
}

const Tbody: React.FC<Props> = ({ data }) => {
  const [updateState, setUpdateState] = useState(false);

  const handleStatusUpdate = () => {
    const x = (document.getElementById('status') as HTMLInputElement).value;
  };

  return (
    <>
      <td className="p-auto m-auto text-center align-middle">
        <Link href={`/Sales/Order/Edit/${data.orderId}`}>{data.orderId}</Link>
      </td>
      <td className="p-auto m-auto text-center align-middle">
        {data.billingAddress.firstName + ' ' + data.billingAddress.lastName}
      </td>
      <td className="p-auto m-auto text-center align-middle">
        {data.billingAddress.email}
      </td>
      <td className="p-auto m-auto text-center align-middle">
        {data.billingAddress.phone}
      </td>
      <td className="p-auto m-auto text-center align-middle">
        {trimDescription(
          data.billingAddress.addressLine1 +
            ', ' +
            data.billingAddress.city +
            ', ' +
            data.billingAddress.state +
            ', ' +
            data.billingAddress.country +
            ', Postal Code: ' +
            data.billingAddress.postCode,
          10
        )}
      </td>
      <td className="p-auto m-auto text-center align-middle">
        {moment(data.createdAt).utc().local().format('ll')}
      </td>
      <td className="text-center align-middle">
        <div className="d-flex justify-content-center gap-2">
          {data.status === 'Resolved' ? (
            <td className="p-auto m-auto text-center align-middle">
              {data.status}
            </td>
          ) : !updateState ? (
            <>
              <p>{data.status}</p>
              <i
                className="bi bi-pencil-square"
                role="button"
                onClick={() => setUpdateState(!updateState)}
              ></i>
            </>
          ) : (
            <>
              <select id="status" name="name">
                <option>Select One</option>
                <option>Accept</option>
                <option>Reject</option>
              </select>
              <i
                className="bi bi-check2-square"
                role="button"
                onClick={() => {
                  handleStatusUpdate();
                  setUpdateState(!updateState);
                }}
              ></i>
              <i
                className="bi bi-x-square"
                role="button"
                onClick={() => setUpdateState(!updateState)}
              ></i>
            </>
          )}
        </div>
      </td>
      <td className="text-center align-middle">
        <Link
          href={{
            pathname: `/Product/View/[id]`,
            query: { id: data.id },
          }}
          passHref
          legacyBehavior
        >
          <button className="btn btn-default btn-outline-primary" disabled>
            <span>
              <i className="bi bi-eye me-2 align-middle"></i>
            </span>
            View
          </button>
        </Link>
      </td>
      <td className="text-center align-middle">
        {data.status !== 'Resolved' ? (
          <button
            type="button"
            className="btn btn-outline-info"
            data-bs-toggle="modal"
            data-bs-target="#exampleModal"
            disabled={data.status !== 'Accepted'}
          >
            <span>
              <i className="bi bi-pencil me-2 align-middle"></i>
            </span>
            Create
          </button>
        ) : (
          '--'
        )}
      </td>
      <Modal productId={data.products[0].productId} />
    </>
  );
};

export default Tbody;
