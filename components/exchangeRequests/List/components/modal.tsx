import { userAPI } from '@/APIs';
import { Product } from 'models';
import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';

interface Props {
  productId: string;
}

const Modal: React.FC<Props> = ({ productId }) => {
  const [product, setProduct] = useState<Product>();
  const [selectedColor, setSelectedColor] = useState('');
  const [selectedSize, setSelectedSize] = useState('');

  const getProductByID = async () => {
    try {
      const res = await userAPI.getProduct({ productId: `${productId}` });
      if ('data' in res!)
        res?.data ? setProduct(res.data) : toast.error(res.error.message);
    } catch (error) {}
  };

  useEffect(() => {
    getProductByID();
  }, []);
  return (
    <>
      <div
        className={`modal fade ${
          document.body.clientWidth <= 804 ? 'container mx-auto' : ''
        } `}
        id="exampleModal"
        tabIndex={-1}
        aria-labelledby="exampleModalLabel"
        aria-hidden="true"
      >
        <div className="modal-dialog">
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title" id="exampleModalLabel">
                Select Size/Color
              </h5>
              <button
                type="button"
                className="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
            <div className="modal-body">
              <p>Product: {product?.info.name}</p>
              <p>Price: {product?.info.price.toFixed(2)}</p>
              <p>Stock: {product?.info.stock}</p>
              <div className="d-flex align-items-top justify-start">
                <p className="">Size</p>
                <div className="d-flex flex-wrap gap-2 ms-2">
                  {product?.info.size?.map((size) => {
                    return (
                      <>
                        <button
                          onClick={() => setSelectedSize(size)}
                          className={`${
                            selectedSize === size
                              ? 'px-2 py-1 m-2 rounded border border-2 border-primary'
                              : 'px-2 py-1 m-2 rounded border border-none'
                          }`}
                        >
                          {size}
                        </button>
                      </>
                    );
                  })}
                </div>
              </div>
              <div className="d-flex align-items-top justify-start">
                <p className="">Color</p>
                <div className="d-flex flex-wrap gap-2 ms-2">
                  {product?.info.color?.map((color) => {
                    return (
                      <>
                        <button
                          onClick={() => setSelectedColor(color)}
                          className={`${
                            selectedColor === color
                              ? 'p-2 m-2 rounded-circle border border-2 border-primary'
                              : 'p-2 m-2 rounded-circle border border-none'
                          }`}
                          style={{
                            backgroundColor: color,
                          }}
                        ></button>
                      </>
                    );
                  })}
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button
                type="button"
                className="btn btn-secondary"
                data-bs-dismiss="modal"
              >
                Close
              </button>
              <button
                type="button"
                className="btn btn-primary"
                data-bs-dismiss="modal"
                onClick={() => {
                  alert(`New order placed according to requirement`);
                  setSelectedColor('');
                  setSelectedSize('');
                }}
              >
                Place New Order
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Modal;
