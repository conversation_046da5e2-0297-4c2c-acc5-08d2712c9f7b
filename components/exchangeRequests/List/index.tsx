import moment from 'moment';
import { FC } from 'react';
import { ISubscriber } from 'models';
import { config } from 'config';
import { handlePagination } from 'utils/handlePagination';
import Table from '@/components/global/table/table';
import Link from 'next/link';
import { trimDescription } from 'utils/trim';
import Tbody from './components/tbody';

interface Props {
  exchangeRequests: any;
  setExchangeRequests: Function;
  setSkip: Function;
  skip: number;
  // showSeeMore: boolean;
  // setLoadData: Function;
  // loadData: boolean;
}
const List: FC<Props> = ({
  exchangeRequests,
  setExchangeRequests,
  setSkip,
  skip,
  // showSeeMore,
  // setLoadData,
  // loadData,
}) => {
  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          {exchangeRequests.length > 0 && (
            <div
              className="d-flex flex-wrap justify-content-between"
              style={{ overflow: 'auto' }}
            >
              <table className="table table-bordered table-striped">
                <thead>
                  <tr>
                    <th className="text-center">Order ID</th>
                    <th className="text-center">User</th>
                    <th className="text-center">Email</th>
                    <th className="text-center">Contact No.</th>
                    <th className="text-center">Address</th>
                    <th className="text-center">Date</th>
                    <th className="text-center">Status</th>
                    <th className="text-center">View</th>
                    <th className="text-center">Create New Order</th>
                  </tr>
                </thead>
                <tbody>
                  {exchangeRequests.map((item: any) => (
                    <>
                      <tr>
                        <Tbody data={item} />
                      </tr>
                    </>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default List;
