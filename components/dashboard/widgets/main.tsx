import { userAPI } from '@/APIs';
import moment from 'moment';
import { FC, useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import Link from 'next/link';
interface Props {
  totalProducts: number;
  totalUser: number;
  totalOrders: number;
  totalSubscribers: number;
}

const StatWidgets: FC<Props> = ({
  totalProducts,
  totalUser,
  totalOrders,
  totalSubscribers,
}) => {
  const widgets = [
    {
      id: '1',
      name: 'Total User',
      type: 'stat',
      statVal: totalUser,
      icon: <i className="bi bi-chevron-double-right"></i>,
      bgColor: '#ffc107',
      link: '/fitmarket-users',
    },
    {
      id: '2',
      name: 'Total Products',
      type: 'stat',
      icon: <i className="bi bi-chevron-double-right"></i>,
      statVal: totalProducts,
      bgColor: '#17a2b8',
      link: '/Product',
    },
    {
      id: '3',
      name: 'Total Orders',
      type: 'stat',
      statVal: totalOrders,
      icon: <i className="bi bi-chevron-double-right"></i>,

      bgColor: '#28a745',
      link: '/Sales/Order/List',
    },
    {
      id: '4',
      name: 'Total Subscribers',
      type: 'stat',
      statVal: totalSubscribers,
      icon: <i className="bi bi-chevron-double-right"></i>,
      bgColor: ' #8F00FF',
      link: '/subscriber-list',
    },
  ];
  return (
    <>
      <div className="container mb-4">
        <div className="row gy-5 mt-1">
          {widgets.map((widget, index) => {
            return (
              <div key={widget.id} className="col-md-6 gx-4">
                <div className="card" style={{ border: 'none' }}>
                  <Link href={widget.link} key={widget.id}>
                    <div
                      className="position-relative card-body"
                      style={{
                        backgroundColor: `${widget.bgColor}`,
                        borderRadius: '5px',
                      }}
                    >
                      <div className="text-white">
                        <h3>{widget.statVal}</h3>
                        <p>{widget.name}</p>
                      </div>
                      <div className="position-absolute translate-middle end-0 fs-1 top-50 z-0 text-black opacity-25">
                        {widget.icon}
                      </div>
                    </div>
                  </Link>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </>
  );
};

export default StatWidgets;
