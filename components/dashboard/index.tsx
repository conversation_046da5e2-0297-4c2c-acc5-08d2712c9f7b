import { FC, useState, useEffect } from 'react';
import DateTimeFilter from './filter/main';
import StatWidgets from './widgets/main';
import moment from 'moment';
import { userAPI } from '@/APIs';
import { toast } from 'react-toastify';
const Dashboard: FC = () => {
  const day = new Date();
  const previousDay = day.setDate(day.getDate() - 1);
  const previousTime = day.setDate(day.getDate() - 0.125);
  const currentTime = `${new Date().getHours}: ${
    new Date().getMinutes
  }: ${'00'}`;
  const [startDate, setStartDate] = useState(new Date('01/01/2020'));
  const [startTime, setStartTime] = useState(new Date(previousTime));
  const [endDate, setEndDate] = useState(new Date());
  const [endTime, setEndTime] = useState(new Date());

  const [totalUser, setTotalUser] = useState();
  const [totalProducts, setTotalProducts] = useState();
  const [totalOrders, setTotalOrders] = useState();
  const [totalSubscribers, setTotalSubscribers] = useState();

  const getTotalUser = async () => {
    try {
      let res = await userAPI.getDashboardData(
        moment(startDate).toISOString(),
        moment(endDate).toISOString(),
        'total-users'
      );
      if ('data' in res) {
        setTotalUser(res.data.count);
      } else {
        toast.error(res.error.message);
      }
      res = await userAPI.getDashboardData(
        moment(startDate).toISOString(),
        moment(endDate).toISOString(),
        'total-products'
      );
      if ('data' in res) {
        setTotalProducts(res.data.count);
      } else {
        toast.error(res.error.message);
      }

      res = await userAPI.getDashboardData(
        moment(startDate).toISOString(),
        moment(endDate).toISOString(),
        'total-orders'
      );
      if ('data' in res) {
        setTotalOrders(res.data.count);
      } else {
        toast.error(res.error.message);
      }

      res = await userAPI.getDashboardData(
        moment(startDate).toISOString(),
        moment(endDate).toISOString(),
        'total-subscribers'
      );
      if ('data' in res) {
        setTotalSubscribers(res.data.count);
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getTotalUser();
  }, [startDate, endDate]);

  return (
    <>
      <div className="container mt-4">
        <DateTimeFilter
          startDate={startDate}
          setStartDate={setStartDate}
          endDate={endDate}
          setEndDate={setEndDate}
        />
        <StatWidgets
          totalProducts={totalProducts!}
          totalUser={totalUser!}
          totalOrders={totalOrders!}
          totalSubscribers={totalSubscribers!}
        />
        {/* <StatChart /> */}
      </div>
    </>
  );
};

export default Dashboard;
