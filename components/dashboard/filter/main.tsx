import { FC, useState } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import filterStyle from './styles/datePicker.module.css';

interface Props {
  startDate: Date;
  setStartDate: Function;
  endDate: Date;
  setEndDate: Function;
}

const DateTimeFilter: FC<Props> = ({
  startDate,
  setStartDate,
  endDate,
  setEndDate,
}) => {
  const [btnToggler, setBtnToggler] = useState('bi-plus-lg');

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  return (
    <>
      <div className="d-md-flex flex-md-row flex-column justify-content-evenly ms-4 flex-wrap d-none d-md-block">
        <div className="w-50">
          <div>Start Date:</div>
          <div>
            <DatePicker
              className={filterStyle.datePicker}
              selected={startDate}
              onChange={(date: Date) => setStartDate(date)}
              maxDate={new Date()}
              dateFormat="dd/MM/yyyy"
            />
          </div>
        </div>
        {/* <div className="w-full w-md-25">
          <div>Start Time:</div>
          <div>
            <DatePicker
              className={filterStyle.datePicker}
              selected={startTime}
              onChange={(time: any) => setStartTime(time)}
              showTimeSelect
              showTimeSelectOnly
              timeIntervals={15}
              timeCaption="Time"
              dateFormat="h:mm aa"
            />
          </div>
        </div> */}
        <div className="w-50">
          <div>End Date:</div>
          <div>
            <DatePicker
              className={filterStyle.datePicker}
              selected={endDate}
              onChange={(date: Date) => {
                setEndDate(date);
              }}
              minDate={new Date(startDate)}
              dateFormat="dd/MM/yyyy"
            />
          </div>
        </div>
        {/* <div className="w-full w-md-25">
          <span>End Time:</span>
          <span>
            <DatePicker
              className={filterStyle.datePicker}
              selected={endTime}
              onChange={(time: any) => setEndTime(time)}
              showTimeSelect
              showTimeSelectOnly
              timeIntervals={15}
              timeCaption="Time"
              dateFormat="h:mm aa"
            />
          </span>
        </div> */}
      </div>

      <div
        className="card d-md-none bg-transparent card-outline mt-4 mx-4"
        data-card-name="dashboard-info"
        data-hideattribute="ProductPage.HideInfoBlock"
        id="dashboard-info"
      >
        <button
          className="btn w-100 text-top invisible m-0 h-auto p-0"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#dashboardInfoTab"
          aria-expanded="true"
          aria-controls="dashboardInfoTab"
          onClick={() => toggleButton()}
        >
          <div className="card-title row align-items-center visible mx-2 mt-2">
            <div className="col text-start">Filter</div>
            <div className="col-1">
              <i className={`bi ${btnToggler}`} />
            </div>
          </div>
        </button>
        <div className="collapse bg-transparent" id="dashboardInfoTab">
          <form className="ms-3 mb-3">
            <div className="d-flex flex-column flex-md-row justify-content-around">
              <div className="w-full">
                <div>Start Date:</div>
                <div>
                  <DatePicker
                    className={filterStyle.datePicker}
                    selected={startDate}
                    onChange={(date: Date) => setStartDate(date)}
                    maxDate={new Date()}
                  />
                </div>
              </div>
              {/* <div className="w-full">
                <div>Start Time:</div>
                <div>
                  <DatePicker
                    className={filterStyle.datePicker}
                    selected={startTime}
                    onChange={(time: any) => setStartTime(time)}
                    showTimeSelect
                    showTimeSelectOnly
                    timeIntervals={15}
                    timeCaption="Time"
                    dateFormat="h:mm aa"
                  />
                </div>
              </div> */}
              <div className="w-full">
                <div>End Date:</div>
                <div>
                  <DatePicker
                    className={filterStyle.datePicker}
                    selected={endDate}
                    onChange={(date: Date) => setEndDate(date)}
                    maxDate={new Date()}
                  />
                </div>
              </div>
              {/* <div className="w-full">
                <span>End Time:</span>
                <span>
                  <DatePicker
                    className={filterStyle.datePicker}
                    selected={endTime}
                    onChange={(time: any) => setEndTime(time)}
                    showTimeSelect
                    showTimeSelectOnly
                    timeIntervals={15}
                    timeCaption="Time"
                    dateFormat="h:mm aa"
                  />
                </span>
              </div> */}
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default DateTimeFilter;
