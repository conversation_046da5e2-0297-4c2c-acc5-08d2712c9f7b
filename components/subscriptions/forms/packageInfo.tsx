import CustomSelect from '@/components/common/customSelectInput';
import FieldTemplate from '@/components/common/fieldTemplate';
import { FC, useState } from 'react';

interface Options {
  label: string;
  value: string | boolean;
}

interface Props {
  setFieldValue?: Function;
  durationUnitOptions: Options[];
  statusOptions: Options[];
  edit?: boolean;
}

const Info: FC<Props> = ({
  setFieldValue,
  edit,
  durationUnitOptions,
  statusOptions,
}) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="package-info"
        data-hideattribute="ProductPage.HideInfoBlock"
        id="package-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#packageInfoTab"
            aria-expanded="true"
            aria-controls="packageInfoTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-info-lg col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col px-3 text-start">Package Info</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="packageInfoTab">
          <div className="card-body">
            <FieldTemplate
              label="Name"
              isRequired={!edit ? true : false}
              fieldID="name"
              fieldType="text"
            />

            <FieldTemplate
              label="Type"
              isRequired={!edit ? true : false}
              fieldID="type"
              fieldType="text"
            />

            <FieldTemplate
              label="Title"
              isRequired={!edit ? true : false}
              fieldID="title"
              fieldType="text"
            />

            <FieldTemplate
              label="Description"
              isRequired={!edit ? true : false}
              fieldID="description"
              fieldType="text"
            />

            <FieldTemplate
              label="Price"
              isRequired={!edit ? true : false}
              fieldID="price"
              fieldType="number"
            />

            <FieldTemplate
              label="Currency"
              isRequired={!edit ? true : false}
              fieldID="currency"
              fieldType="text"
            />

            {/* <FieldTemplate
              label="Priority"
              isRequired={!edit ? true : false}
              fieldID="priority"
              fieldType="number"
            /> */}

            <FieldTemplate
              label="Duration"
              isRequired={!edit ? true : false}
              fieldID="duration"
              fieldType="number"
            />

            <FieldTemplate
              label="Duration In"
              isRequired={!edit ? true : false}
              fieldID="durationUnit"
              fieldType="none"
              fieldClass="custom-select w-100"
              options={durationUnitOptions}
              component={CustomSelect}
              placeholder="Select unit..."
              ismulti={false}
            />

            {/* <FieldTemplate
              label="Duration In Days"
              isRequired={!edit ? true : false}
              fieldID="durationInDays"
              fieldType="number"
            /> */}

            <FieldTemplate
              label="Status"
              isRequired={!edit ? true : false}
              fieldID="isActive"
              fieldType="none"
              fieldClass="custom-select w-100"
              options={statusOptions}
              component={CustomSelect}
              placeholder="Select status..."
              ismulti={false}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default Info;
