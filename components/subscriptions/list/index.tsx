import Link from 'next/link';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import Table from '@/components/global/table/table';
import { config } from 'config';
import { ISubscriptionPackage } from 'models';
import { toast } from 'react-toastify';
import { handlePagination } from 'utils/handlePagination';

interface Props {
  subscriptionList: ISubscriptionPackage[];
  setSubscriptions: Function;
  setSkip: Function;
  skip: number;
}

const SubscriptionsList: FC<Props> = ({
  subscriptionList,
  setSubscriptions,
  setSkip,
  skip,
}) => {
  const [PackageID, setPackageID] = useState('');
  const [modal, setModal] = useState({
    delete: false,
  });

  const onChangeForList = async (skip: number) => {
    try {
      const res = await userAPI.getPackages(skip, config.limit);
      if ('data' in res) {
        setSubscriptions(res.data);
      } else {
        toast.error(res.error.message);
      }
      setSkip(0);
    } catch (error) {}
  };

  const deleteSubscription = async () => {
    try {
      const res = await userAPI.deleteTrainingCategory(PackageID);
      if ('data' in res) {
        onChangeForList(0);
      } else {
        toast.error(res.error.message);
      }
      setModal({
        ...modal,
        delete: false,
      });
    } catch (error) {}
  };

  const onClickForDelete = (id: string) => {
    setPackageID(id);
    setModal({ ...modal, delete: true });
  };

  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const columns = [
    {
      label: 'Name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Type',
      path: 'type',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Price',
      path: 'price',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Duration',
      path: 'duration',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data[key] + ' ' + data.durationUnit}
        </td>
      ),
    },
    {
      label: 'Currency',
      path: 'currency',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Status',
      path: 'isActive',
      content: (data: any, key: any, index: any) => (
        <>
          <td className="text-center align-middle">
            {data[key] ? 'Active' : 'Inactive'}
          </td>
        </>
      ),
    },
    // {
    //   label: 'Priority/Order',
    //   path: 'priority',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="text-center align-middle">{data[key]}</td>
    //   ),
    // },
    {
      label: 'View',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/subscriptions/view/[id]`,
              query: { id: data?.[key] },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Edit',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/subscriptions/edit/[id]`,
              query: { id: data?.[key] },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-info">
              <span>
                <i className="bi bi-pencil me-2 align-middle"></i>
              </span>
              Edit
            </button>
          </Link>
        </td>
      ),
    },
    // {
    //   label: 'Delete',
    //   path: 'id',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="text-center align-middle">
    //       <button
    //         className="btn btn-default btn-outline-danger"
    //         onClick={() => onClickForDelete(data.id)}
    //       >
    //         <i className="bi bi-trash3-fill me-2 align-middle"></i>
    //         Delete
    //       </button>
    //     </td>
    //   ),
    // },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={subscriptionList}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>
      {modal.delete ? (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              // close modal when outside of modal is clicked
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                // do not close modal if anything inside modal content is clicked
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this item?</p>
                <br />

                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{
                      border: '1px solid gray',
                      backgroundColor: 'gray',
                      color: 'white',
                      marginRight: '10px',
                    }}
                    onClick={() =>
                      setModal({
                        ...modal,
                        delete: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => deleteSubscription()}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default SubscriptionsList;
