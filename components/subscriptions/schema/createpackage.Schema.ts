import * as Yup from 'yup';
import { number, object, string } from 'yup';

export const createPackageSchema = object().shape({
  name: string().required('This field must not be empty'),
  description: string().required('This field must not be empty'),
  title:string().required('This field must not be empty'),
  type:string().required('This field must not be empty'),
  price:number().required('This field must not be empty'),
  currency:string().required('This field must not be empty'),
  duration:number().required('This field must not be empty'),
  durationIn:string().required('This field must not be empty'),
  //durationInDays:number().required('This field must not be empty'),
  isActive:Yup.boolean().required('This field must not be empty'),
});
