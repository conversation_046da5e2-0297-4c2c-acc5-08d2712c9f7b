import { Form, Formik } from 'formik';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';
import Info from '../forms/packageInfo';
import { DurationUnitEnum, ISubscriptionPackage } from 'models';
import { userAPI } from '@/APIs';
import { toast } from 'react-toastify';
import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { enableEdit, showModal } from '@/toolkit/modalSlice';

interface Options {
  label: string;
  value: string | boolean;
}

interface Props {
  packageToEdit: ISubscriptionPackage;
}

const EditSubscriptionPackage: FC<Props> = ({ packageToEdit }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const edit = useAppSelector((state) => state.persistedReducer.modal.edit);
  const [updateData, setUpdateData] = useState<any>();

  const [durationUnit, setDurationUnit] = useState<Options[]>([
    {
      label: DurationUnitEnum.DAYS,
      value: DurationUnitEnum.DAYS,
    },
    {
      label: DurationUnitEnum.MONTHS,
      value: DurationUnitEnum.MONTHS,
    },
  ]);

  const [statusOptions, setStatusOptions] = useState<Options[]>([
    {
      label: 'Active',
      value: true,
    },
    {
      label: 'Inactive',
      value: false,
    },
  ]);

  const handleSubmit = async (data: any) => {
    try {
      let newPackage = { ...data, currencySymbol: '$' };
      if (data.durationUnit === DurationUnitEnum.MONTHS) {
        newPackage = { ...data, durationInDays: data.duration * 30 };
      }
      const res = await userAPI.updatePackage(packageToEdit.id!, newPackage);
      if ('data' in res) {
        router.push('/subscriptions');
        toast.success('Package updated successfully');
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  if (edit === true) {
    handleSubmit(updateData!);
    dispatch(enableEdit(false));
  }

  return (
    <>
      <Formik
        initialValues={{
          name: packageToEdit ? packageToEdit.name : '',
          type: packageToEdit ? packageToEdit.type : '',
          title: packageToEdit ? packageToEdit.title : '',
          description: packageToEdit ? packageToEdit.description : '',
          price: packageToEdit ? packageToEdit.price : 0,
          duration: packageToEdit ? packageToEdit.duration : 0,
          durationUnit: packageToEdit ? packageToEdit.durationUnit : '',
          //durationInDays: packageToEdit ? packageToEdit.durationInDays : 0,
          currency: packageToEdit ? packageToEdit.currency : '',
          isActive: packageToEdit ? packageToEdit.isActive : false,
        }}
        onSubmit={(values, actions) => {
          dispatch(showModal(true));
          setUpdateData(values);
          actions.setSubmitting(false);
        }}
        //validationSchema={categorySchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix"
                style={{ paddingTop: '10px' }}
              >
                <h3 className="float-start">
                  Edit A Subscription Package
                  <span className="fs-5 p-3">
                    <Link
                      href="/subscriptions"
                      className="text-decoration-none"
                    >
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      <span style={{ fontSize: '14px' }}>
                        Back to Subscription list
                      </span>
                    </Link>
                  </span>
                </h3>
                <div className="float-end">
                  <button
                    type="submit"
                    name="save"
                    className="btn btn-primary m-1"
                  >
                    {/* <i className="bi bi-save" /> */}
                    <p className="float-end mx-1 my-0">Save</p>
                  </button>
                </div>
              </div>
              <div className="mt-4">
                <Info
                  durationUnitOptions={durationUnit}
                  statusOptions={statusOptions}
                  edit={true}
                />
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default EditSubscriptionPackage;
