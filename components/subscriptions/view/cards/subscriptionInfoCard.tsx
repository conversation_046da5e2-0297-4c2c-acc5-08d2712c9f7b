import { FC } from 'react';

import SingleView from '@/components/common/singleView';
import { ISubscriptionPackage } from 'models';

interface Props {
  subscription: ISubscriptionPackage;
}

const SubscriptionInfoCard: FC<Props> = ({ subscription }) => {
  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="subscription-info"
        id="subscription-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <div className="card-title row align-items-center ps-2 pt-2">
            <i
              className="bi bi-info-lg col-1 align-text-top"
              style={{ fontSize: '25px' }}
            />
            <div className="fs-5 col text-start px-3">Subscription Info</div>
          </div>
        </div>
        <div className="" id="subscriptionInfoTab">
          <div className="card-body">
            <SingleView label="Name" value={subscription.name} />
            <SingleView label="Title" value={subscription.title} />
            <SingleView label="Type" value={subscription.type} />
            <SingleView label="Description" value={subscription.description} />

            {/* <SingleView label="Type" value={exercise.type} /> */}
            <SingleView label="Price" value={subscription.price} />
            <SingleView
              label="Duration"
              value={subscription.duration + ' ' + subscription.durationUnit}
            />
            <SingleView label="Currency" value={subscription.currency} />
            <SingleView
              label="Status"
              value={subscription.isActive ? 'Active' : 'Inactive'}
            />
            {/* <SingleView label="Priority" value={subscription.priority} /> */}
          </div>
        </div>
      </div>
    </>
  );
};

export default SubscriptionInfoCard;
