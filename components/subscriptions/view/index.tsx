import Link from 'next/link';
import SubscriptionInfoCard from './cards/subscriptionInfoCard';

interface Props {
  subscription: any;
}

const ViewSubscription: React.FC<Props> = ({ subscription }) => {
  return (
    <>
      {subscription ? (
        <div>
          <div className="content-header clearfix mt-3">
            <h1 className="float-start fs-2">
              View subscription details
              <span className="fs-5 p-3">
                <Link href="/subscriptions" className="text-decoration-none ">
                  <i className="bi bi-arrow-left-circle-fill p-2" />
                  Back to subscription list
                </Link>
              </span>
            </h1>
          </div>

          <div className="mt-4">
            <SubscriptionInfoCard subscription={subscription} />
          </div>
        </div>
      ) : (
        ''
      )}
    </>
  );
};

export default ViewSubscription;
