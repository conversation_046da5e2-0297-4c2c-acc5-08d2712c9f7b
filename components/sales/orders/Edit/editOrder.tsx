import Link from 'next/link';
import { FC, useState } from 'react';
import Billing from '../Cards/billing';
import Info from '../Cards/info';
import Products from '../Cards/products';
import Shipping from '../Cards/shipping';

interface Props {
  singleOrderInfo: any;
  getSingleOrderData: Function;
}

interface Options {
  label: string;
  value: string;
}

const EditOrder: FC<Props> = ({ singleOrderInfo, getSingleOrderData }) => {
  const [shippingStatusValue, setShippingStatusValue] = useState(
    singleOrderInfo.shippingStatus
  );

  const [orderStatusValue, setOrderStatusValue] = useState(
    singleOrderInfo.orderStatus
  );

  const [paymentStatusValue, setPaymentStatusValue] = useState(
    singleOrderInfo.paymentStatus
  );

  // const [orderEnum, setOrderEnum] = useState<OrderStatusTypes>();
  // const [paymentEnum, setPaymentEnum] = useState<PaymentStatusTypes>();
  // const [shippingEnum, setShippingEnum] = useState<ShippingStatusTypes>();

  // const [orderOptions, setOrderOptions] = useState<Options[]>([]);
  // const [paymentOptions, setPaymentOptions] = useState<Options[]>([]);
  // const [shippingOptions, setShippingOptions] = useState<Options[]>([]);

  // const getAllEnum = async () => {
  //   const res = await userAPI.getOrderEnum();
  //   if ('data' in res!) {
  //     setOrderEnum(res?.data?.orderStatusEnums);
  //     setPaymentEnum(res?.data?.paymentStatusEnums);
  //     setShippingEnum(res?.data?.shippingStatusEnum);
  //   }
  // };

  // useEffect(() => {
  //   getAllEnum();
  // }, []);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between flex-md-nowrap align-items-center border-bottom mb-3 flex-wrap pt-3 pb-2">
          <div className="d-flex justify-content-between ">
            <h1 className="h2 fs-2">Edit order details </h1>
            <div
              style={{ marginLeft: '10px', fontSize: '20px' }}
              className="mb-2 pt-1 pb-2"
            >
              <Link href="/Sales/Order/List" passHref legacyBehavior>
                <p
                  style={{
                    cursor: 'pointer',
                    color: '#3c8dbc',
                  }}
                >
                  <i className="bi bi-arrow-left-circle-fill"></i> back to order
                  list
                </p>
              </Link>
            </div>
          </div>
        </div>

        <Info
          singleOrderInfo={singleOrderInfo}
          shippingStatusValue={shippingStatusValue}
          orderStatusValue={orderStatusValue}
          setOrderStatusValue={setOrderStatusValue}
          paymentStatusValue={paymentStatusValue}
          setPaymentStatusValue={setPaymentStatusValue}
          getSingleOrderData={getSingleOrderData}
        />
        <Billing singleOrderInfo={singleOrderInfo} />
        <Shipping
          paymentStatusValue={paymentStatusValue}
          orderStatusValue={orderStatusValue}
          singleOrderInfo={singleOrderInfo}
          shippingStatusValue={shippingStatusValue}
          setShippingStatusValue={setShippingStatusValue}
          getSingleOrderData={getSingleOrderData}
        />
        <Products singleOrderInfo={singleOrderInfo} />
      </main>
    </>
  );
};

export default EditOrder;
