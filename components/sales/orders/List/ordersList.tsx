import Pagination from '@/components/global/pagination';
import Table from '@/components/global/table/table';
import moment from 'moment';
import Link from 'next/link';
import { FC, useMemo, useState } from 'react';
interface Props {
  orderListData: any;
}
const OrderList: FC<Props> = ({ orderListData }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [PageSize, setPageSize] = useState(7);

  const columns = [
    {
      label: 'Order Number',
      path: 'order_number',
      content: (data: any, key: any, index: any) => (
        <td className="text-center">{data.orderId}</td>
      ),
    },
    {
      label: 'Order Status',
      path: 'order_status',
      content: (data: any, key: any, index: any) => (
        <td className="text-center">
          <button
            className={
              data?.orderStatus === 'Completed'
                ? 'btn-sm bg-success mt-1 rounded border-0 px-2 text-white'
                : data?.orderStatus === 'Processing'
                ? 'btn-sm bg-info mt-1 rounded border-0 px-2 text-white'
                : data?.orderStatus === 'Cancelled'
                ? 'btn-sm bg-danger rounded mt-1 border-0 px-2 text-white'
                : 'btn-sm bg-warning mt-1 rounded border-0 px-2 text-white'
            }
            disabled
          >
            <td className="text-center">{data?.orderStatus}</td>
          </button>
        </td>
      ),
    },
    {
      label: 'Payment Status',
      path: 'payment_status',
      content: (data: any, key: any, index: any) => (
        <td className="text-center">{data?.paymentStatus}</td>
      ),
    },
    {
      label: 'Shipping Status',
      path: 'shipping_status',
      content: (data: any, key: any, index: any) => (
        <td className="text-center">
          {data?.shippingStatus === 'NotYetShipped'
            ? 'Not Yet Shipped'
            : data?.shippingStatus === 'PartiallyShipped'
            ? 'Partially Shipped'
            : data?.shippingStatus}
        </td>
      ),
    },
    {
      label: 'Customer Name',
      path: 'customer',
      content: (data: any, key: any, index: any) => (
        <td className="text-center">
          {`${data?.billingAddress.firstName} ${data?.billingAddress.lastName}`}
        </td>
      ),
    },
    {
      label: 'Order Date',
      path: 'orderedDate',
      content: (data: any, key: any, index: any) => (
        <td className="text-center">
          {moment(data?.orderedDate).utc().local().format('lll')}
        </td>
      ),
    },
    {
      label: 'Order Total',
      path: 'order_total',
      content: (data: any, key: any, index: any) => (
        <td className="text-center">{data?.totalCost}</td>
      ),
    },
    {
      label: 'View',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center">
          <Link
            href={{
              pathname: `/Sales/Order/Edit/[id]`,
              query: { id: data?.orderId },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },
  ];

  // const currentTableData = useMemo(() => {
  //   const firstPageIndex = (currentPage - 1) * PageSize;
  //   const lastPageIndex = firstPageIndex + PageSize;
  //   return orderListData?.orders?.slice(firstPageIndex, lastPageIndex);
  // }, [currentPage, PageSize, orderListData]);

  return (
    <>
      <main className="px-1">
        <div
          className="d-flex justify-content-between flex-md-nowrap align-items-center border-bottom mb-3 flex-wrap pt-3 pb-2"
          style={{ paddingLeft: '10px' }}
        >
          <h1 className="h2">Orders</h1>
        </div>
        <div className="card border-1 mt-3 rounded px-2">
          <div className="card-body">
            {orderListData.length > 0 && (
              <Table items={orderListData} columns={columns} />
            )}

            {/* <div>
              {orderListData.orders?.length > 0 ? (
                <Pagination
                  currentPage={currentPage}
                  totalCount={orderListData.orders?.length}
                  pageSize={PageSize}
                  setCurrentPage={setCurrentPage}
                  setPageSize={setPageSize}
                />
              ) : (
                'No data found'
              )}
            </div> */}
          </div>
        </div>
      </main>
    </>
  );
};

export default OrderList;
