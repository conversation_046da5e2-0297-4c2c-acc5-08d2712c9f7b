import { FC } from 'react';

interface TOOL {
  label: string;
  data: string | Date | number;
}

const Tooltip: FC<TOOL> = ({ label, data }) => {
  return (
    <div>
      <>
        <div className="row mt-1">
          <div className="col-12 col-sm-12 col-md-12 col-lg-3">
            <strong className="fs-6 me-1">{label}</strong>
          </div>
          <div className="col ms-md-5">
            <p>{data}</p>
          </div>
        </div>
      </>
    </div>
  );
};

export default Tooltip;
