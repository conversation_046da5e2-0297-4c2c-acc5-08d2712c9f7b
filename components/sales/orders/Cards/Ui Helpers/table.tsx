import { I<PERSON><PERSON>rAddress } from 'models';
import { FC } from 'react';

interface Props {
  data: IOrderAddress;
  label: string;
}

const Table: FC<Props> = ({ data, label }) => {
  return (
    <>
      <div className="w-100">
        <table className="table table-bordered">
          <thead>
            <tr>
              <th colSpan={2}>{label}</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="w-50">First name</td>
              <td>{data.firstName}</td>
            </tr>
            <tr>
              <td className="w-50">Last name</td>
              <td>{data.lastName}</td>
            </tr>
            <tr>
              <td className="w-50">Email</td>
              <td>{data.email ? data.email : '--'}</td>
            </tr>
            <tr>
              <td className="w-50">Address Line 1</td>
              <td>{data.addressLine1 ? data.addressLine1 : '--'}</td>
            </tr>
            <tr>
              <td className="w-50">Address Line 2</td>
              <td>{data.addressLine2 ? data.addressLine2 : '--'}</td>
            </tr>
            <tr>
              <td className="w-50">City</td>
              <td>{data.city ? data.city : '--'}</td>
            </tr>
            <tr>
              <td className="w-50">Postal Code</td>
              <td>{data.postCode ? data.postCode : '--'}</td>
            </tr>
            <tr>
              <td className="w-50">Phone</td>
              <td>{data.phone ? data.phone : '--'}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </>
  );
};

export default Table;
