import { userAPI } from '@/APIs';
import {
  ChangeStatusModel,
  OrderResponseData,
  OrderStatusTypes,
  PaymentStatusTypes,
  ShippingStatusTypes,
} from 'models';
import moment from 'moment';
import { FC, useEffect, useState } from 'react';
import Modal from '../../service/modal';
import Tooltip from './Ui Helpers/tooltip';
import { toast } from 'react-toastify';

interface Options {
  label: string;
  value: string;
}
interface Props {
  singleOrderInfo: OrderResponseData;
  shippingStatusValue: string;
  orderStatusValue: string;
  setOrderStatusValue: Function;
  paymentStatusValue: string;
  setPaymentStatusValue: Function;
  getSingleOrderData: Function;
  // paymentOptions: Options[];
  // orderOptions: Options[];
}

const Info: FC<Props> = ({
  getSingleOrderData,
  singleOrderInfo,
  shippingStatusValue,
  orderStatusValue,
  setOrderStatusValue,
  paymentStatusValue,
  setPaymentStatusValue,
  // paymentOptions,
  // orderOptions,
}) => {
  const [orderState, setOrderState] = useState('Pending');
  const [paymentState, setPaymentState] = useState('Pending');
  const [orderEnum, setOrderEnum] = useState<OrderStatusTypes>();
  const [paymentEnum, setPaymentEnum] = useState<PaymentStatusTypes>();
  const [shippingEnum, setShippingEnum] = useState<ShippingStatusTypes>();

  const handleOrderStatus = (event: any) => {
    setOrderState(event.target.value);
  };

  const handlePaymentStatus = (event: any) => {
    setPaymentState(event.target.value);
  };

  const getAllOrderEnum = async () => {
    const res = await userAPI.getOrderEnum();
    if ('data' in res) {
      res ? setOrderEnum(res?.data?.orderStatusEnums) : '';
      res ? setPaymentEnum(res?.data?.paymentStatusEnums) : '';
      res ? setShippingEnum(res?.data?.shippingStatusEnum) : '';
    } else {
      toast.error(res.response.message);
    }
  };

  useEffect(() => {
    getAllOrderEnum();
  }, []);

  const [modal, setModal] = useState({
    cancel_order: false,
    change_status: false,
    change_payment_status: false,
    change_status_save: false,
    change_payment_status_save: false,
  });

  const handleCancelOff = () => {
    setModal({ ...modal, cancel_order: false });
  };

  const handleChange = () => {
    setModal({ ...modal, change_status: true });
  };

  const handlePaymentChange = () => {
    setModal({ ...modal, change_payment_status: true });
  };

  const handleSaveStatus = () => {
    setModal({ ...modal, change_status_save: true });
  };

  const handlePaymentSaveStatus = () => {
    setModal({ ...modal, change_payment_status_save: true });
  };

  const handleSaveStatusOff = () => {
    setModal({ ...modal, change_status_save: false });
  };

  const handlePaymentSaveStatusOff = () => {
    setModal({ ...modal, change_payment_status_save: false });
  };

  const handlePositive = () => {
    setModal({ ...modal, change_status_save: false, change_status: false });
    const obj: ChangeStatusModel = {
      orderId: singleOrderInfo?.orderId,
      statusType: 'orderStatusEnums',
      statusValue: orderState,
    };
    setOrderStatusValue(orderState);
    userAPI.updateOrderStatus(obj);
    if (orderState === 'Cancelled') {
      const shipOb = {
        orderId: singleOrderInfo?.orderId,
        statusType: 'shippingStatusEnums',
        statusValue: 'Cancelled',
      };
      userAPI.updateShippingStatus(shipOb);
      const paymentObj = {
        orderId: singleOrderInfo?.orderId,
        statusType: 'paymentStatusEnums',
        statusValue: 'Cancelled',
      };
      userAPI.updatePaymentStatus(paymentObj);
    }
    getSingleOrderData();
  };

  const handlePaymentPositive = () => {
    setModal({
      ...modal,
      change_payment_status_save: false,
      change_payment_status: false,
    });
    const obj = {
      orderId: singleOrderInfo?.orderId,
      statusType: 'paymentStatusEnums',
      statusValue: paymentState,
    };
    setPaymentStatusValue(paymentState);
    userAPI.updatePaymentStatus(obj);
    getSingleOrderData();
    // router.push('/Sales/Order/List');
  };

  return (
    <>
      <p className="fs-4 fw-semibold">Order Info</p>
      <div className="d-flex flex-column flex-md-row gap-3">
        <div className="w-100 border fs-6 p-3 mb-3">
          <Tooltip label="Order Number" data={singleOrderInfo.orderId} />
          <Tooltip
            label={'Created on'}
            data={moment(singleOrderInfo.orderedDate)
              .utc()
              .local()
              .format('llll')}
          />
          <Tooltip label={'Customer ID'} data={singleOrderInfo.userId} />
          <Tooltip label={'Order status'} data={orderStatusValue} />
          {modal.change_status ? (
            <div>
              <select
                className="mt-2 w-auto p-2 border border-secondary text-left rounded"
                onChange={(e: any) => {
                  handleOrderStatus(e);
                }}
                defaultValue={orderState}
              >
                {singleOrderInfo.paymentStatus === paymentEnum?.Paid && (
                  <>
                    <option value={orderEnum?.Pending}>
                      {orderEnum?.Pending}
                    </option>

                    {singleOrderInfo.shippingStatus ===
                      shippingEnum?.IN_TRANSIT && (
                      <>
                        <option value={orderEnum?.Processing}>
                          {orderEnum?.Processing}
                        </option>
                        <option value={orderEnum?.Cancelled}>
                          {orderEnum?.Cancelled}
                        </option>
                      </>
                    )}
                    {singleOrderInfo.shippingStatus ===
                      shippingEnum?.DELIVERED && (
                      <>
                        {' '}
                        <option value={orderEnum?.Completed}>
                          {orderEnum?.Completed}
                        </option>
                        <option value={orderEnum?.Cancelled}>
                          {orderEnum?.Cancelled}
                        </option>
                      </>
                    )}
                  </>
                )}

                {/* {singleOrderInfo.paymentStatus === 'Pending' && (
                  <>
                    {' '}
                    <option value={orderEnum.Pending}>
                      {orderEnum.Pending}
                    </option>
                  </>
                )} */}

                {/* {orderOptions.map((option) => {
                  return (
                    <option key={option.label} value={option.value}>
                      {option.label}
                    </option>
                  );
                })} */}
              </select>
              <span className="text-left mt-2 w-25 ms-3">
                <button
                  type="button"
                  className="btn btn-danger"
                  style={{
                    marginRight: '10px',
                    backgroundColor: '#3c8dbc',
                    border: '1px solid #3c8dbc',
                  }}
                  onClick={() => handleSaveStatus()}
                >
                  Save
                </button>
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() =>
                    setModal({
                      ...modal,
                      change_status: false,
                    })
                  }
                >
                  Cancel
                </button>
                {modal.change_status_save ? (
                  <Modal
                    state={'change_status_save'}
                    handleStatus={handleSaveStatusOff}
                    handlePositive={handlePositive}
                  />
                ) : (
                  <></>
                )}
              </span>
            </div>
          ) : (
            <button
              type="button"
              className="btn text-white"
              style={{
                backgroundColor: '#3c8dbc',
                border: '1px solid #3c8dbc',
              }}
              disabled={
                singleOrderInfo.paymentStatus === 'Pending' ||
                singleOrderInfo.orderStatus === 'Cancelled'
              }
              onClick={() => handleChange()}
            >
              Change status
            </button>
          )}
        </div>
        <div className="w-100 border fs-6 p-3 mb-3">
          <Tooltip
            label={'Order subtotal'}
            data={singleOrderInfo.productCost.toFixed(2)}
          />
          <Tooltip
            label={'Order shipping'}
            data={singleOrderInfo.shippingCost}
          />
          <Tooltip
            label={'Order total'}
            data={singleOrderInfo.totalCost.toFixed(2)}
          />

          <Tooltip
            label={'Payment method'}
            data={singleOrderInfo.paymentMethod}
          />
          <Tooltip label={'Payment status'} data={paymentStatusValue} />
          {modal.change_payment_status ? (
            <div>
              <select
                className="mt-2 w-auto p-2 border border-secondary text-left rounded"
                onChange={(e: any) => {
                  handlePaymentStatus(e);
                }}
                defaultValue={paymentState}
              >
                {singleOrderInfo.orderStatus === paymentEnum?.Pending && (
                  <>
                    {' '}
                    <option value={paymentEnum?.Pending}>
                      {paymentEnum?.Pending}
                    </option>
                  </>
                )}
                <option value={paymentEnum?.Paid}>{paymentEnum?.Paid}</option>
                {singleOrderInfo.orderStatus === 'Cancelled' && (
                  <>
                    <option value={paymentEnum?.Cancelled}>
                      {paymentEnum?.Cancelled}
                    </option>
                  </>
                )}
                {/* {paymentOptions.map((option) => {
                  return (
                    <option key={option.label} value={option.value}>
                      {option.label}
                    </option>
                  );
                })} */}
              </select>
              <span
                style={{
                  textAlign: 'left',
                  marginTop: '10px',
                  width: '25%',
                  marginLeft: '5%',
                }}
              >
                <button
                  type="button"
                  className="btn btn-danger"
                  style={{
                    marginRight: '10px',
                    backgroundColor: '#3c8dbc',
                    border: '1px solid #3c8dbc',
                  }}
                  onClick={() => handlePaymentSaveStatus()}
                >
                  Save
                </button>
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() =>
                    setModal({
                      ...modal,
                      change_payment_status: false,
                    })
                  }
                >
                  Cancel
                </button>
                {modal.change_payment_status_save ? (
                  <Modal
                    state={'change_status_save'}
                    handleStatus={handlePaymentSaveStatusOff}
                    handlePositive={handlePaymentPositive}
                  />
                ) : (
                  <></>
                )}
              </span>
            </div>
          ) : (
            <button
              type="button"
              className="btn text-white"
              style={{
                backgroundColor: '#3c8dbc',
                border: '1px solid #3c8dbc',
              }}
              disabled={
                paymentStatusValue === 'Cancelled' ||
                singleOrderInfo.orderStatus === 'Completed'
              }
              onClick={() => handlePaymentChange()}
            >
              Change status
            </button>
          )}
        </div>
      </div>

      {modal.cancel_order ? (
        <Modal state={'cancel_order'} handleStatus={handleCancelOff} />
      ) : (
        <div />
      )}
    </>
  );
};

export default Info;
