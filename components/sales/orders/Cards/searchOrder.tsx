import { FC, useState } from 'react';

interface Props {
  setOrderStatus: Function;
  setPaymentStatus: Function;
  setShippingStatus: Function;
  setStartDate: Function;
  setEndDate: Function;
  getAllOrderList: Function;
  setId: Function;
  handleSearch: (event: any) => void;
  startDate: any;
}

const SearchOrder: FC<Props> = ({
  setOrderStatus,
  setEndDate,
  setPaymentStatus,
  setShippingStatus,
  setStartDate,
  getAllOrderList,
  startDate,
  setId,
  handleSearch,
}) => {
  const [btnToggler, setBtnToggler] = useState('bi-plus-lg');

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  const handleReset = () => {
    setOrderStatus('');
    setPaymentStatus('');
    setShippingStatus('');
    setStartDate('');
    setEndDate('');
    getAllOrderList();
  };
  return (
    <>
      <div>
        <div
          className="card card-secondary card-outline my-4"
          data-card-name="order-info"
          data-hideattribute="ProductPage.HideInfoBlock"
          id="order-info"
        >
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#orderInfoTab"
            aria-expanded="true"
            aria-controls="orderInfoTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title mx-2 mt-2 row align-items-center visible">
              <div className="fs-5 col px-3 text-start">Search</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
          <div className="collapse" id="orderInfoTab">
            <form className="m-3">
              <div className="d-flex flex-column flex-md-row justify-content-around gap-3">
                <div className="col">
                  Start Date
                  <input
                    type="date"
                    className="form-control"
                    onChange={(e) => {
                      setStartDate(e.target.value);
                    }}
                  />
                </div>

                <div className="col">
                  End Date
                  <input
                    type="date"
                    className="form-control"
                    placeholder=""
                    onChange={(e) => {
                      setEndDate(e.target.value);
                    }}
                    min={startDate}
                  />
                </div>
              </div>

              <div className="mt-3 d-flex flex-column flex-md-row justify-content-around gap-3">
                <div className="col">
                  Order Status
                  <select
                    className="form-select"
                    aria-label="Default select example"
                    onChange={(e) => {
                      setOrderStatus(e.target.value);
                    }}
                  >
                    <option selected> </option>
                    <option value="Pending">Pending</option>
                    <option value="Processing">Processing</option>
                    <option value="Completed">Completed</option>
                    <option value="Cancelled">Cancelled</option>
                  </select>
                </div>
                <div className="col">
                  Payment Status
                  <select
                    className="form-select"
                    aria-label="Default select example"
                    onChange={(e) => {
                      setPaymentStatus(e.target.value);
                    }}
                  >
                    <option selected> </option>
                    <option value="Pending">Pending</option>
                    <option value="Paid">Paid</option>
                    <option value="Cancelled">Cancelled</option>
                  </select>
                </div>
                <div className="col">
                  Shipping Status
                  <select
                    className="form-select"
                    aria-label="Default select example"
                    onChange={(e) => {
                      setShippingStatus(e.target.value);
                    }}
                  >
                    <option selected> </option>
                    <option value="NotYetShipped">NotYetShipped</option>
                    <option value="PartiallyShipped">PartiallyShipped</option>
                    <option value="Shipped">Shipped</option>
                    <option value="Delivered">Delivered</option>
                  </select>
                </div>
                <div className="col">
                  Order ID
                  <input
                    type="text"
                    className="form-control"
                    id="orderId"
                    placeholder=""
                    onChange={(e) => {
                      setId(e.target.value);
                    }}
                  />
                </div>
              </div>
              <div className="d-flex flex-wrap justify-content-center gap-3">
                <button
                  type="submit"
                  className="btn btn-primary mt-3"
                  onClick={handleSearch}
                >
                  <i className="bi bi-search"></i> Search
                </button>
                <button className="btn btn-primary mt-3" onClick={handleReset}>
                  <i className="bi bi-x-circle"></i> Reset
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default SearchOrder;
