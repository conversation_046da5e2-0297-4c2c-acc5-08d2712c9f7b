import { userAPI } from '@/APIs';
import { OrderResponseData, ShippingStatusTypes } from 'models';
import { useRouter } from 'next/router';
import { FC, useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import Modal from '../../service/modal';
import Tooltip from './Ui Helpers/tooltip';
import CreateShipmentModal from './shipmentModal/createShipmentModal';

interface Options {
  label: string;
  value: string;
}
interface Props {
  singleOrderInfo: OrderResponseData;
  setShippingStatusValue: Function;
  shippingStatusValue: string;
  orderStatusValue: string;
  paymentStatusValue: string;
  // shippingOptions: Options[];
  getSingleOrderData: Function;
}
const Shipping: FC<Props> = ({
  singleOrderInfo,
  shippingStatusValue,
  setShippingStatusValue,
  orderStatusValue,
  paymentStatusValue,
  getSingleOrderData,
  // shippingOptions,
}) => {
  const router = useRouter();
  const id = router.query.id as string;

  const [modal, setModal] = useState({
    change_shipping_status: false,
    change_shipping_status_save: false,
  });
  const [shippingState, setShippingState] = useState('delivered');
  const [status, setStatus] = useState(singleOrderInfo?.shippingStatus);
  const [created, setCreated] = useState(false);

  const handleShippingStatus = (event: any) => {
    setShippingState(event.target.value);
  };

  const [shippingEnum, setShippingEnum] = useState<ShippingStatusTypes>();

  const getAllShippingEnum = async () => {
    const res = await userAPI.getOrderEnum();
    if ('data' in res) {
      res ? setShippingEnum(res?.data?.shippingStatusEnum) : '';
    } else {
      toast.error(res.response.message);
    }
  };
  useEffect(() => {
    getAllShippingEnum();
  }, []);

  const handleShippingSaveStatus = () => {
    setModal({ ...modal, change_shipping_status_save: true });
  };

  const handleShippingSaveStatusOff = () => {
    setModal({ ...modal, change_shipping_status_save: false });
  };

  const handleShippingPositive = () => {
    setModal({
      ...modal,
      change_shipping_status_save: false,
      change_shipping_status: false,
    });
    const obj = {
      orderId: singleOrderInfo?.orderId,
      statusType: 'shippingStatusEnums',
      statusValue: shippingState,
    };
    setShippingStatusValue(shippingState);
    userAPI.updateShippingStatus(obj);
    getSingleOrderData();
  };

  const handlePaymentChange = () => {
    setModal({ ...modal, change_shipping_status: true });
  };

  const handleShipmentBuy = async () => {
    try {
      const res = await userAPI.buyShipment(singleOrderInfo.orderId);
      if ('data' in res) {
        toast.success('New Shipment Bought');
        setStatus('Bought');
        getSingleOrderData();
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  const handleShipmentLabelView = () => {
    router.push(`/Sales/Order/Edit/${id}/shipment-details`);
  };

  return (
    <div>
      <div
        className="modal fade container px-5"
        id="shipmentModal"
        tabIndex={-1}
        aria-labelledby="shipmentModalLabel"
        aria-hidden="true"
      >
        <CreateShipmentModal
          singleOrderInfo={singleOrderInfo}
          getSingleOrderData={getSingleOrderData}
          setCreated={setCreated}
        />
      </div>
      <div className="w-100 border fs-6 p-3 mb-3">
        <div className="d-flex flex-md-row flex-column gap-md-5 justify-content-around align-items-center">
          <p>Create Shipment</p>
          <button
            type="button"
            className="btn btn-danger"
            style={{
              backgroundColor: '#3c8dbc',
              border: '1px solid #3c8dbc',
            }}
            disabled={
              singleOrderInfo.paymentStatus !== 'Paid' ||
              status !== shippingEnum?.PRE_TRANSIT
            }
            data-bs-toggle="modal"
            data-bs-target="#shipmentModal"
          >
            Create
          </button>
          <p>Buy Shipment</p>
          <button
            type="button"
            className="btn btn-danger"
            style={{
              backgroundColor: '#3c8dbc',
              border: '1px solid #3c8dbc',
            }}
            onClick={handleShipmentBuy}
            disabled={
              singleOrderInfo.paymentStatus !== 'Paid' ||
              status !== shippingEnum?.PRE_TRANSIT ||
              created === false
            }
          >
            Buy
          </button>
          <p>View Details</p>
          <button
            type="button"
            className="btn btn-danger"
            style={{
              backgroundColor: '#3c8dbc',
              border: '1px solid #3c8dbc',
            }}
            onClick={handleShipmentLabelView}
            disabled={singleOrderInfo.paymentStatus !== 'Paid'}
          >
            View
          </button>
        </div>
      </div>
      <div className="w-100 border fs-6 p-3 mb-3">
        {/* <Tooltip
          label={'Shipping method'}
          data={singleOrderInfo?.shippingMethod}
        /> */}
        <div className="row">
          <Tooltip label={'Shipping status'} data={shippingStatusValue} />
          <div className="col" style={{}}>
            {modal.change_shipping_status ? (
              <div>
                <select
                  className="mt-2 w-auto p-2 border border-secondary text-left rounded"
                  onChange={(e: any) => {
                    handleShippingStatus(e);
                  }}
                  defaultValue={shippingState}
                >
                  {singleOrderInfo.paymentStatus === 'Paid' && (
                    <>
                      <option value={shippingEnum?.IN_TRANSIT}>
                        {shippingEnum?.IN_TRANSIT}
                      </option>
                      <option value={shippingEnum?.PRE_TRANSIT}>
                        {shippingEnum?.PRE_TRANSIT}
                      </option>
                      <option value={shippingEnum?.OUT_FOR_DELIVERY}>
                        {shippingEnum?.OUT_FOR_DELIVERY}
                      </option>
                      <option value={shippingEnum?.DELIVERED}>
                        {shippingEnum?.DELIVERED}
                      </option>
                      {/* {singleOrderInfo.orderStatus === 'Pending' && (
                        <>
                          {' '}
                          <option value={shippingEnum?.PRE_TRANSIT}>
                            {shippingEnum?.PRE_TRANSIT}
                          </option>
                        </>
                      )}
                      {singleOrderInfo.orderStatus === 'Processing' && (
                        <>
                          {' '}
                          <option value={shippingEnum?.OUT_FOR_DELIVERY}>
                            {shippingEnum?.OUT_FOR_DELIVERY}
                          </option>
                          <option value={shippingEnum?.DELIVERED}>
                            {shippingEnum?.DELIVERED}
                          </option>
                        </>
                      )}{' '} */}
                      {/* {singleOrderInfo.orderStatus === 'Cancelled' && (
                        <>
                          <option value={shippingEnum?.NotYetShipped}>
                            {shippingEnum?.NotYetShipped}
                          </option>
                        </>
                      )} */}
                    </>
                  )}
                  {/* {(singleOrderInfo.paymentStatus === 'Pending' ||
                    singleOrderInfo.paymentStatus === 'Cancelled') && (
                    <>
                      {' '}
                      <option value={shippingEnum?.NotYetShipped}>
                        {shippingEnum?.NotYetShipped}
                      </option>{' '}
                    </>
                  )} */}
                  {/* <option value={shippingEnum?.PartiallyShipped}>
                    {shippingEnum?.PartiallyShipped}
                  </option> */}

                  {/* {shippingOptions.map((option) => {
                    return (
                      <option key={option.label} value={option.value}>
                        {option.label}
                      </option>
                    );
                  })} */}
                </select>
                <span
                  style={{
                    textAlign: 'left',
                    marginTop: '10px',
                    width: '25%',
                    marginLeft: '5%',
                  }}
                >
                  <button
                    type="button"
                    className="btn btn-danger"
                    style={{
                      marginRight: '10px',
                      backgroundColor: '#3c8dbc',
                      border: '1px solid #3c8dbc',
                    }}
                    onClick={() => handleShippingSaveStatus()}
                  >
                    Save
                  </button>
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={() =>
                      setModal({
                        ...modal,
                        change_shipping_status: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  {modal.change_shipping_status_save ? (
                    <Modal
                      state={'change_shipping_status_save'}
                      handleStatus={handleShippingSaveStatusOff}
                      handlePositive={handleShippingPositive}
                    />
                  ) : (
                    <></>
                  )}
                </span>
              </div>
            ) : (
              <button
                type="button"
                className="btn btn-danger"
                style={{
                  backgroundColor: '#3c8dbc',
                  border: '1px solid #3c8dbc',
                }}
                disabled={
                  singleOrderInfo.shippingStatus === 'Cancelled' ||
                  singleOrderInfo.paymentStatus === 'Pending' ||
                  singleOrderInfo.orderStatus === 'Completed'
                }
                onClick={() => handlePaymentChange()}
              >
                Change status
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Shipping;
