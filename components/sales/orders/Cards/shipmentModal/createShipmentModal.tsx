import { userAPI } from '@/APIs';
import { shipmentSchema } from '@/components/sales/service/schema/shipment.schema';
import { Form, Formik } from 'formik';
import { OrderResponseData } from 'models';
import { FC } from 'react';
import { toast } from 'react-toastify';

interface Props {
  singleOrderInfo: OrderResponseData;
  getSingleOrderData: Function;
  setCreated: Function;
}

const CreateShipmentModal: FC<Props> = ({
  singleOrderInfo,
  getSingleOrderData,
  setCreated,
}) => {
  const handleShipmentCreation = async (data: any) => {
    try {
      // let obj = {};
      // if (data.length > 0) {
      //   obj = { ...obj, length: data.length };
      // }
      // if (data.width > 0) {
      //   obj = { ...obj, width: data.width };
      // }
      // if (data.weight > 0) {
      //   obj = { ...obj, weight: data.weight };
      // }
      // if (data.height > 0) {
      //   obj = { ...obj, height: data.height };
      // }
      const res = await userAPI.createShipment(singleOrderInfo.orderId);
      if ('data' in res) {
        toast.success('New Shipment Request Created');
        setCreated(true);
        getSingleOrderData();
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          length: 0,
          width: 0,
          height: 0,
          weight: 0,
        }}
        onSubmit={(values, actions) => {
          handleShipmentCreation(values);
          actions.setSubmitting(false);
          actions.resetForm();
        }}
        validationSchema={shipmentSchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div className="modal-dialog">
                <div className="modal-content">
                  <div className="modal-header">
                    <h5 className="modal-title" id="shipmentModalLabel">
                      Create Shipment
                    </h5>
                    <button
                      type="button"
                      className="btn-close"
                      data-bs-dismiss="modal"
                      aria-label="Close"
                    ></button>
                  </div>
                  <div className="modal-body">
                    <h6>Order Id: {singleOrderInfo.orderId} </h6>
                    <hr />
                    {/* <h6>Parcel Details</h6>
                    <hr />
                    <ShipmentCreateForm /> */}
                  </div>
                  <div className="modal-footer">
                    <button
                      type="button"
                      className="btn btn-secondary"
                      data-bs-dismiss="modal"
                    >
                      Close
                    </button>
                    <button
                      type="submit"
                      className="btn btn-primary"
                      data-bs-dismiss="modal"
                    >
                      Submit
                    </button>
                  </div>
                </div>
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default CreateShipmentModal;
