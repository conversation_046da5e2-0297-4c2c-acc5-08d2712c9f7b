import FieldTemplate from '@/components/common/fieldTemplate';

const ShipmentCreateForm = () => {
  return (
    <>
      <FieldTemplate
        label="Length"
        isRequired={false}
        fieldID="length"
        fieldType="number"
        placeholder="Length in cm"
      />
      <FieldTemplate
        label="Width"
        isRequired={false}
        fieldID="width"
        fieldType="number"
        placeholder="Width in cm"
      />
      <FieldTemplate
        label="Height"
        isRequired={false}
        fieldID="height"
        fieldType="number"
        placeholder="Height in cm"
      />
      <FieldTemplate
        label="Weight"
        isRequired={false}
        fieldID="weight"
        fieldType="number"
        placeholder="Weight in kg"
      />
    </>
  );
};

export default ShipmentCreateForm;
