import Table from '@/components/sales/orders/Cards/Ui Helpers/table';
import { OrderResponseData } from 'models';
import { useRouter } from 'next/router';
import { FC } from 'react';
interface Props {
  singleOrderInfo: OrderResponseData;
}
const Billing: FC<Props> = ({ singleOrderInfo }) => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;

  return (
    <>
      <p className="fs-4 fw-semibold">Billing & Shipping Info</p>
      <div className="d-flex flex-column flex-md-row gap-3 justify-content-center">
        <Table data={singleOrderInfo.billingAddress} label="Billing Address" />
        <Table
          data={singleOrderInfo.shippingAddress}
          label="Shipping Address"
        />
      </div>
    </>
  );
};

export default Billing;
