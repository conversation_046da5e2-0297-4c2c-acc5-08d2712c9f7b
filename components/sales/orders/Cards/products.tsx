import Table from '@/components/global/table/table';
import myImageLoader from 'image/loader';
import Image from 'next/image';
import { FC } from 'react';
interface Props {
  singleOrderInfo: any;
}
const Products: FC<Props> = ({ singleOrderInfo }) => {
  const products = singleOrderInfo?.products;

  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const columns = [
    {
      label: 'Picture',
      path: 'photo',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.[key] ? (
            <Image
              loader={myImageLoader}
              src={data && data?.[key] && data?.[key]}
              alt="Photo"
              height={100}
              width={100}
            />
          ) : (
            'No Image Found'
          )}
        </td>
      ),
    },
    {
      label: 'Product Name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.[key]}</td>
      ),
    },
    {
      label: 'Size',
      path: 'size',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.[key]}</td>
      ),
    },
    {
      label: 'Color',
      path: 'color',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <button
            className={'p-2 m-2 rounded-circle border border-none'}
            style={{
              backgroundColor: data?.[key],
            }}
          ></button>
        </td>
      ),
    },
    {
      label: 'Price',
      path: 'price',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.[key].toFixed(2)}</td>
      ),
    },
    {
      label: 'Quantity',
      path: 'quantity',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.[key]}</td>
      ),
    },

    {
      label: 'Total',
      path: 'total',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {(data?.quantity * data?.price).toFixed(2)}
        </td>
      ),
    },
  ];

  return (
    <div>
      <p className="fs-4 fw-semibold">Ordered Products</p>
      <Table
        items={products}
        columns={columns}
        onClickForSort={onClickForSort}
      />
    </div>
  );
};

export default Products;
