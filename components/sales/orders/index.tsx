import moment from 'moment';
import { useEffect, useState } from 'react';
import { userAPI } from '../../../APIs';
import OrderList from './List/ordersList';
import { config } from 'config';
import {
  OrderStatusTypes,
  PaymentStatusTypes,
  ShippingStatusTypes,
} from 'models';
import { toast } from 'react-toastify';

interface Options {
  label: string;
  value: string;
}

const OrderListMain = () => {
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [orderStatus, setOrderStatus] = useState('');
  const [paymentStatus, setPaymentStatus] = useState('');
  const [shippingStatus, setShippingStatus] = useState('');
  const [id, setId] = useState('');
  const [orderListData, setOrderListData] = useState<any>([]);
  const [btnToggler, setBtnToggler] = useState('bi-plus-lg');
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  const [disableNext, setDisableNext] = useState(false);

  const [orderEnum, setOrderEnum] = useState<OrderStatusTypes>();
  const [paymentEnum, setPaymentEnum] = useState<PaymentStatusTypes>();
  const [shippingEnum, setShippingEnum] = useState<ShippingStatusTypes>();

  const [orderOptions, setOrderOptions] = useState<Options[]>([]);
  const [paymentOptions, setPaymentOptions] = useState<Options[]>([]);
  const [shippingOptions, setShippingOptions] = useState<Options[]>([]);

  const getAllEnum = async () => {
    const res = await userAPI.getOrderEnum();
    if ('data' in res!) {
      setOrderEnum(res?.data?.orderStatusEnums);
      setPaymentEnum(res?.data?.paymentStatusEnums);
      setShippingEnum(res?.data?.shippingStatusEnum);
    } else {
      toast.error(res.response.message);
    }
  };

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  const getAllOrderList = async () => {
    const res = await userAPI.getAllOrderList(
      orderStatus,
      paymentStatus,
      shippingStatus,
      startDate,
      endDate,
      skip,
      limit
    );
    if ('data' in res) {
      //const newArray = products.concat(productsList.data);
      if (res.data.orders.length === 0) {
        setDisableNext(true);
      } else {
        setDisableNext(false);
        setOrderListData(res.data.orders);
      }
    }
  };

  const handleSearch = (event: any) => {
    event.preventDefault();
    setSkip(0);
    if (id) {
      const singleOrderList = orderListData?.orders?.filter(
        (order: any) => order.orderId === id
      );
      const order = {
        orders: singleOrderList,
      };
      setOrderListData(order);
    } else {
      setStartDate(moment(startDate).toJSON());
      setEndDate(moment(endDate).toJSON());
      getAllOrderList();
    }
  };

  const handleReset = () => {
    setOrderStatus('');
    setPaymentStatus('');
    setShippingStatus('');
    setStartDate('');
    setEndDate('');
    getAllOrderList();
  };

  useEffect(() => {
    getAllOrderList();
  }, [skip]);

  useEffect(() => {
    getAllEnum();
  }, []);

  return (
    <>
      <main className="px-5">
        <div
          className="d-flex justify-content-between flex-md-nowrap align-items-center border-bottom mb-3 flex-wrap pt-3 pb-2"
          style={{ paddingLeft: '10px' }}
        >
          <h1 className="h2 fs-2">Orders</h1>
        </div>
        <div>
          <div
            className="card card-secondary card-outline my-4"
            data-card-name="order-info"
            data-hideattribute="ProductPage.HideInfoBlock"
            id="order-info"
          >
            <button
              className="btn w-100 text-top invisible m-0 h-auto p-0"
              type="button"
              data-bs-toggle="collapse"
              data-bs-target="#orderInfoTab"
              aria-expanded="true"
              aria-controls="orderInfoTab"
              onClick={() => toggleButton()}
            >
              <div className="card-title mx-2 mt-2 row align-items-center visible">
                <div className="fs-5 col px-3 text-start">Search</div>
                <div className="col-1">
                  <i className={`bi ${btnToggler}`} />
                </div>
              </div>
            </button>
            <div className="collapse" id="orderInfoTab">
              <form className="m-3">
                <div className="d-flex flex-column flex-md-row justify-content-around gap-5">
                  <div className="col">
                    Start Date
                    <input
                      type="date"
                      className="form-control"
                      onChange={(e) => {
                        setStartDate(e.target.value);
                      }}
                    />
                  </div>

                  <div className="col">
                    End Date
                    <input
                      type="date"
                      className="form-control"
                      placeholder=""
                      onChange={(e) => {
                        setEndDate(e.target.value);
                      }}
                      min={startDate}
                    />
                  </div>

                  {/* <div className="col">
                    Order ID
                    <input
                      type="text"
                      className="form-control"
                      id="orderId"
                      placeholder=""
                      onChange={(e) => {
                        setId(e.target.value);
                      }}
                    />
                  </div> */}
                </div>

                <div className="mt-3 d-flex flex-column flex-md-row justify-content-around gap-5">
                  <div className="col">
                    Order Status
                    <select
                      className="form-select"
                      aria-label="Default select example"
                      onChange={(e) => {
                        setOrderStatus(e.target.value);
                      }}
                    >
                      <option>Select Status</option>
                      <option value={orderEnum?.Pending}>
                        {orderEnum?.Pending}
                      </option>
                      <option value={orderEnum?.Processing}>
                        {orderEnum?.Processing}
                      </option>
                      <option value={orderEnum?.Completed}>
                        {orderEnum?.Completed}
                      </option>
                      <option value={orderEnum?.Cancelled}>
                        {orderEnum?.Cancelled}
                      </option>
                    </select>
                  </div>
                  <div
                    // style={{
                    //   padding: '5px',
                    //   marginRight: '100px',
                    //   placeContent: 'Cancelled',
                    // }}
                    className="col"
                  >
                    Payment Status
                    <select
                      className="form-select"
                      aria-label="Default select example"
                      placeholder="Pending 111"
                      onChange={(e) => {
                        setPaymentStatus(e.target.value);
                      }}
                    >
                      <option>Select Status</option>
                      <option value={paymentEnum?.Pending}>
                        {paymentEnum?.Pending}
                      </option>
                      <option value={paymentEnum?.Paid}>
                        {paymentEnum?.Paid}
                      </option>
                      <option value={paymentEnum?.Cancelled}>
                        {paymentEnum?.Cancelled}
                      </option>
                    </select>
                  </div>
                  <div className="col">
                    Shipping Status
                    <select
                      className="form-select"
                      aria-label="Default select example"
                      placeholder=""
                      onChange={(e) => {
                        setShippingStatus(e.target.value);
                      }}
                    >
                      <option>Select Status</option>
                      <option value={shippingEnum?.PRE_TRANSIT}>
                        {shippingEnum?.PRE_TRANSIT}
                      </option>
                      <option value={shippingEnum?.IN_TRANSIT}>
                        {shippingEnum?.IN_TRANSIT}
                      </option>
                      <option value={shippingEnum?.OUT_FOR_DELIVERY}>
                        {shippingEnum?.OUT_FOR_DELIVERY}
                      </option>
                      <option value={shippingEnum?.DELIVERED}>
                        {shippingEnum?.DELIVERED}
                      </option>
                    </select>
                  </div>
                  {/* <div className="col">
                    Order ID
                    <input
                      type="text"
                      className="form-control"
                      id="orderId"
                      placeholder=""
                      onChange={(e) => {
                        setId(e.target.value);
                      }}
                    />
                  </div> */}
                </div>
                <div className="d-flex flex-wrap justify-content-center gap-3">
                  <button
                    type="submit"
                    className="btn btn-primary my-3"
                    onClick={handleSearch}
                  >
                    <i className="bi bi-search"></i> Search
                  </button>
                  <button
                    className="btn btn-primary my-3"
                    onClick={handleReset}
                  >
                    <i className="bi bi-x-circle"></i> Reset
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <div className="card border-1 mt-3 rounded px-2">
          <div className="card-body">
            <div className="d-flex justify-content-between">
              <div className="d-flex justify-content-start">
                {/* <p>
                  Learn more about
                  <a
                    href="#"
                    style={{
                      textDecoration: 'none',
                      marginLeft: '5px',
                    }}
                  >
                    Orders
                  </a>
                </p> */}
              </div>
            </div>

            {orderListData ? (
              <OrderList orderListData={orderListData} />
            ) : (
              'No Order Data Found'
            )}
            <div className="d-flex justify-content-between items-center mt-3">
              <button
                className="btn btn-outline-primary"
                disabled={skip === 0}
                onClick={() => {
                  let newSkip;
                  if (disableNext) newSkip = skip - 2 * limit;
                  else newSkip = skip - limit;
                  setSkip(newSkip);
                }}
              >
                Previous
              </button>
              <button
                className="btn btn-outline-primary"
                disabled={disableNext}
                onClick={() => {
                  const newSkip = skip + limit;
                  setSkip(newSkip);
                }}
              >
                Next
              </button>
            </div>
          </div>
        </div>
      </main>
    </>
  );
};

export default OrderListMain;
