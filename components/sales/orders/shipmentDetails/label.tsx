import { saveAs } from 'file-saver';
import myImageLoader from 'image/loader';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { FC, useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

interface Props {
  shipmentDetails: any;
}

const LabelComponent: FC<Props> = ({ shipmentDetails }) => {
  const router = useRouter();
  const id = router.query.id as string;
  const componentRef = useRef<HTMLDivElement>(null);

  const handleClick = () => {
    let url = shipmentDetails.postage_label.label_url;
    saveAs(url, `label-${id}`);
  };

  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
  });

  return (
    <div className="px-lg-4 px-0">
      <h6>Label</h6>
      <div className="d-flex flex-column gap-3 align-items-center justify-content-center">
        <div className="d-flex justify-content-center gap-5">
          <div>
            <button
              className="btn text-white"
              style={{
                background: '#3c8dbc',
              }}
              onClick={handleClick}
            >
              Download <i className="bi bi-file-earmark-arrow-down"></i>
            </button>
          </div>
          <button className="btn btn-secondary" onClick={handlePrint}>
            Print <i className="bi bi-printer"></i>
          </button>
        </div>
        <div ref={componentRef}>
          <Image
            src={shipmentDetails?.postage_label?.label_url}
            height={300}
            width={300}
            alt="image"
            quality={100}
            loader={myImageLoader}
            className="mb-5"
          />
        </div>
      </div>
    </div>
  );
};

export default LabelComponent;
