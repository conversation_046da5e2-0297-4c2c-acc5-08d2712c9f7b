import moment from 'moment';
import { FC } from 'react';

interface Props {
  shipmentDetails: any;
}

const ParcelDetails: FC<Props> = ({ shipmentDetails }) => {
  return (
    <div>
      <h6>Parcel Details</h6>
      <table className="table table-bordered">
        <tbody>
          <tr className="text-center">
            <td>Height</td>
            <td>{shipmentDetails?.parcel?.height}</td>
          </tr>
          <tr className="text-center ">
            <td>Width</td>
            <td>{shipmentDetails?.parcel?.width}</td>
          </tr>
          <tr className="text-center ">
            <td>Length</td>
            <td>{shipmentDetails?.parcel?.length}</td>
          </tr>
          <tr className="text-center ">
            <td>Weight</td>
            <td>{shipmentDetails?.parcel?.weight}</td>
          </tr>
          <tr className="text-center ">
            <td>Created At</td>
            <td>
              {moment(shipmentDetails?.parcel?.createdAt)
                .utc()
                .local()
                .format('lll')}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default ParcelDetails;
