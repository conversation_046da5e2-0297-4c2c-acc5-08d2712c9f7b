import { FC } from 'react';
import ParcelDetails from './parcelDetails';
import TrackingDetails from './trackingDetails';

interface Props {
  shipmentDetails: any;
}

const Details: FC<Props> = ({ shipmentDetails }) => {
  return (
    <>
      <ParcelDetails shipmentDetails={shipmentDetails} />
      {/* <FeeTable shipmentDetails={shipmentDetails} /> */}
      <TrackingDetails shipmentDetails={shipmentDetails} />
    </>
  );
};

export default Details;
