import { FC } from 'react';

interface Props {
  shipmentDetails: any;
}
const FeeTable: FC<Props> = ({ shipmentDetails }) => {
  return (
    <div className="overflow-auto">
      <h6>Fees</h6>
      <table className="table table-bordered ">
        <thead>
          <tr className="text-center">
            <th scope="col">Type</th>
            <th scope="col">Amount</th>
            <th scope="col">Charged</th>
            <th scope="col">Refunded</th>
          </tr>
        </thead>
        <tbody>
          {shipmentDetails?.fees.map((fee: any, index: number) => {
            return (
              <tr className="text-center" key={index}>
                <td>{fee.type}</td>
                <td>{parseFloat(fee.amount).toFixed(2)} USD</td>
                <td>
                  {fee.charged ? (
                    <i className="bi bi-check2"></i>
                  ) : (
                    <i className="bi bi-x"></i>
                  )}
                </td>
                <td>
                  {fee.refunded ? (
                    <i className="bi bi-check2"></i>
                  ) : (
                    <i className="bi bi-x"></i>
                  )}
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default FeeTable;
