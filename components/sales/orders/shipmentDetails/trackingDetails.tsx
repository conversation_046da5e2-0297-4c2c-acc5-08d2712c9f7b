import moment from 'moment';
import { FC } from 'react';

interface Props {
  shipmentDetails: any;
}

const TrackingDetails: FC<Props> = ({ shipmentDetails }) => {
  const handleTracking = () => {
    window.open(shipmentDetails?.tracker?.public_url);
  };
  return (
    <div>
      <h6>Tracking Details</h6>
      <table className="table table-bordered">
        <tbody>
          <tr className="text-center">
            <td>To Track</td>
            <td>
              <p
                className="cursor-pointer"
                onClick={handleTracking}
                style={{
                  cursor: 'pointer',
                  color: '#3c8dbc',
                }}
              >
                click here
              </p>
            </td>
          </tr>
          <tr className="text-center ">
            <td>Status</td>
            <td>{shipmentDetails?.tracker?.status}</td>
          </tr>

          <tr className="text-center ">
            <td>Estimated Delivery At</td>
            <td>
              {moment(shipmentDetails?.tracker?.est_delivery_date)
                .utc()
                .local()
                .format('lll')}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default TrackingDetails;
