import moment from 'moment';
import { FC } from 'react';
import Table from '../global/table/table';
import { ISubscriber } from 'models';
import { config } from 'config';
import { handlePagination } from 'utils/handlePagination';

interface Props {
  mails: ISubscriber[];
  setMails: Function;
  setSkip: Function;
  skip: number;
  // showSeeMore: boolean;
  // setLoadData: Function;
  // loadData: boolean;
}
const List: FC<Props> = ({
  mails,
  setMails,
  setSkip,
  skip,
  // showSeeMore,
  // setLoadData,
  // loadData,
}) => {
  const columns = [
    {
      label: 'Emails',
      path: 'email',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          {data?.email}
        </td>
      ),
    },
    {
      label: 'Date',
      path: 'createdAt',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          {moment(data?.createdAt).utc().local().format('llll')}
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          {mails.length > 0 && (
            <>
              <Table items={mails} columns={columns} />
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default List;
