import { Tag } from 'models';
import Link from 'next/link';
import { FC } from 'react';
import Table from '../../global/table/table';
interface Props {
  TagData: Tag[];
}
const AllTagLists: FC<Props> = ({ TagData }) => {
  const columns = [
    {
      label: 'Tag Name',
      path: 'displayOrder',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">{data?.name}</td>
      ),
    },
    {
      label: 'View',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          <Link
            href={{
              pathname: `/tags/view/[id]`,
              query: { id: data?.[key] },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          {TagData.length > 0 && <Table items={TagData} columns={columns} />}
        </div>
      </div>
    </>
  );
};

export default AllTagLists;
