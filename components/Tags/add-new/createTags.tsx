import { Form, Formik } from 'formik';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC } from 'react';
import { userAPI } from '../../../APIs';
import { tagSchema } from '../schema/tags.Schema';
import TagInfo from './forms/tagInfo';
import { toast } from 'react-toastify';

const CreateTag: FC = () => {
  const router = useRouter();
  const handleSubmit = async (data: any) => {
    try {
      const res = await userAPI.createTags(data, router);
      if ('data' in res) {
        router.push('/tags');
        toast.success('Tag Created Successfully');
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          name: '',
          isHomePageProductsTag: false,
        }}
        onSubmit={(values, actions) => {
          handleSubmit(values);
          actions.setSubmitting(false);
        }}
        validationSchema={tagSchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix"
                style={{ paddingTop: '19px' }}
              >
                <h1 className="float-start fs-2">
                  Add a Tag
                  <span className="fs-5 p-3">
                    <Link href="/tags/" className="text-decoration-none">
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      <span style={{ fontSize: '14px' }}>Back to Tag list</span>
                    </Link>
                  </span>
                </h1>
                <div className="float-end">
                  <button
                    type="submit"
                    name="save"
                    className="btn btn-primary m-1"
                  >
                    {/* <i className="bi bi-save" /> */}
                    <p className="float-end mx-1 my-0">Save</p>
                  </button>
                </div>
              </div>
              <div className="mt-4">
                <TagInfo />
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default CreateTag;
