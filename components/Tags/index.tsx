import Link from 'next/link';
import { useEffect, useState } from 'react';
import { userAPI } from '../../APIs/index';
import AllTagLists from './Lists/tagLists';
import { toast } from 'react-toastify';

const Tags = () => {
  const [TagData, setAllTagData] = useState<any>();
  const getAllTags = async () => {
    try {
      const res = await userAPI.getAllTags();
      if ('data' in res) setAllTagData(res.data);
      // else {
      //   toast.error(res?.error.message!);
      // }
    } catch (error) {}
  };
  useEffect(() => {
    getAllTags();
  }, []);
  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Tags</div>
          <Link href={'/tags/add-new'} className="btn btn-primary">
            Add new
          </Link>
        </div>
        <div>
          {TagData ? <AllTagLists TagData={TagData} /> : 'No Tags data found'}
        </div>
      </main>
    </>
  );
};

export default Tags;
