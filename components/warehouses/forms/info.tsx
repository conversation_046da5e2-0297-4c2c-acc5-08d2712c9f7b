import CustomSelect from '@/components/common/customSelectInput';
import FieldTemplate from '@/components/common/fieldTemplate';
import { FC, useState } from 'react';

interface Options {
  label: string;
  value: string | boolean;
}

interface Props {
  setFieldValue?: Function;
  statusOptions: Options[];
  edit?: boolean;
}

const Info: FC<Props> = ({ setFieldValue, edit, statusOptions }) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="warehouse-info"
        data-hideattribute="ProductPage.HideInfoBlock"
        id="warehouse-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#warehouseInfoTab"
            aria-expanded="true"
            aria-controls="warehouseInfoTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-info-lg col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col px-3 text-start">Warehouse Info</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="warehouseInfoTab">
          <div className="card-body">
            <FieldTemplate
              label="Name"
              isRequired={!edit ? true : false}
              fieldID="name"
              fieldType="text"
            />

            <FieldTemplate
              label="Title"
              isRequired={!edit ? true : false}
              fieldID="title"
              fieldType="text"
            />

            <FieldTemplate
              label="Owner"
              isRequired={!edit ? true : false}
              fieldID="owner"
              fieldType="text"
            />

            <FieldTemplate
              label="Email"
              isRequired={!edit ? true : false}
              fieldID="email"
              fieldType="text"
            />

            <FieldTemplate label="Phone" fieldID="phone" fieldType="text" />

            <FieldTemplate
              label="Address Line 1"
              isRequired={!edit ? true : false}
              fieldID="address1"
              fieldType="text"
            />

            <FieldTemplate
              label="Address Line 2"
              fieldID="address 2"
              fieldType="text"
            />

            <FieldTemplate
              label="City"
              isRequired={!edit ? true : false}
              fieldID="city"
              fieldType="city"
            />

            <FieldTemplate
              label="State"
              isRequired={!edit ? true : false}
              fieldID="state"
              fieldType="text"
            />

            <FieldTemplate
              label="Post Code"
              isRequired={!edit ? true : false}
              fieldID="postCode"
              fieldType="number"
            />

            <FieldTemplate
              label="Status"
              isRequired={!edit ? true : false}
              fieldID="status"
              fieldType="none"
              fieldClass="custom-select w-100"
              options={statusOptions}
              component={CustomSelect}
              placeholder="Select status..."
              ismulti={false}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default Info;
