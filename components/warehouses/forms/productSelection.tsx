import { userAPI } from '@/APIs';
import { config } from 'config';
import { ErrorMessage } from 'formik';
import { Product } from 'models';
import { FC, useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import ProductTable from './productTable';

interface Props {
  selectedProducts: Product[];
  setSelectedProducts: Function;
}

const ProductSelectionForm: FC<Props> = ({
  selectedProducts,
  setSelectedProducts,
}) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');
  const [products, setProducts] = useState<Product[]>([]);
  const [file, setFile] = useState<any>();
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  const [showSeeMore, setShowSeeMore] = useState(true);
  const [loadData, setLoadData] = useState(false);

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  const getAllProducts = async () => {
    try {
      const res = await userAPI.getProducts(skip, config.limit);
      if ('data' in res) {
        const newArray = products.concat(res.data);
        setProducts(newArray);
        if (res.data.length < limit) {
          setShowSeeMore(false);
        } else {
          setShowSeeMore(true);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getAllProducts();
  }, [loadData]);

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="warehouse-products-info"
        data-hideattribute="ProductPage.HideInfoBlock"
        id="warehouse-products-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#warehouseProductsInfoTab"
            aria-expanded="true"
            aria-controls="warehouseProductsInfoTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-info-lg col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col px-3 text-start">Products List</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="warehouseProductsInfoTab">
          <div className="card-body">
            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                  <label
                    className="col-form-label col fs-5 px-1 text-center"
                    htmlFor="exerciseID"
                  >
                    Select Product
                    <span className="required text-danger ">*</span>
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                {' '}
                <ProductTable
                  productList={products}
                  selectedProducts={selectedProducts}
                  setSelectedProducts={setSelectedProducts}
                  setSkip={setSkip}
                  skip={skip}
                  showSeeMore={showSeeMore}
                  setLoadData={setLoadData}
                  loadData={loadData}
                />
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="productId" />
                </div>
                <div className="float-end mt-3">
                  <button
                    type="submit"
                    name="save"
                    className="btn btn-primary m-1"
                  >
                    {/* <i className="bi bi-save" /> */}
                    <p className="float-end mx-1 my-0">Next</p>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProductSelectionForm;
