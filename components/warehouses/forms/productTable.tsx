import { FC } from 'react';

import Table from '@/components/global/table/table';
import { config } from 'config';
import { Field } from 'formik';
import myImageLoader from 'image/loader';
import { Product } from 'models';
import Image from 'next/image';
import { handlePagination } from 'utils/handlePagination';

interface Props {
  productList: Product[];
  selectedProducts: Product[];
  setSelectedProducts: Function;
  setSkip: Function;
  skip: number;
  showSeeMore: boolean;
  setLoadData: Function;
  loadData: boolean;
}

const ProductTable: FC<Props> = ({
  productList,
  selectedProducts,
  setSelectedProducts,
  setSkip,
  skip,
  showSeeMore,
  setLoadData,
  loadData,
}) => {
  // const [currentPage, setCurrentPage] = useState(1);
  // const [PageSize, setPageSize] = useState(7);

  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  // const currentTableData = useMemo(() => {
  //   const firstPageIndex = (currentPage - 1) * PageSize;
  //   const lastPageIndex = firstPageIndex + PageSize;
  //   return productList?.slice(firstPageIndex, lastPageIndex);
  // }, [currentPage, PageSize, productList]);

  const columns = [
    {
      label: 'Select',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Field
            type="checkbox"
            name="products"
            id="products"
            value={data[key]}
            onClick={(e: any) => {
              if (e.target.checked) {
                const list = new Set(selectedProducts);
                list.add(data);
                console.log(list);
                setSelectedProducts(Array.from(list));
              } else {
                const list = new Set(selectedProducts);
                list.delete(data);
                setSelectedProducts(Array.from(list));
              }
            }}
          />
        </td>
      ),
    },
    {
      label: 'Picture',
      path: 'url',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.photos![0][key] && (
            <Image
              loader={myImageLoader}
              src={`${data?.photos![0][key]}`}
              height={75}
              width={75}
              alt="..."
            />
          )}
        </td>
      ),
    },
    {
      label: 'Product name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.info[key]}</td>
      ),
    },
    {
      label: 'Price',
      path: 'price',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.info[key]}</td>
      ),
    },
    {
      label: 'Categories',
      path: 'categories',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.info[key]}
          {data?.categories[0] ? data?.categories[0].name : '---'}
          {data?.categories?.map((category: any, index: any) =>
            index > 0 ? ` , ${category?.name}` : ''
          )}
        </td>
      ),
    },
    // {
    //   label: 'View',
    //   path: 'id',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="text-center align-middle">
    //       <Link
    //         href={{
    //           pathname: `/Product/View/[id]`,
    //           query: { id: data?.[key] },
    //         }}
    //         passHref
    //         legacyBehavior
    //       >
    //         <button className="btn btn-default btn-outline-primary">
    //           <span>
    //             <i className="bi bi-eye me-2 align-middle"></i>
    //           </span>
    //           View
    //         </button>
    //       </Link>
    //     </td>
    //   ),
    // },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={productList}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>
    </>
  );
};

export default ProductTable;
