import Link from 'next/link';
import Products from './cards/products';
import WarehouseInfoCard from './cards/warehouseInfoCard';
// import SubscriptionInfoCard from './cards/subscriptionInfoCard';

interface Props {
  warehouse: any;
}

const ViewWarehouse: React.FC<Props> = ({ warehouse }) => {
  return (
    <>
      {warehouse ? (
        <div>
          <div className="content-header clearfix mt-3">
            <h1 className="float-start fs-2">
              View warehouse details
              <span className="fs-5 p-3">
                <Link href="/warehouses" className="text-decoration-none ">
                  <i className="bi bi-arrow-left-circle-fill p-2" />
                  Back to warehouse list
                </Link>
              </span>
            </h1>
          </div>

          <div className="mt-4">
            <WarehouseInfoCard warehouse={warehouse} />
            <Products warehouse={warehouse} />
          </div>
        </div>
      ) : (
        ''
      )}
    </>
  );
};

export default ViewWarehouse;
