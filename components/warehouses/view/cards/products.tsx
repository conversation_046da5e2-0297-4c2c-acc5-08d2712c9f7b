import Table from '@/components/global/table/table';
import myImageLoader from 'image/loader';
import Image from 'next/image';
import { FC } from 'react';
interface Props {
  warehouse: any;
}
const Products: FC<Props> = ({ warehouse }) => {
  const products = warehouse?.stock;

  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const columns = [
    {
      label: 'Picture',
      path: 'photos',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.product?.[key] ? (
            <Image
              loader={myImageLoader}
              src={data && data?.product?.[key]![0].url}
              alt="Photo"
              height={100}
              width={100}
            />
          ) : (
            'No Image Found'
          )}
        </td>
      ),
    },
    {
      label: 'Product Name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.product?.info?.[key]}
        </td>
      ),
    },
    {
      label: 'SKU',
      path: 'sku',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.product?.info?.[key]}
        </td>
      ),
    },
    {
      label: 'Price',
      path: 'price',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.product?.info?.[key]}
        </td>
      ),
    },
    {
      label: 'Published',
      path: 'published',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.product?.info?.[key] ? (
            <i className="bi bi-check-lg"></i>
          ) : (
            'X'
          )}
        </td>
      ),
    },
    {
      label: 'Stock Quantity',
      path: 'quantity',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.[key]}</td>
      ),
    },
  ];

  return (
    <div>
      <p className="fs-4 fw-semibold">Stock Details</p>
      <Table
        items={products}
        columns={columns}
        onClickForSort={onClickForSort}
      />
    </div>
  );
};

export default Products;
