import { FC } from 'react';

import SingleView from '@/components/common/singleView';

interface Props {
  warehouse: any;
}

const WarehouseInfoCard: FC<Props> = ({ warehouse }) => {
  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="warehouse-info"
        id="warehouse-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <div className="card-title row align-items-center ps-2 pt-2">
            <i
              className="bi bi-info-lg col-1 align-text-top"
              style={{ fontSize: '25px' }}
            />
            <div className="fs-5 col text-start px-3">Warehouse Info</div>
          </div>
        </div>
        <div className="" id="warehouseInfoTab">
          <div className="card-body">
            <SingleView label="Name" value={warehouse.name} />
            <SingleView label="Title" value={warehouse.title} />
            <SingleView label="Owner" value={warehouse.owner} />
            {/* <SingleView label="Type" value={exercise.type} /> */}
            <SingleView label="Email" value={warehouse.email} />
            <SingleView label="Phone" value={warehouse.phone} />
            <SingleView label="Address 1" value={warehouse.address1} />
            <SingleView label="Address 2" value={warehouse.address2} />
            <SingleView label="City" value={warehouse.city} />
            <SingleView label="State" value={warehouse.state} />
            <SingleView label="Postal Code" value={warehouse.postCode} />
            <SingleView label="Status" value={warehouse.status} />
          </div>
        </div>
      </div>
    </>
  );
};

export default WarehouseInfoCard;
