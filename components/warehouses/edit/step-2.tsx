import Table from '@/components/global/table/table';
import { useAppSelector } from '@/redux-hooks';
import myImageLoader from 'image/loader';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { FC, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';

interface Options {
  label: string;
  value: string | boolean;
}

interface IStock {
  productId: string;
  quantity: number;
}

interface Props {
  warehouseToEdit: any;
}

const EditWarehouseStep2: FC<Props> = ({ warehouseToEdit }) => {
  const router = useRouter();
  const id = router.query.id;
  const dispatch = useDispatch();
  const warehouse = useAppSelector(
    (state) => state.persistedReducer.warehouse.warehouse
  );

  const [stock, setStock] = useState<IStock[]>([]);
  const ref = useRef(null);

  const columns = [
    {
      label: 'Picture',
      path: 'url',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.photos![0][key] && (
            <Image
              loader={myImageLoader}
              src={`${data?.photos![0][key]}`}
              height={75}
              width={75}
              alt="..."
            />
          )}
        </td>
      ),
    },
    {
      label: 'Product name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.info[key]}</td>
      ),
    },
    {
      label: 'SKU',
      path: 'sku',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.info[key]}</td>
      ),
    },
    {
      label: 'Price',
      path: 'price',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.info[key]}</td>
      ),
    },
    {
      label: 'Categories',
      path: 'categories',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.categories[0] ? data?.categories[0].name : '---'}
          {data?.categories?.map((category: any, index: any) =>
            index > 0 ? ` , ${category?.name}` : ''
          )}
        </td>
      ),
    },
    {
      label: 'Tags',
      path: 'tags',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.tags[0] ? data?.tags[0] : '---'}
          {data?.tags?.map((tag: any, index: any) =>
            index > 0 ? ` , ${tag}` : ''
          )}
        </td>
      ),
    },
  ];

  const handleSubmit = async () => {
    let data = {
      name: warehouse.name,
      title: warehouse.title,
      owner: warehouse.owner,
      email: warehouse.email,
      phone: warehouse.phone,
      address1: warehouse.address1,
      address2: warehouse.address2,
      city: warehouse.city,
      state: warehouse.state,
      postCode: warehouse.postCode,
      status: warehouse.status,
      stock,
    };
    console.log(data);
    setStock([]);
  };

  const saveStock = (productId: string) => {
    const quantity = parseInt(
      (document.getElementById(productId) as HTMLInputElement).value
    );

    let newList: IStock[] = stock;
    const existingDetails = stock?.find((item) => item.productId === productId);
    if (existingDetails) {
      newList = stock.filter(
        (item) => item.productId !== existingDetails.productId
      );
    }
    newList.push({
      productId,
      quantity,
    });
    console.log(newList);
    setStock(newList);
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const checkForPrev = (id: string) => {
    let ans = 0;
    warehouseToEdit.stock.forEach((item: any) => {
      if (item.product.id === id) {
        ans = item.quantity;
      }
    });
    return ans;
  };

  return (
    <>
      <div className="content-header clearfix" style={{ paddingTop: '10px' }}>
        <h3 className="float-start">
          Edit An Warehouse: Step 2
          <span className="fs-5 p-3">
            <Link
              href={`/warehouses/edit/${id}`}
              className="text-decoration-none"
            >
              <i className="bi bi-arrow-left-circle-fill p-2" />
              <span style={{ fontSize: '14px' }}>Back to step 1</span>
            </Link>
          </span>
        </h3>
        <div className="float-end">
          <button
            type="submit"
            onClick={handleSubmit}
            name="save"
            className="btn btn-primary m-1"
          >
            {/* <i className="bi bi-save" /> */}
            <p className="float-end mx-1 my-0">Save</p>
          </button>
        </div>
      </div>
      <div className="mt-4">
        {warehouse.products.map((product) => {
          return (
            <React.Fragment key={product.id}>
              <Table
                items={[product]}
                columns={columns}
                onClickForSort={onClickForSort}
              />
              <div className="form-group row my-2 mb-3">
                <div className="col-md-2">
                  <div className="label-wrapper row row-cols-auto pe-3">
                    <label
                      className="col-form-label col fs-5 px-1 text-center"
                      htmlFor={product.id}
                    >
                      Stock Quantity
                      <span className="required text-danger ">*</span>
                    </label>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className="input-group">
                    <input
                      ref={ref}
                      defaultValue={checkForPrev(product?.id!)}
                      required
                      type="number"
                      id={product.id}
                      name={product.id}
                      className="border-bottom rounded-0 border border-0 border-2 p-2 shadow-none form-control"
                    />
                  </div>
                </div>
                <div className="col-md-4">
                  <button
                    type="button"
                    onClick={() => saveStock(product.id!)}
                    className="ms-md-4 mt-2 mt-md-0 btn btn-primary"
                  >
                    Update
                  </button>
                </div>
              </div>
            </React.Fragment>
          );
        })}
      </div>
    </>
  );
};

export default EditWarehouseStep2;
