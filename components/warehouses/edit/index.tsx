import { useAppDispatch } from '@/redux-hooks';
import { saveWarehouse } from '@/toolkit/warehouseSlice';
import { Form, Formik } from 'formik';
import { Product } from 'models';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useEffect, useState } from 'react';
import Info from '../forms/info';
import ProductSelectionForm from '../forms/productSelection';

interface Options {
  label: string;
  value: string | boolean;
}

interface Props {
  warehouseToEdit: any;
}

const EditWarehouseDetails: FC<Props> = ({ warehouseToEdit }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [selectedProducts, setSelectedProducts] = useState<Product[]>();

  const [statusOptions, setStatusOptions] = useState<Options[]>([
    {
      label: 'Active',
      value: true,
    },
    {
      label: 'Inactive',
      value: false,
    },
  ]);

  const handleSubmit = async (data: any) => {
    let warehouse = data;
    delete warehouse.products;
    warehouse = { ...warehouse, products: selectedProducts };
    dispatch(saveWarehouse(warehouse));
    router.push(`/warehouses/edit/${router.query.id}/step-2`);
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  const separateProducts = () => {
    const list = warehouseToEdit.stock.map((item: any) => item.product);
    setSelectedProducts(list);
  };

  useEffect(() => {
    separateProducts();
  }, []);

  return (
    <>
      <Formik
        initialValues={{
          name: warehouseToEdit ? warehouseToEdit.name : '',
          title: warehouseToEdit ? warehouseToEdit.title : '',
          owner: warehouseToEdit ? warehouseToEdit.owner : '',
          email: warehouseToEdit ? warehouseToEdit.email : '',
          phone: warehouseToEdit ? warehouseToEdit.phone : '',
          address1: warehouseToEdit ? warehouseToEdit.address1 : '',
          address2: warehouseToEdit ? warehouseToEdit.address2 : '',
          city: warehouseToEdit ? warehouseToEdit.city : '',
          state: warehouseToEdit ? warehouseToEdit.state : '',
          postCode: warehouseToEdit ? warehouseToEdit.postCode : '',
          status: warehouseToEdit ? warehouseToEdit.status : '',
          products: warehouseToEdit ? warehouseToEdit.stock : [],
        }}
        onSubmit={(values, actions) => {
          handleSubmit(values);
          actions.setSubmitting(false);
        }}
        //validationSchema={categorySchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix"
                style={{ paddingTop: '10px' }}
              >
                <h3 className="float-start">
                  Edit An Warehouse
                  <span className="fs-5 p-3">
                    <Link href="/warehouses" className="text-decoration-none">
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      <span style={{ fontSize: '14px' }}>
                        Back to warehouse list
                      </span>
                    </Link>
                  </span>
                </h3>
              </div>
              <div className="mt-4">
                <Info statusOptions={statusOptions} />
                <ProductSelectionForm
                  selectedProducts={selectedProducts!}
                  setSelectedProducts={setSelectedProducts}
                />
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default EditWarehouseDetails;
