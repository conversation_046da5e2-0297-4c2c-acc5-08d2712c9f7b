import { saveWarehouse } from '@/toolkit/warehouseSlice';
import { Form, Formik } from 'formik';
import { Product } from 'models';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';
import { useDispatch } from 'react-redux';
import Info from '../forms/info';
import ProductSelectionForm from '../forms/productSelection';

interface Options {
  label: string;
  value: string | boolean;
}

const CreateWarehouse: FC = () => {
  const router = useRouter();
  const dispatch = useDispatch();

  const [statusOptions, setStatusOptions] = useState<Options[]>([
    {
      label: 'Active',
      value: true,
    },
    {
      label: 'Inactive',
      value: false,
    },
  ]);

  const [selectedProducts, setSelectedProducts] = useState<Product[]>([]);

  const handleSubmit = async (data: any) => {
    let warehouse = data;
    delete warehouse.products;
    warehouse = { ...warehouse, products: selectedProducts };
    dispatch(saveWarehouse(warehouse));
    router.push('/warehouses/add-new/step-2');
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          name: '',
          title: '',
          owner: '',
          email: '',
          phone: '',
          address1: '',
          address2: '',
          city: '',
          state: '',
          postCode: '',
          status: '',
          products: [],
        }}
        onSubmit={(values, actions) => {
          handleSubmit(values);
          actions.setSubmitting(false);
        }}
        //validationSchema={categorySchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix"
                style={{ paddingTop: '10px' }}
              >
                <h3 className="float-start">
                  Create An Warehouse
                  <span className="fs-5 p-3">
                    <Link href="/warehouses" className="text-decoration-none">
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      <span style={{ fontSize: '14px' }}>
                        Back to warehouse list
                      </span>
                    </Link>
                  </span>
                </h3>
              </div>
              <div className="mt-4">
                <Info statusOptions={statusOptions} />
                <ProductSelectionForm
                  selectedProducts={selectedProducts}
                  setSelectedProducts={setSelectedProducts}
                />
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default CreateWarehouse;
