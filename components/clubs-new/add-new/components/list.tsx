import { FC, useState } from 'react';

import Table from '@/components/global/table/table';
import { config } from 'config';
import myImageLoader from 'image/loader';
import Image from 'next/image';

interface Props {
  clubsFound: any;
  selectedClubs: any;
  setSelectedClubs: Function;
}

const List: FC<Props> = ({ clubsFound, selectedClubs, setSelectedClubs }) => {
  const [ClubID, setClubID] = useState('');
  const [checkAll, setCheckAll] = useState(false);

  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const columns = [
    {
      label: (
        <input
          type="checkbox"
          onClick={(e: any) => {
            if (e.target.checked) {
              setSelectedClubs(clubsFound);
            } else {
              setSelectedClubs([]);
            }
            setCheckAll(!checkAll);
          }}
        ></input>
      ),
      path: 'select',
      content: (data: any, key: any, index: any) => (
        <td className="text-center px-3 py-5">
          {checkAll && <input type="checkbox" value="" checked></input>}
          {!checkAll && (
            <input
              type="checkbox"
              value=""
              onClick={(e: any) => {
                if (e.target.checked) {
                  const list = new Set(selectedClubs);
                  list.add(data);
                  setSelectedClubs(Array.from(list));
                } else {
                  const list = new Set(selectedClubs);
                  list.delete(data);
                  setSelectedClubs(Array.from(list));
                }
              }}
            ></input>
          )}
        </td>
      ),
    },
    {
      label: 'Image',
      path: 'photo_reference',
      content: (data: any, key: any, index: any) => (
        <>
          {/* {console.log(data)} */}
          <td className="text-center align-middle p-4">
            <Image
              loader={myImageLoader}
              src={`${
                config.mapApiURL
              }/photo?maxwidth=400&photo_reference=${data.photos![0]
                .photo_reference!}&key=${config.map_api}`}
              height={100}
              width={100}
              alt="..."
            />
          </td>
        </>
      ),
    },
    {
      label: 'Icon',
      path: 'icon',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle p-4">
          <Image
            loader={myImageLoader}
            src={`${data[key]}`}
            height={40}
            width={40}
            alt="..."
          />
        </td>
      ),
    },
    // {
    //   label: 'Place ID',
    //   path: 'place_id',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="text-center align-middle">{data[key]}</td>
    //   ),
    // },
    {
      label: 'Name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Address',
      path: 'formatted_address',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 rounded px-2 mt-5 container">
        <div className="card-body">
          <h4 className="mb-3">Gyms based on your searched state</h4>
          {clubsFound?.length! > 0 ? (
            <Table
              items={clubsFound}
              columns={columns}
              onClickForSort={onClickForSort}
            />
          ) : (
            'No gym found'
          )}
        </div>
      </div>
    </>
  );
};

export default List;
