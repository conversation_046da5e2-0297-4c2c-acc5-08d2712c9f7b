import Link from 'next/link';
import { FC } from 'react';

interface Props {
  handleSave: Function;
}

const Header: FC<Props> = ({ handleSave }) => {
  return (
    <div className="content-header clearfix" style={{ paddingTop: '10px' }}>
      <h3 className="float-start">
        Add A Club
        <span className="fs-5 p-3">
          <Link href="/clubManagement/clubs" className="text-decoration-none">
            <i className="bi bi-arrow-left-circle-fill p-2" />
            <span style={{ fontSize: '14px' }}>Back to Club list</span>
          </Link>
        </span>
      </h3>
      <div className="float-end">
        <button
          name="save"
          className="btn btn-primary m-1"
          onClick={() => handleSave()}
        >
          <p className="float-end mx-1 my-0">Save</p>
        </button>
      </div>
    </div>
  );
};

export default Header;
