import { <PERSON>M<PERSON>, Marker } from '@react-google-maps/api';
import { FC, useCallback, useEffect, useState } from 'react';

interface IToBeMarked {
  lat: number;
  lng: number;
}

interface Props {
  toBeMarked: IToBeMarked[];
}

const RenderMap: FC<Props> = ({ toBeMarked }) => {
  const [map, setMap] = useState<google.maps.Map>();
  const onLoad = useCallback((map) => setMap(map), []);

  useEffect(() => {
    if (map) {
      const bounds = new window.google.maps.LatLngBounds();
      toBeMarked.map((marker) => {
        bounds.extend({
          lat: marker.lat,
          lng: marker.lng,
        });
      });
      map.fitBounds(bounds);
    }
  }, [map, toBeMarked]);

  return (
    <div className="mt-3 d-flex justify-content-center bg-white container">
      <div style={{ height: `600px`, width: `900px` }} className="px-0 py-4">
        <GoogleMap
          center={toBeMarked[0]}
          zoom={13}
          mapContainerStyle={{ width: '100%', height: '100%' }}
          options={{
            streetViewControl: false,
            mapTypeControl: false,
          }}
          onLoad={onLoad}
        >
          <Marker position={toBeMarked[0]} />
          {toBeMarked &&
            toBeMarked!.map((point: any, index: number) => {
              return <Marker key={index} position={point} />;
            })}
        </GoogleMap>
      </div>
    </div>
  );
};

export default RenderMap;
