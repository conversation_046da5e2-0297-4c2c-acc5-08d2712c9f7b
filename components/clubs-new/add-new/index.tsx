import { userAPI } from '@/APIs';
import Header from '@/components/clubs-new/add-new/components/header';
import List from '@/components/clubs-new/add-new/components/list';
import RenderMap from '@/components/clubs-new/add-new/components/renderMap';
import SelectCountry from '@/components/clubs-new/forms/selectFields/selectCountry';
import SelectStates from '@/components/clubs-new/forms/selectFields/selectState';
import { useJsApiLoader } from '@react-google-maps/api';
import { config } from 'config';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import stateData from '../../../allData/stateData.json';
import SelectCity from '../forms/selectFields/selectCity';

interface Options {
  label: string;
  value: string;
}

const AddNewClub = () => {
  const center = { lat: 24.554203, lng: -81.7787515 };

  const [states, setStates] = useState<Options[]>([]);
  const [cities, setCities] = useState<Options[]>([]);
  const [selectedCity, setSelectedCity] = useState();
  const [selectedState, setSelectedState] = useState();
  const [selectedClubs, setSelectedClubs] = useState<any>([]);
  const [clubsFound, setClubsFound] = useState();
  const [toBeMarked, setToBeMarked] = useState([center]);
  const [map, setMap] = useState<any>();

  const { isLoaded } = useJsApiLoader({
    googleMapsApiKey: config?.map_api!,
  });

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  const handleMapSearch = async () => {
    try {
      const res = await userAPI.getClubsFromGoogleMap(
        `gyms+in+${selectedCity}+usa`
      );
      setClubsFound(res.results);
      const geoLocations = res.results.map((club: any) => {
        return {
          lat: club.geometry.location.lat,
          lng: club.geometry.location.lng,
        };
      });
      setToBeMarked(geoLocations);
      toast.info('Scroll down to see search result details');
    } catch (error) {
      console.log(error);
    }
  };

  const handleSave = async () => {
    console.log(selectedClubs);
    getSingleClubDetail(selectedClubs[0].place_id);
  };

  const getSingleClubDetail = async (placeId: string) => {
    try {
      const res = await userAPI.getClubDetailsFromGoogleMap(placeId);
      //console.log(res.result);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    const stateOpts = [
      {
        label: 'New York',
        value: 'New York',
      },
      {
        label: 'California',
        value: 'California',
      },
      {
        label: 'Florida',
        value: 'Florida',
      },
      {
        label: 'North Carolina',
        value: 'North Carolina',
      },
      {
        label: 'Minnesota',
        value: 'Minnesota',
      },
      {
        label: 'North Dakota',
        value: 'North Dakota',
      },
    ];
    setStates(stateOpts);
  }, []);

  useEffect(() => {
    let cityList: Options[] = [];
    stateData.data.forEach((value: any) => {
      if (value.admin_name1 === selectedState)
        cityList.push({
          label: value.place_name,
          value: value.place_name,
        });
    });
    setCities(cityList);
  }, [selectedState]);

  if (!isLoaded) return <p>Loading</p>;

  return (
    <div>
      <Header handleSave={handleSave} />

      <div className="container">
        <SelectCountry
          countries={[{ label: 'USA', value: 'USA' }]}
          disabled={true}
        />
      </div>

      <br />

      <SelectStates states={states} setSelectedState={setSelectedState} />

      {selectedState && (
        <div className="d-flex justify-content-center gap-2 flex-md-row flex-column mt-4 container">
          {' '}
          <SelectCity cities={cities} setSelectedCity={setSelectedCity} />
          {cities?.length! > 0 && (
            <div>
              <button
                type="submit"
                name="selectState"
                className="btn btn-primary"
                onClick={handleMapSearch}
              >
                Search
              </button>
            </div>
          )}
        </div>
      )}

      <RenderMap toBeMarked={toBeMarked} />

      <List
        clubsFound={clubsFound}
        selectedClubs={selectedClubs}
        setSelectedClubs={setSelectedClubs}
      />
    </div>
  );
};

export default AddNewClub;
