import React, { FC } from 'react';

interface Options {
  label: string;
  value: string;
}

interface Props {
  cities: Options[];
  setSelectedCity: Function;
}

const SelectCity: FC<Props> = ({ cities, setSelectedCity }) => {
  return (
    <>
      {cities?.length! > 0 ? (
        <div className="form-group row w-md-75 w-100">
          <div className="col-md-2">
            <div className="label-wrapper row row-cols-auto float-md-end pe-3">
              <label
                className="col-form-label col fs-5 px-1 text-center"
                htmlFor="city"
              >
                Select A City
              </label>
            </div>
          </div>
          <div className="col-md-10">
            <select
              className="form-select custom-select w-100"
              id="city"
              name="city"
              onChange={(e: any) => {
                if (
                  (e.target as HTMLInputElement).value &&
                  (e.target as HTMLInputElement).value != 'Select One City'
                ) {
                  setSelectedCity((e.target as HTMLInputElement).value!);
                }
              }}
            >
              <option disabled>Select One City</option>
              {cities?.map((city, index) => {
                return (
                  <React.Fragment key={index}>
                    <option value={city.value}>{city.label}</option>
                  </React.Fragment>
                );
              })}
            </select>
          </div>
        </div>
      ) : (
        <div>No City Found</div>
      )}
    </>
  );
};

export default SelectCity;
