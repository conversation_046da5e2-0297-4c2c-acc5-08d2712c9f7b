import React, { <PERSON> } from 'react';

interface Options {
  label: string;
  value: string;
}

interface Props {
  states: Options[];
  setSelectedState: Function;
}

const SelectStates: FC<Props> = ({ states, setSelectedState }) => {
  return (
    <>
      {states?.length! > 0 && (
        <div className="form-group row w-md-75 w-100">
          <div className="col-md-2">
            <div className="label-wrapper row row-cols-auto float-md-end pe-3">
              <label
                className="col-form-label col fs-5 px-1 text-center"
                htmlFor="state"
              >
                Select A State
              </label>
            </div>
          </div>
          <div className="col-md-10">
            <select
              className="form-select custom-select w-100"
              id="state"
              name="state"
              onChange={(e: any) => {
                if (
                  (e.target as HTMLInputElement).value &&
                  (e.target as HTMLInputElement).value != 'Select One State'
                ) {
                  setSelectedState((e.target as HTMLInputElement).value!);
                }
              }}
            >
              <option disabled>Select One State</option>
              {states?.map((state) => {
                return (
                  <React.Fragment key={state.label}>
                    <option value={state.value}>{state.label}</option>
                  </React.Fragment>
                );
              })}
            </select>
          </div>
        </div>
      )}
    </>
  );
};

export default SelectStates;
