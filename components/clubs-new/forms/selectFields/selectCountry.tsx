import React, { FC } from 'react';

interface Options {
  label: string;
  value: string;
}

interface Props {
  countries: Options[];
  setSelectedCountry?: Function;
  disabled: boolean;
}

const SelectCountry: FC<Props> = ({
  countries,
  setSelectedCountry,
  disabled,
}) => {
  return (
    <>
      {countries?.length! > 0 && (
        <div className="form-group row w-md-75 w-100 mt-3">
          <div className="col-md-2">
            <div className="label-wrapper row row-cols-auto float-md-end pe-4">
              <label
                className="col-form-label col fs-5 px-1 text-center"
                htmlFor="country"
              >
                Country
              </label>
            </div>
          </div>
          <div className="col-md-9">
            <select
              className="form-select custom-select w-100"
              id="country"
              name="country"
              disabled={disabled}
              onChange={(e: any) => {
                if (
                  (e.target as HTMLInputElement).value &&
                  (e.target as HTMLInputElement).value != 'Select One Country'
                ) {
                  setSelectedCountry!((e.target as HTMLInputElement).value!);
                }
              }}
            >
              {countries?.map((country) => {
                return (
                  <React.Fragment key={country.label}>
                    <option value={country.value}>{country.label}</option>
                  </React.Fragment>
                );
              })}
            </select>
          </div>
        </div>
      )}
    </>
  );
};

export default SelectCountry;
