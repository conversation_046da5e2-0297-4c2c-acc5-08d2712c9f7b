import { userAPI } from '@/APIs';
import myImageLoader from 'image/loader';
import { IUserChallengeStatusEnum } from 'models';
import Image from 'next/image';
import Link from 'next/link';
import { FC, useState } from 'react';
import { toast } from 'react-toastify';

interface Props {
  refundRequest: any;
  getRefundRequestList: Function;
}

const TableBody: FC<Props> = ({ refundRequest, getRefundRequestList }) => {
  const [editState, setEditState] = useState<string>('edit');

  const handleUpdate = async () => {
    try {
      const status = (
        document.getElementById(refundRequest.id) as HTMLInputElement
      ).value;
      const id = refundRequest.id;
      const res = await userAPI.editAcceptedChallenge(status, id);
      toast.success('Status Updated');
      setEditState('edit');
      getRefundRequestList(IUserChallengeStatusEnum.PENDING);
    } catch (error) {}
  };
  return (
    <>
      <tr>
        {/* <td className="text-center align-middle">{challenge.id}</td> */}
        <td className="text-center align-middle">
          {refundRequest.orderID ? refundRequest.orderID : '--'}
        </td>
        <td className="text-center align-middle">
          {refundRequest.reason ? refundRequest.reason : '--'}
        </td>
        <td className="text-center align-middle">
          <Image
            src={refundRequest.attachment}
            height={100}
            width={100}
            alt="Refund request attachment"
            loader={myImageLoader}
          />
        </td>
        <td className="text-center align-middle">
          {refundRequest.username ? refundRequest.username : '--'}
        </td>

        <td className="text-center align-middle">
          {editState === 'edit' ? (
            <>{refundRequest.status}</>
          ) : (
            <>
              <select className="form-select text-center" id={refundRequest.id}>
                <option selected>Select One</option>
                <option value="Accept">Accept</option>
                <option value="Reject">Reject</option>
              </select>
            </>
          )}
        </td>
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/refund-requests/[id]`,
              query: { id: refundRequest.id },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              View
            </button>
          </Link>
        </td>

        {refundRequest.status === 'Requested' && (
          <td className="text-center align-middle">
            {editState === 'edit' ? (
              <button
                className={`btn btn-default btn-outline-info`}
                onClick={() => {
                  setEditState('update');
                }}
              >
                <>
                  <span>
                    <i className="bi bi-pencil m-2 align-middle"></i>
                  </span>
                  Edit
                </>
              </button>
            ) : (
              <>
                <button
                  className="btn btn-default btn-outline-success m-3"
                  onClick={handleUpdate}
                >
                  <>
                    <span>
                      <i className="bi bi-pencil m-2 align-middle"></i>
                    </span>
                    Update
                  </>
                </button>

                <button
                  className="btn btn-default btn-outline-secondary"
                  onClick={() => {
                    setEditState('edit');
                  }}
                >
                  <>
                    <span>
                      <i className="bi bi-x m-2 align-middle"></i>
                    </span>
                    Cancel
                  </>
                </button>
              </>
            )}
          </td>
        )}
      </tr>
    </>
  );
};

export default TableBody;
