import { FC } from 'react';

import { config } from 'config';
import React from 'react';
import TableBody from './tableBody';
import { handlePagination } from 'utils/handlePagination';

interface Props {
  refundRequestList: any;
  setRefundRequestList: Function;
  getRefundRequestList: Function;
  refundStatus: string;
  setSkip: Function;
  skip: number;
  showSeeMore: boolean;
  setLoadData: Function;
  loadData: boolean;
  setShowSeeMore: Function;
}

const RefuncRequestList: FC<Props> = ({
  refundRequestList,
  setRefundRequestList,
  getRefundRequestList,
  refundStatus,
  setSkip,
  skip,
  showSeeMore,
  setLoadData,
  loadData,
  setShowSeeMore,
}) => {
  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <div
            className="d-flex flex-wrap justify-content-between"
            style={{ overflow: 'auto' }}
          >
            <table className="table table-bordered table-striped">
              <thead className="border">
                <tr>
                  {' '}
                  {/* <th className="text-center p-2">ID</th> */}
                  <th className="text-center p-2">Order ID</th>
                  <th className="text-center p-2">Reason</th>
                  <th className="text-center p-2">Attachment</th>
                  <th className="text-center p-2">User Name</th>
                  <th className="text-center p-2">Status</th>
                  <th className="text-center p-2">View Detail</th>
                  {refundStatus !== 'Rejected' && (
                    <th className="text-center p-2">Update Status</th>
                  )}
                </tr>
              </thead>
              <tbody>
                {refundRequestList?.map((data: any) => {
                  return (
                    <React.Fragment key={data.id}>
                      <TableBody
                        refundRequest={data}
                        getRefundRequestList={getRefundRequestList}
                      />
                    </React.Fragment>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </>
  );
};

export default RefuncRequestList;
