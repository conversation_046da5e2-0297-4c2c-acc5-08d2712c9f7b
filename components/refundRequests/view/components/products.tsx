import Table from '@/components/global/table/table';
import myImageLoader from 'image/loader';
import Image from 'next/image';
import { FC } from 'react';
interface Props {
  request: any;
}
const Products: FC<Props> = ({ request }) => {
  const products = request?.products;

  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const columns = [
    {
      label: 'Picture',
      path: 'photo',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.[key] ? (
            <Image
              loader={myImageLoader}
              src={data && data?.[key] && data?.[key]}
              alt="Photo"
              height={100}
              width={100}
            />
          ) : (
            'No Image Found'
          )}
        </td>
      ),
    },
    {
      label: 'Product Name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.[key]}</td>
      ),
    },
    {
      label: 'Price',
      path: 'price',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.[key]}</td>
      ),
    },
    {
      label: 'Quantity',
      path: 'quantity',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.[key]}</td>
      ),
    },

    {
      label: 'Total',
      path: 'total',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.quantity * data?.price}
        </td>
      ),
    },
  ];

  return (
    <div>
      <p className="fs-4 fw-semibold">Ordered Products</p>
      <Table
        items={products}
        columns={columns}
        onClickForSort={onClickForSort}
      />
    </div>
  );
};

export default Products;
