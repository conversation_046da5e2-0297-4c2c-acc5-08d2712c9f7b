import { FC } from 'react';

import SingleView from '@/components/common/singleView';

interface Props {
  refundRequest: any;
}

const RequestInfoCard: FC<Props> = ({ refundRequest }) => {
  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="request-info"
        id="request-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <div className="card-title row align-items-center ps-2 pt-2">
            <i
              className="bi bi-info-lg col-1 align-text-top"
              style={{ fontSize: '25px' }}
            />
            <div className="fs-5 col text-start px-3">Request Info</div>
          </div>
        </div>
        <div className="" id="requestInfoTab">
          <div className="card-body">
            <SingleView label="ID" value={refundRequest.id} />
            <SingleView label="Order ID" value={refundRequest.orderID} />
            <SingleView label="Reason" value={refundRequest.reason} />
            <SingleView label="Username" value={refundRequest.username} />
            <SingleView label="Email" value={refundRequest.email} />
            <SingleView label="Contact No." value={refundRequest.phone} />
            <SingleView label="Status" value={refundRequest.status} />
            <SingleView
              label="Attachment"
              attachment={refundRequest.attachment}
              isImage={true}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default RequestInfoCard;
