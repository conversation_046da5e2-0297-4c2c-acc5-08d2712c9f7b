import Link from 'next/link';
import RequestInfoCard from './components/cards/InfoCard';
import Products from './components/products';

const ViewRefundRequest: React.FC<any> = ({ refundRequest }) => {
  return (
    <>
      {refundRequest ? (
        <div>
          <div className="content-header clearfix mt-3">
            <h1 className="float-start">
              View request details
              <span className="fs-5 p-3">
                <Link href="/refund-requests" className="text-decoration-none ">
                  <i className="bi bi-arrow-left-circle-fill p-2" />
                  Back to request list
                </Link>
              </span>
            </h1>
          </div>

          <div className="mt-4">
            <RequestInfoCard refundRequest={refundRequest} />
          </div>
          <Products request={refundRequest} />
        </div>
      ) : (
        ''
      )}
    </>
  );
};

export default ViewRefundRequest;
