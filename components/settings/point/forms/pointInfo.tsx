import { ErrorMessage, Field } from 'formik';
import { FC, useState } from 'react';

interface Props {
  edit?: boolean;
}

const CreateInfo: FC<Props> = ({ edit }) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="point-create-info"
        data-hideattribute="ProductPage.HideInfoBlock"
        id="point-create-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#point-createInfoTab"
            aria-expanded="true"
            aria-controls="point-createInfoTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-info-lg col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col text-start">Add Point Details</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="point-createInfoTab">
          <div className="card-body">
            {/* <FieldTemplate
              label="1 Point Equivalent To"
              isRequired={!edit ? true : false}
              fieldID="point"
              fieldType="text"
            /> */}

            <div className="form-group row my-2 mb-3">
              <div className="col-md-5">
                <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                  <label
                    className="col-form-label col fs-5 px-1 text-center"
                    htmlFor="amount"
                  >
                    1 Point Equivalent to
                    <span className="text-danger">*</span>
                  </label>
                </div>
              </div>
              <div className="col-md-2">
                <div className="input-group mb-2">
                  <Field
                    className="form-control rounded border-black"
                    id="amount"
                    name="amount"
                    type="number"
                  />
                </div>
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="amount" />
                </div>
              </div>
              <div className="col-md-1 mt-1">
                <p className="text-base ms-3">USD</p>
              </div>
              <div className="col-md-2">
                <button
                  type="submit"
                  name="save"
                  className="btn btn-primary m-1"
                >
                  {/* <i className="bi bi-save" /> */}
                  <p className="float-end mx-1 my-0">Save</p>
                </button>
              </div>
            </div>
            {/* <FieldTemplate
              label="Enabled"
              isRequired={true}
              fieldID="enable"
              fieldType="checkbox"
              fieldClass="check-box mt-2  "
            /> */}
          </div>
        </div>
      </div>
    </>
  );
};

export default CreateInfo;
