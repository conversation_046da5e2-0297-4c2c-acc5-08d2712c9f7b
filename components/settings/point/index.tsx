import { userAPI } from '@/APIs';
import PointInfoCard from '@/components/settings/point/cards/pointsInfoCard';
import CreateInfo from '@/components/settings/point/forms/pointInfo';
import { Form, Formik } from 'formik';
import { IAmountPerPoint } from 'models';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { pointsSchema } from './schema';

const PointDetails = () => {
  const [point, setPoint] = useState<IAmountPerPoint>();
  const [pointToUpdate, setPointToUpdate] = useState<number>(0);
  const [modal, setModal] = useState({
    create: false,
  });

  const getPoints = async () => {
    try {
      const res = await userAPI.getPoint();
      if ('data' in res) {
        setPoint(res.data);
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  const handlePointSubmit = async () => {
    try {
      const res = await userAPI.setPoint(pointToUpdate!);
      console.log(res);
      if ('data' in res) {
        getPoints();
        toast.success('Point set successfully');
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
    setModal({ ...modal, create: false });
  };

  const onClickForUpdate = (point: number) => {
    setPointToUpdate(point);
    setModal({ ...modal, create: true });
  };

  useEffect(() => {
    getPoints();
  }, []);

  return (
    <>
      <PointInfoCard point={point!} />
      <Formik
        initialValues={{
          amount: 1,
          //enable: false,
        }}
        onSubmit={(values, actions) => {
          onClickForUpdate(values.amount);
          actions.setSubmitting(false);
        }}
        validationSchema={pointsSchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix"
                style={{ paddingTop: '10px' }}
              ></div>
              <div className="mt-4">
                <CreateInfo />
              </div>
            </Form>
          );
        }}
      </Formik>

      {modal.create ? (
        <div
          className="modal"
          style={{ display: modal.create ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              // close modal when outside of modal is clicked
              setModal({ ...modal, create: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                // do not close modal if anything inside modal content is clicked
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to update Point method?</p>
                <br />

                <div className="clearfix">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{
                      border: '1px solid gray',
                      backgroundColor: 'gray',
                      color: 'white',
                      marginRight: '10px',
                    }}
                    onClick={() =>
                      setModal({
                        ...modal,
                        create: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => handlePointSubmit()}
                  >
                    Confirm
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default PointDetails;
