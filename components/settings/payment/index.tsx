import { userAPI } from '@/APIs';
import { Form, Formik } from 'formik';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import PaymentMethodInfoCard from './cards/paymentMethodsInfoCard';
import CreateInfo from './forms/paymentMethodInfo';
import { paymentSchema } from './schema';

const PaymentDetails = () => {
  const [paymentMethods, setPaymentMethods] = useState<any>();
  const [list, setList] = useState([]);
  const [modal, setModal] = useState({
    create: false,
  });

  const getPaymentMethods = async () => {
    try {
      const res = await userAPI.getPaymentMethod();
      if ('data' in res) {
        setPaymentMethods(res.data);
      } else {
        toast.error(res.error.message);
      }
      setModal({
        ...modal,
        create: false,
      });
    } catch (error) {
      console.log(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  const handlePaymentMethodSubmit = async () => {
    try {
      const res = await userAPI.setPaymentMethod(list);
      if ('data' in res) {
        getPaymentMethods();
        toast.success('Payment methods added');
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onClickForUpdate = (list: any) => {
    setList(list);
    setModal({ ...modal, create: true });
  };

  useEffect(() => {
    getPaymentMethods();
  }, []);

  return (
    <>
      <PaymentMethodInfoCard paymentMethods={paymentMethods!} />
      <Formik
        initialValues={{
          list: [],
        }}
        onSubmit={(values, actions) => {
          onClickForUpdate(values.list);
          actions.setSubmitting(false);
        }}
        validationSchema={paymentSchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix"
                style={{ paddingTop: '10px' }}
              ></div>
              <div className="mt-4">
                <CreateInfo />
              </div>
            </Form>
          );
        }}
      </Formik>
      {modal.create ? (
        <div
          className="modal"
          style={{ display: modal.create ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              // close modal when outside of modal is clicked
              setModal({ ...modal, create: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                // do not close modal if anything inside modal content is clicked
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to update payment methods?</p>
                <br />

                <div className="clearfix">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{
                      border: '1px solid gray',
                      backgroundColor: 'gray',
                      color: 'white',
                      marginRight: '10px',
                    }}
                    onClick={() =>
                      setModal({
                        ...modal,
                        create: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => handlePaymentMethodSubmit()}
                  >
                    Update
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default PaymentDetails;
