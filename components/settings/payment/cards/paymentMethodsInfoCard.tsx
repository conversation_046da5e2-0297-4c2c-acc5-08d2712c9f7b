import SingleView from '@/components/common/singleView';
import { FC } from 'react';

interface Props {
  paymentMethods: any;
}

const PaymentMethodInfoCard: FC<Props> = ({ paymentMethods }) => {
  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="payment-info"
        id="payment-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <div className="card-title row align-items-center ps-2 pt-2">
            <i
              className="bi bi-info-lg col-1 align-text-top"
              style={{ fontSize: '25px' }}
            />
            <div className="fs-5 col text-start px-3">
              Current Payment Method Settings
            </div>
          </div>
        </div>
        <div className="" id="paymentMethodInfoTab">
          <div className="card-body">
            <SingleView label="Methods Enabled" value={paymentMethods?.list} />
          </div>
        </div>
      </div>
    </>
  );
};

export default PaymentMethodInfoCard;
