import { userAPI } from '@/APIs';
import PointInfoCard from '@/components/settings/shipping/cards/pointsInfoCard';
import CreateInfo from '@/components/settings/shipping/forms/pointInfo';
import { Form, Formik } from 'formik';
import { IAmountPerPoint } from 'models';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { shippingSchema } from './schema';
import modal from '@/components/sales/service/modal';

const ShippingDetails = () => {
  const [point, setPoint] = useState<IAmountPerPoint>();
  const [modal, setModal] = useState({
    create: false,
  });
  const handlePointSubmit = async () => {
    try {
      if (true) {
        //getPoints();
        toast.success('Shipment amount set successfully');
      } else {
        toast.error('Error happened');
      }
    } catch (error) {
      console.log(error);
    }
    setModal({ ...modal, create: false });
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  const onClickForUpdate = () => {
    //setList(list);
    setModal({ ...modal, create: true });
  };

  return (
    <>
      <PointInfoCard point={point!} />
      <Formik
        initialValues={{
          amount: 70,
          //enable: false,
        }}
        onSubmit={(values, actions) => {
          //handlePointSubmit(values.amount);
          onClickForUpdate();
          actions.setSubmitting(false);
        }}
        validationSchema={shippingSchema}
      >
        {(formikprops) => {
          return (
            // eslint-disable-next-line react/jsx-no-duplicate-props
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix"
                style={{ paddingTop: '10px' }}
              ></div>
              <div className="mt-4">
                <CreateInfo />
              </div>
            </Form>
          );
        }}
      </Formik>

      {modal.create ? (
        <div
          className="modal"
          style={{ display: modal.create ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              // close modal when outside of modal is clicked
              setModal({ ...modal, create: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                // do not close modal if anything inside modal content is clicked
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to update Shipment cost method?</p>
                <br />

                <div className="clearfix">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{
                      border: '1px solid gray',
                      backgroundColor: 'gray',
                      color: 'white',
                      marginRight: '10px',
                    }}
                    onClick={() =>
                      setModal({
                        ...modal,
                        create: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => handlePointSubmit()}
                  >
                    Confirm
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default ShippingDetails;
