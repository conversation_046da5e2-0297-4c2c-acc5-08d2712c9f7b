import SingleView from '@/components/common/singleView';
import { IAmountPerPoint } from 'models';
import { FC } from 'react';

interface Props {
  point: IAmountPerPoint;
}

const PointInfoCard: FC<Props> = () => {
  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="shipping-info"
        id="shipping-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <div className="card-title row align-items-center ps-2 pt-2">
            <i
              className="bi bi-info-lg col-1 align-text-top"
              style={{ fontSize: '25px' }}
            />
            <div className="fs-5 col text-start px-3">
              Free Shipment Settings
            </div>
          </div>
        </div>
        <div className="" id="pointInfoTab">
          <div className="card-body">
            <SingleView label="0 Shipping Cost" value={`70 USD`} />
          </div>
        </div>
      </div>
    </>
  );
};

export default PointInfoCard;
