import { userAP<PERSON> } from '@/APIs';
import { Form, Formik } from 'formik';
import { ClubLocationType, CreateClubRequestBody } from 'models';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC } from 'react';
import { toast } from 'react-toastify';
import { createClubSchema } from '../schema/index';
import Address from './forms/address';
import ClubInfo from './forms/clubInfo';
import Location from './forms/location';

const CreateClub: FC = () => {
  const router = useRouter();

  const handleSubmit = async (data: CreateClubRequestBody) => {
    try {
      const res = await userAPI.createClub(data);
      if (!res.data) {
        toast.error("Can't Create Club");
      } else {
        router.push('/clubManagement/clubs');
        toast.success('Club Created Successfully');
      }
      console.log(res);
    } catch (error: any) {
      toast.error(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          name: '',
          description: '',
          cover: '',
          logo: '',
          addressLine1: '',
          addressLine2: '',
          city: '',
          country: '',
          postCode: '',
          coordinatesx: '',
          coordinatesy: '',
        }}
        onSubmit={(values, actions) => {
          const data = {
            name: values.name,
            image: {
              cover: values.cover,
              logo: values.logo,
            },
            location: {
              type: ClubLocationType.POINT,
              coordinates: [
                parseFloat(values.coordinatesy),
                parseFloat(values.coordinatesx),
              ],
            },
            address: {
              addressLine1: values.addressLine1,
              addressLine2: values.addressLine2,
              city: values.city,
              country: values.country,
              postCode: values.postCode,
            },
            description: values.description,
          };
          handleSubmit(data);
          actions.setSubmitting(false);
        }}
        validationSchema={createClubSchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix"
                style={{ paddingTop: '10px' }}
              >
                <h3 className="float-start">
                  Add A Club
                  <span className="fs-5 p-3">
                    <Link
                      href="/clubManagement/clubs"
                      className="text-decoration-none"
                    >
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      <span style={{ fontSize: '14px' }}>
                        Back to Club list
                      </span>
                    </Link>
                  </span>
                </h3>
                <div className="float-end">
                  <button
                    type="submit"
                    name="save"
                    className="btn btn-primary m-1"
                  >
                    {/* <i className="bi bi-save" /> */}
                    <p className="float-end mx-1 my-0">Save</p>
                  </button>
                </div>
              </div>
              <div className="mt-4">
                <ClubInfo setFieldValue={formikprops.setFieldValue} />
                <Address />
                <Location setFieldValue={formikprops.setFieldValue} />
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default CreateClub;
