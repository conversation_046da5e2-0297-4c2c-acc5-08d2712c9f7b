import { ErrorMessage, Field } from 'formik';
import { FC, useState } from 'react';

const Address: FC = () => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="photos"
        id="photos"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#photosTab"
            aria-expanded="true"
            aria-controls="photosTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-house-door-fill col-1"
                style={{ fontSize: '20px' }}
              />
              <div className="fs-5 col px-3 text-start">Address</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="photosTab">
          <div className="card-body">
            <div className="form-group row my-2">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end">
                  <label
                    className="col-form-label col px-1"
                    htmlFor="addressLine1"
                  >
                    Address Line 1
                    <span className="required text-danger ">*</span>
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className="input-group pe-3">
                  <Field
                    className="border-bottom form-control rounded-0 border-3 border border-0 shadow-none"
                    id="addressLine1"
                    name="addressLine1"
                    type="text"
                  />
                </div>
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="addressLine1" />
                </div>
              </div>
            </div>
            <div className="form-group row my-2">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end">
                  <label
                    className="col-form-label col px-1"
                    htmlFor="addressLine2"
                  >
                    Address Line 2
                    <span className="required text-danger ">*</span>
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className="input-group pe-3">
                  <Field
                    className="border-bottom form-control rounded-0 border-3 border border-0 shadow-none"
                    id="addressLine2"
                    name="addressLine2"
                    type="text"
                  />
                </div>
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="addressLine2" />
                </div>
              </div>
            </div>
            <div className="form-group row my-2">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end">
                  <label className="col-form-label col px-1" htmlFor="city">
                    City
                    <span className="required text-danger ">*</span>
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className="input-group pe-3 ">
                  <Field
                    type="text"
                    className="border-bottom form-control rounded-0 border-2 border border-0 shadow-none"
                    id="city"
                    name="city"
                  />
                </div>
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="city" />
                </div>
              </div>
            </div>
            <div className="form-group row my-2">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end">
                  <label className="col-form-label col px-1" htmlFor="country">
                    Country
                    <span className="required text-danger ">*</span>
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className="input-group pe-3 ">
                  <Field
                    type="text"
                    className="border-bottom form-control rounded-0 border-2 border border-0 shadow-none"
                    id="country"
                    name="country"
                  />
                </div>
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="country" />
                </div>
              </div>
            </div>
            <div className="form-group row my-2">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end">
                  <label className="col-form-label col px-1" htmlFor="postCode">
                    Postal Code
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className="input-group pe-3 ">
                  <Field
                    type="text"
                    className="border-bottom form-control rounded-0 border-2 border border-0 shadow-none"
                    id="postCode"
                    name="postCode"
                  />
                </div>
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="postCode" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Address;
