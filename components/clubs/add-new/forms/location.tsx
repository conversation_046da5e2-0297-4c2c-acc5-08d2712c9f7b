import React, { FC, useState } from 'react';
import CoordinateInput from './co-ordinateInput';

interface Props {
  setFieldValue: Function;
}

const Location: FC<Props> = ({ setFieldValue }) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');
  const [urlOrCoordinate, setUrlOrCoordinate] = useState('');

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  const options = [
    {
      value: 'choose',
      label: 'Choose...',
    },
    {
      value: 'url',
      label: 'URL (Select a location from Google Map & enter the URL)',
    },
    {
      value: 'coordinates',
      label: 'Co-ordinates (Enter co-ordinate values)',
    },
  ];

  const openInNewTab = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const URLtoLatLng = () => {
    const selected = document.getElementById('urlInput') as HTMLInputElement;
    let url = '';
    if (selected != null) {
      url = selected.value;
    }
    if (url.length > 0) {
      const s = url.match(/@(.*),/)!;
      const coordinates = s[1].split(',');
      setFieldValue('coordinatesx', coordinates[0]);
      setFieldValue('coordinatesy', coordinates[1]);
    }
  };

  const handleSelectInput = () => {
    const selected = document.getElementById(
      'loactionInput'
    ) as HTMLInputElement;
    let value = '';
    if (selected != null) {
      value = selected.value;
    }
    setUrlOrCoordinate(value);
    if (value === 'url') {
      openInNewTab('https://www.google.com/maps/@23.7806365,90.4193257,12z');
      setFieldValue('coordinatesx', '');
      setFieldValue('coordinatesy', '');
    } else if (value === 'coordinates') {
      setFieldValue('coordinatesx', '');
      setFieldValue('coordinatesy', '');
    }
  };

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="locationcard"
        id="locationcard"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#location"
            aria-expanded="true"
            aria-controls="location"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-geo-alt-fill col-1"
                style={{ fontSize: '20px' }}
              />
              <div className="fs-5 col px-3 text-start">Location</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="location">
          <div className="card-body">
            <div className="form-group row my-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end">
                  <label
                    className="col-form-label col px-1 me-4"
                    htmlFor="loactionInput"
                  >
                    Select One
                    <span className="required text-danger ">*</span>
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className=" pe-3">
                  <select
                    onChange={handleSelectInput}
                    name="loactionInput"
                    id="loactionInput"
                    className="form-select rounded-0"
                  >
                    {options?.map((option) => {
                      return (
                        <React.Fragment key={option.value}>
                          <option value={option.value}>
                            {option.label.charAt(0).toUpperCase() +
                              option.label.slice(1)}
                          </option>
                        </React.Fragment>
                      );
                    })}
                  </select>
                </div>
              </div>
            </div>
            {urlOrCoordinate === 'coordinates' && (
              <>
                <CoordinateInput />
              </>
            )}

            {urlOrCoordinate === 'url' && (
              <>
                <div className="form-group row my-3">
                  <div className="col-md-3">
                    <div className="label-wrapper row row-cols-auto float-md-end">
                      <label
                        className="col-form-label col px-1 me-4"
                        htmlFor="urlInput"
                      >
                        Enter an URL
                        <span className="required text-danger ">*</span>
                      </label>
                    </div>
                  </div>
                  <div className="col-md-9">
                    <div className="input-group pe-3 d-flex flex-wrap">
                      <input
                        id="urlInput"
                        className="border-bottom form-control rounded-0 border-3 border border-0 shadow-none"
                        type="text"
                      />
                      <p
                        onClick={URLtoLatLng}
                        className="ms-4 btn btn-primary rounded px-3"
                      >
                        Enter
                      </p>
                    </div>
                  </div>
                </div>
                <CoordinateInput />
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default Location;
