import { ErrorMessage, Field } from 'formik';

const CoordinateInput = () => {
  return (
    <>
      <div>
        <div className="form-group row my-2">
          <div className="col-md-3">
            <div className="label-wrapper row row-cols-auto float-md-end">
              <label className="col-form-label col px-1" htmlFor="coordinatesx">
                Co-ordinates (x)
                <span className="required text-danger ">*</span>
              </label>
            </div>
          </div>
          <div className="col-md-9">
            <div className="input-group pe-3">
              <Field
                className="border-bottom form-control rounded-0 border-3 border border-0 shadow-none"
                id="coordinatesx"
                name="coordinatesx"
                type="text"
              />
            </div>
            <div className="errMsg text-danger text-red-600">
              <ErrorMessage name="coordinatesx" />
            </div>
          </div>
        </div>
        <div className="form-group row my-2">
          <div className="col-md-3">
            <div className="label-wrapper row row-cols-auto float-md-end">
              <label className="col-form-label col px-1" htmlFor="coordinatesy">
                Co-ordinates (y)
                <span className="required text-danger ">*</span>
              </label>
            </div>
          </div>
          <div className="col-md-9">
            <div className="input-group pe-3">
              <Field
                className="border-bottom form-control rounded-0 border-3 border border-0 shadow-none"
                id="coordinatesy"
                name="coordinatesy"
                type="text"
              />
            </div>
            <div className="errMsg text-danger text-red-600">
              <ErrorMessage name="coordinatesy" />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CoordinateInput;
