import { Form, Formik } from 'formik';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';
import { toast } from 'react-toastify';

import { userAPI } from '@/APIs';

import { Club, ClubLocationType } from 'models';
import Link from 'next/link';
import { createClubSchema } from '../schema';
import Address from './forms/address';
import ClubInfo from './forms/clubInfo';
import Location from './forms/location';
import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { enableEdit, showModal } from '@/toolkit/modalSlice';

interface Props {
  club: Club;
}

const EditClub: FC<Props> = (props) => {
  const router = useRouter();
  const [club, setClub] = useState(props.club);
  const dispatch = useAppDispatch();
  const edit = useAppSelector((state) => state.persistedReducer.modal.edit);
  const [updateData, setUpdateData] = useState<Club>();

  const handleSubmit = async (data: Club) => {
    try {
      const res = await userAPI.updateClub(data);
      if (!res.data) {
        if ('error' in res) {
          toast.error(`Can't Update Club`);
        }
      } else {
        router.push('/clubManagement/clubs');
        toast.success('Club Updated Successfully');
      }
    } catch (error: any) {
      toast.error(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  if (edit === true) {
    handleSubmit(updateData!);
    dispatch(enableEdit(false));
  }

  return (
    <>
      {club ? (
        <Formik
          initialValues={{
            name: club?.name || '',
            description: club?.description || '',
            cover: club?.image?.cover || '',
            logo: club?.image?.logo || '',
            addressLine1: club?.address?.addressLine1 || '',
            addressLine2: club?.address?.addressLine2 || '',
            city: club?.address?.city || '',
            country: club?.address?.country || '',
            postCode: club?.address?.postCode || '',
            coordinatesx: club?.location?.coordinates[0] || '',
            coordinatesy: club?.location?.coordinates[1] || '',
          }}
          onSubmit={(values, actions) => {
            const data = {
              id: club?.id!,
              name: values.name,
              image: {
                cover: values.cover,
                logo: values.logo,
              },
              location: {
                type: ClubLocationType.POINT,
                coordinates: [
                  parseFloat(values.coordinatesy as string),
                  parseFloat(values.coordinatesx as string),
                ],
              },
              address: {
                addressLine1: values.addressLine1,
                addressLine2: values.addressLine2,
                city: values.city,
                country: values.country,
                postCode: values.postCode,
              },
              description: values.description,
            };
            dispatch(showModal(true));
            setUpdateData(data);
            actions.setSubmitting(false);
          }}
          validationSchema={createClubSchema}
        >
          {(formikprops) => {
            return (
              <Form
                onSubmit={formikprops.handleSubmit}
                onKeyDown={onKeyDown}
                className="min-vh-100"
              >
                <div className="content-header clearfix pt-4">
                  <h1 className="float-start">
                    Edit Club details
                    <span className="fs-5 p-3">
                      <Link
                        href="/clubManagement/clubs"
                        className="text-decoration-none "
                      >
                        <i className="bi bi-arrow-left-circle-fill p-2" />
                        Back to club list
                      </Link>
                    </span>
                  </h1>
                  <div className="float-end">
                    <button
                      type="submit"
                      name="save"
                      className="btn btn-primary m-1"
                    >
                      <i className="bi bi-save" />
                      <p className="float-end mx-1 my-0">Save </p>
                    </button>
                  </div>
                </div>
                <div className="mt-4 pb-5">
                  <ClubInfo
                    setFieldValue={formikprops.setFieldValue}
                    club={club}
                  />
                  <Address />
                  <Location setFieldValue={formikprops.setFieldValue} />
                </div>
              </Form>
            );
          }}
        </Formik>
      ) : (
        'Something went wrong!'
      )}
    </>
  );
};

export default EditClub;
