import Link from 'next/link';
import { useRouter } from 'next/router';
import ListBlock from './ListBlock';

const ClubMembers = () => {
  const router = useRouter();
  const id = router.query.id! as string;
  return (
    <>
      <main>
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="d-flex align-items-center">
            <div className="fs-2 me-2">Club Members</div>
            <Link
              href="/clubManagement/clubs"
              className="text-decoration-none mt-2"
            >
              <i className="bi bi-arrow-left-circle-fill p-2" />
              <span>Back to Club list</span>
            </Link>
          </div>
          <Link
            href= {{
              pathname: "/clubManagement/[id]/members/addMember",
              query: { id }
            }}
            className="text-decoration-none mt-2"
          >
            <button className="btn btn-primary p-2 mt-3">Add New Member</button>
          </Link>
        </div>
        <br />
        <div className="d-flex flex-wrap flex-column">
          <ListBlock
            id={id}
            label="View Active Member List"
            extraClass="btn-outline-primary"
            membeStatus="activeMembers"
          />

          <ListBlock
            id={id}
            label="View Pending Member Join Requests"
            extraClass="btn-outline-success"
            membeStatus="pendingMembers"
          />

          <ListBlock
            id={id}
            label="View Expired Member List"
            extraClass="btn-outline-warning"
            membeStatus="expiredMembers"
          />

          <ListBlock
            id={id}
            label="View Blocked Member List"
            extraClass="btn-outline-danger"
            membeStatus="blockedMembers"
          />
        </div>
      </main>
    </>
  );
};

export default ClubMembers;
