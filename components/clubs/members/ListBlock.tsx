import Link from "next/link";

interface Props {
  label: string;
  extraClass: string;
  membeStatus: string;
  id: string;
}
const ListBlock: React.FC<Props> = ({ label, extraClass, membeStatus, id }) => {
  return (
    <>
      <Link
        href={{
          pathname: `/clubManagement/[id]/members/${membeStatus}`,
          query: { id },
        }}
        className={`p-4 btn ${extraClass}`}
      >
        {label}
      </Link>
      <br />
    </>
  );
};

export default ListBlock;
