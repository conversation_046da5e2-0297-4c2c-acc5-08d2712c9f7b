import Link from 'next/link';
import { FC, useEffect, useMemo, useState } from 'react';

import Table from '@/components/global/table/table';
import Pagination from '@/components/global/pagination';
import { useRouter } from 'next/router';
import { ClubMember } from 'models';

interface Props {
  members: ClubMember[];
  columns: any;
  title: string;
  setMembers: Function;
}

const List: FC<Props> = ({ members, columns, title }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [PageSize, setPageSize] = useState(7);
  const [memberID, setMemberID] = useState('');
  const router = useRouter();
  const clubId = router.query.id;

  console.log(members)

  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const currentTableData = useMemo(() => {
    const firstPageIndex = (currentPage - 1) * PageSize;
    const lastPageIndex = firstPageIndex + PageSize;
    return members?.slice(firstPageIndex, lastPageIndex);
  }, [currentPage, PageSize, members]);

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <div className="d-flex justify-content-between align-items-center mt-3">
            <div className="fs-2">{title}</div>
            <Link
              href={{
                pathname: `/clubManagement/[id]/members`,
                query: { id: clubId },
              }}
              className="text-decoration-none"
            >
              <i className="bi bi-arrow-left-circle-fill p-2" />
              <span>Back to members dashboard</span>
            </Link>
          </div>
          <br />
          {members?.length > 0 ? (
            <>
              <Table
                items={currentTableData}
                columns={columns}
                onClickForSort={onClickForSort}
              />
              <div className="">
                {members?.length > 1 ? (
                  <Pagination
                    currentPage={currentPage}
                    totalCount={members.length}
                    pageSize={PageSize}
                    setCurrentPage={setCurrentPage}
                    setPageSize={setPageSize}
                  />
                ) : null}
              </div>
            </>
          ) : <>
            <p>No members to show</p>
          </>}
        </div>
      </div>
    </>
  );
};

export default List;
