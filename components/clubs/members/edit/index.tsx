import { userAP<PERSON> } from '@/APIs';
import { Form, Formik } from 'formik';
import { ClubMember } from 'models';
import moment from 'moment';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { toast } from 'react-toastify';
import FieldTemplate from './fieldTemplate';

interface Props {
  member: ClubMember;
}

interface FromData {
  id: string;
  name: string;
  status: string;
  joiningDate: string;
  membershipExpirationDate?: string;
}

const EditMemberDetails: React.FC<Props> = ({ member }) => {
  const router = useRouter();
  const id = router.query.id!;
  const memberid = router.query.memberid!;
  const hrefLink =
    member?.status === 'PENDING'
      ? '/clubManagement/[id]/members/pendingMembers'
      : member?.status === 'ACTIVE'
      ? '/clubManagement/[id]/members/activeMembers'
      : member?.status === 'EXPIRED'
      ? '/clubManagement/[id]/members/expiredMembers'
      : '/clubManagement/[id]/members/blockedMembers';
  let joiningDateInput = '';
  let membershipExpirationDateInput;
  let date;

  const [page, setPage] = useState(
    member.status === 'PENDING'
      ? 'pending'
      : member.status === 'ACTIVE'
      ? 'active'
      : member.status === 'EXPIRED'
      ? 'expired'
      : 'blocked'
  );

  if (member?.status !== 'PENDING') {
    date = new Date(member?.createdAt!);
    date.setMinutes(date.getMinutes() - date.getTimezoneOffset());
    joiningDateInput = date?.toISOString().slice(0, 16);

    date = new Date(member?.expireDate!);
    date.setMinutes(date.getMinutes() - date.getTimezoneOffset());
    membershipExpirationDateInput = date.toISOString().slice(0, 16);
  }

  const handleEditMemberDetails = async (memberDetails: FromData) => {
    try {
      const res = await userAPI.updateClubMember(
        member.id,
        memberDetails.membershipExpirationDate
      );
      if (!res.data) {
        toast.error('Can not update member');
      } else {
        if (page === 'pending')
          router.push(`/clubManagement/${id}/members/pendingMembers`);
        else if (page === 'active')
          router.push(`/clubManagement/${id}/members/activeMembers`);
        else if (page === 'expired')
          router.push(`/clubManagement/${id}/members/expiredMembers`);
        else router.push(`/clubManagement/${id}/members/blockedMembers`);

        toast.success('Member updated successfully');
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          id: member?.userId,
          name: member?.userName,
          status: member?.status,
          joiningDate:
            member?.status === 'PENDING'
              ? moment().utc().local().format()
              : joiningDateInput,
          membershipExpirationDate:
            member?.status === 'PENDING'
              ? moment().utc().local().format()
              : membershipExpirationDateInput,
        }}
        onSubmit={(values, actions) => {
          handleEditMemberDetails(values);
          actions.setSubmitting(false);
        }}
      >
        {(formikprops) => {
          return (
            <Form
              onSubmit={formikprops.handleSubmit}
              onKeyDown={onKeyDown}
              className="min-vh-100"
            >
              <div className="content-header clearfix pt-4">
                <h1 className="float-start">
                  Club Member details
                  <span className="fs-5 p-3">
                    <Link
                      href={{
                        pathname: hrefLink,
                        query: { id },
                      }}
                      className="text-decoration-none "
                    >
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      Back to member list
                    </Link>
                  </span>
                </h1>
                <div className="float-end">
                  <button
                    type="submit"
                    name="save"
                    className="btn btn-primary m-1"
                  >
                    <i className="bi bi-save" />
                    <p className="float-end mx-1 my-0">Save </p>
                  </button>
                </div>
              </div>
              <div
                className="card card-secondary card-outline my-4"
                data-card-name="member-details"
                id="member-details"
              >
                <div className="card-header with-border d-flex justify-content-between align-items-center">
                  <div className="card-title row align-items-center ps-2 pt-2">
                    <i
                      className="bi bi-person-fill col-1"
                      style={{ fontSize: '20px' }}
                    />
                    <div className="fs-5 col text-start px-3">
                      Member Details
                    </div>
                  </div>
                </div>
                <div className="" id="memberDetailsTab">
                  <div className="card-body">
                    <FieldTemplate
                      id="id"
                      label="Member ID"
                      type="text"
                      disabled={true}
                    />
                    <FieldTemplate
                      id="name"
                      label="Name"
                      type="text"
                      disabled={true}
                    />
                    <FieldTemplate
                      id="status"
                      label="Status"
                      as="select"
                      options={['Choose...', 'active', 'expired', 'pending']}
                    />
                    <FieldTemplate
                      id="joiningDate"
                      label="Joining Date"
                      type="datetime-local"
                    />
                    <FieldTemplate
                      id="membershipExpirationDate"
                      label="Membership Expiration Date"
                      type="datetime-local"
                    />
                  </div>
                </div>
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default EditMemberDetails;
