import { Field } from 'formik';
import React from 'react';

interface Props {
  id: string;
  label: string;
  required?: boolean;
  type?: string;
  disabled?: boolean;
  options?: string[];
  as?: string;
}

const FieldTemplate: React.FC<Props> = (props) => {
  const { id, label, required, type, options, disabled, as } = props;
  return (
    <>
      <div className="form-group row my-3">
        <div className="col-md-3">
          <div className="label-wrapper row row-cols-auto float-md-end">
            <label className="col-form-label col px-1 me-4" htmlFor={id}>
              {label}
              {required && <span className="required text-danger ">*</span>}
            </label>
          </div>
        </div>
        <div className="col-md-9">
          <div className="input-group pe-3">
            {as !== 'select' ? (
              <Field
                className="form-control rounded-0 border-2 border shadow-none"
                id={id}
                name={id}
                type={type}
                disabled={disabled}
              />
            ) : (
              <>
                <Field
                  as={as}
                  name={id}
                  id={id}
                  className="form-select rounded-0"
                >
                  {options?.map((option) => {
                    return (
                      <React.Fragment key={option}>
                        <option value={option}>
                          {option.charAt(0).toUpperCase() + option.slice(1)}
                        </option>
                      </React.Fragment>
                    );
                  })}
                </Field>
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default FieldTemplate;
