import { userAPI } from '@/APIs';
import { Form, Formik } from 'formik';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import FieldTemplate from './fieldTemplate';

interface FormData {
  userId: string;
  clubId: string;
  date: string;
}

const AddMemberForm: React.FC = () => {
  const router = useRouter();
  const id = router.query.id! as string;
  const [ready, setReady] = useState(false);

  const handleAddClubMember = async ({ userId, clubId, date }: FormData) => {
    try {
      const res = await userAPI.addClubMember(clubId, userId, date);
      if (res.data) {
        router.push(`/clubManagement/${id}/members`);
        toast.success('Member added successfully');
      } else {
        toast.error('Can not add user');
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
    }
  }, [router.isReady]);

  if (!ready) {
    return null;
  }

  return (
    <>
      <Formik
        initialValues={{
          userId: '',
          clubId: id,
          date: '',
        }}
        onSubmit={(values, actions) => {
          handleAddClubMember(values);
          actions.setSubmitting(false);
        }}
      >
        {(formikprops) => {
          return (
            <Form
              onSubmit={formikprops.handleSubmit}
              onKeyDown={onKeyDown}
              className="min-vh-100"
            >
              <div className="content-header clearfix pt-4">
                <h1 className="float-start">
                  Add a member
                  <span className="fs-5 p-3">
                    <Link
                      href={{
                        pathname: '/clubManagement/[id]/members',
                        query: { id },
                      }}
                      className="text-decoration-none "
                    >
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      <small>Back to member dashboard</small>
                    </Link>
                  </span>
                </h1>
                <div className="float-end">
                  <button
                    type="submit"
                    name="save"
                    className="btn btn-primary m-1"
                  >
                    <i className="bi bi-save" />
                    <p className="float-end mx-1 my-0">Add </p>
                  </button>
                </div>
              </div>
              <div
                className="card card-secondary card-outline my-4"
                data-card-name="add-details"
                id="add-details"
              >
                <div className="card-header with-border d-flex justify-content-between align-items-center">
                  <div className="card-title row align-items-center ps-2 pt-2">
                    <i
                      className="bi bi-person-fill col-1"
                      style={{ fontSize: '20px' }}
                    />
                    <div className="fs-5 col text-start px-3">Add Details</div>
                  </div>
                </div>
                <div className="" id="addDetailsTab">
                  <div className="card-body">
                    <FieldTemplate id="userId" label="Member ID" type="text" />
                    <FieldTemplate
                      id="clubId"
                      label="Club ID"
                      type="text"
                      disabled={true}
                    />
                    <FieldTemplate
                      id="date"
                      label="Membership Expiration Date"
                      type="datetime-local"
                    />
                  </div>
                </div>
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default AddMemberForm;
