import { userAPI } from '@/APIs';
import { ClubMember } from 'models';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useEffect, useMemo, useState } from 'react';
import { toast } from 'react-toastify';
import List from '../List';

const PendingMemberList: FC = () => {
  const [memberID, setMemberID] = useState('');
  const [members, setMembers] = useState<ClubMember[]>();
  const [modal, setModal] = useState({
    delete: false,
  });
  const router = useRouter();
  const [ready, setReady] = useState(false);
  const clubId = router.query.id! as string;

  const onChangeForList = async () => {
    const res = await userAPI.getClubMembersByStatus(clubId, 'PENDING');
    setMembers(res.data);
  };

  const deleteMemberFunction = async () => {
    try {
      const res = await userAPI.deleteClubMember(clubId, memberID);
      if (res.data) {
        onChangeForList();
        toast.success('Member deleted successfully');
      } else {
        toast.error('Can not delete member');
      }
      setModal({
        ...modal,
        delete: false,
      });
    } catch (error) {
      console.log(error);
    }
  };

  const onClickForDelete = (id: string) => {
    setMemberID(id);
    setModal({ ...modal, delete: true });
  };

  const columns = [
    {
      label: 'ID',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Member ID',
      path: 'userId',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Name',
      path: 'userName',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Status',
      path: 'status',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Edit',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/clubManagement/${clubId}/members/pendingMembers/[memberid]`,
              query: { memberid: data?.[key] },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-info">
              <span>
                <i className="bi bi-pencil me-2 align-middle"></i>
              </span>
              Edit
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Delete Request',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <button
            className="btn btn-default btn-outline-danger"
            onClick={() => onClickForDelete(data[key])}
          >
            <i className="bi bi-trash3-fill me-2 align-middle"></i>
            Delete
          </button>
        </td>
      ),
    },
  ];

  const getPendingMembers = async () => {
    try {
      const pendingMembers = await userAPI.getClubMembersByStatus(
        clubId,
        'PENDING'
      );
      console.log(pendingMembers);
      if ('data' in pendingMembers) setMembers(pendingMembers.data);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getPendingMembers();
    }
  }, [router.isReady]);

  if (!ready) {
    return null;
  }

  return (
    <>
      <List
        members={members!}
        title="Pending Member Join Requests"
        columns={columns}
        setMembers={setMembers}
      />
      {modal.delete ? (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              // close modal when outside of modal is clicked
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                // do not close modal if anything inside modal content is clicked
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this item?</p>
                <br />

                <div className="clearfix">
                  <button
                    type="button"
                    className="btn btn-secondary me-2"
                    onClick={() =>
                      setModal({
                        ...modal,
                        delete: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => deleteMemberFunction()}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default PendingMemberList;
