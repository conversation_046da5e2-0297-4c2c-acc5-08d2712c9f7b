import Link from 'next/link';
import { FC, useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import { ClubMember } from 'models';
import moment from 'moment';
import { useRouter } from 'next/router';
import { toast } from 'react-toastify';
import List from '../List';

const ActiveMemberList: FC = () => {
  const [memberID, setMemberID] = useState('');
  const [members, setMembers] = useState<ClubMember[]>();
  const [modal, setModal] = useState({
    delete: false,
    block: false,
  });

  const router = useRouter();
  const [ready, setReady] = useState(false);

  const clubId = router.query.id as string;

  const onChangeForList = async () => {
    const res = await userAPI.getClubMembersByStatus(clubId, 'ACTIVE');
    setMembers(res.data);
  };

  const onClickForDelete = (id: string) => {
    setMemberID(id);
    setModal({ ...modal, delete: true });
  };

  const onClickForBlock = (id: string) => {
    setMemberID(id);
    setModal({ ...modal, block: true });
  };

  const deleteMemberFunction = async () => {
    try {
      const res = await userAPI.deleteClubMember(clubId, memberID);
      if (res.data) {
        onChangeForList();
        toast.success('Member deleted successfully');
      } else {
        toast.error('Can not delete member');
      }
      setModal({
        ...modal,
        delete: false,
      });
    } catch (error) {
      console.log(error);
    }
  };

  const blockMemberFunction = async () => {
    try {
      const res = await userAPI.blockClubMember(clubId, memberID);
      if (res.data) {
        onChangeForList();
        toast.success('Member blocked');
      } else {
        toast.error('Can not block member');
      }
      setModal({
        ...modal,
        block: false,
      });
    } catch (error) {
      console.log(error);
    }
  };

  const columns = [
    {
      label: 'ID',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Member ID',
      path: 'userId',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Name',
      path: 'userName',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Status',
      path: 'status',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Joining Date',
      path: 'createdAt',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {moment(data[key]).utc().local().format('lll')}
        </td>
      ),
    },
    {
      label: 'Membership Expiration Date',
      path: 'expireDate',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {moment(data[key]).utc().local().format('lll')}
        </td>
      ),
    },
    {
      label: 'Edit',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/clubManagement/${clubId}/members/activeMembers/[memberid]`,
              query: { memberid: data[key] },
            }}
            passHref
          >
            <button className="btn btn-default btn-outline-info">
              <span>
                <i className="bi bi-pencil me-2 align-middle"></i>
              </span>
              Edit
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Remove User',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <button
            className="btn btn-default btn-outline-warning"
            onClick={() => onClickForDelete(data[key])}
          >
            <i className="bi bi-trash3-fill me-2 align-middle"></i>
            Remove
          </button>
        </td>
      ),
    },
    {
      label: 'Block User',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <button
            className="btn btn-default btn-outline-danger"
            onClick={() => onClickForBlock(data[key])}
          >
            <i className="bi bi-slash-circle-fill me-2 align-middle"></i>
            Block
          </button>
        </td>
      ),
    },
  ];

  const getActiveMembers = async () => {
    try {
      const activeMembers = await userAPI.getClubMembersByStatus(
        clubId,
        'ACTIVE'
      );
      console.log(activeMembers);
      if ('data' in activeMembers) setMembers(activeMembers.data);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getActiveMembers();
    }
  }, [router.isReady]);

  if (!ready) {
    return null;
  }

  return (
    <>
      <List
        members={members!}
        title="Active Members"
        columns={columns}
        setMembers={setMembers}
      />
      {modal.delete ? (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              // close modal when outside of modal is clicked
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                // do not close modal if anything inside modal content is clicked
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this user?</p>
                <br />

                <div className="clearfix">
                  <button
                    type="button"
                    className="btn btn-secondary me-2"
                    onClick={() =>
                      setModal({
                        ...modal,
                        delete: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => deleteMemberFunction()}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}

      {modal.block ? (
        <div
          className="modal"
          style={{ display: modal.block ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              // close modal when outside of modal is clicked
              setModal({ ...modal, block: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                // do not close modal if anything inside modal content is clicked
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to block this user?</p>
                <br />

                <div className="clearfix">
                  <button
                    type="button"
                    className="btn btn-secondary me-2"
                    onClick={() =>
                      setModal({
                        ...modal,
                        block: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => blockMemberFunction()}
                  >
                    Block
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default ActiveMemberList;
