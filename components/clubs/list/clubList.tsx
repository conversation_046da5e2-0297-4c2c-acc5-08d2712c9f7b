import Link from 'next/link';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import Table from '@/components/global/table/table';
import { config } from 'config';
import myImageLoader from 'image/loader';
import { Club } from 'models';
import Image from 'next/image';
import { handlePagination } from 'utils/handlePagination';
import { toast } from 'react-toastify';

interface Props {
  clubs: Club[];
  setClubs: Function;
  setSkip: Function;
  skip: number;
  // showSeeMore: boolean;
  // setLoadData: Function;
  // loadData: boolean;
  // setShowSeeMore: Function;
}

const ClubList: FC<Props> = ({
  clubs,
  setClubs,
  setSkip,
  skip,
  // showSeeMore,
  // setLoadData,
  // loadData,
  // setShowSeeMore,
}) => {
  const [ClubID, setClubID] = useState('');
  const [modal, setModal] = useState({
    delete: false,
  });

  const onChangeForList = async (skip: number) => {
    try {
      const clubList = await userAPI.getClubs(skip, config.limit);
      if ('data' in clubList) {
        setClubs(clubList.data);
        setSkip(0);
      } else {
        toast.error(clubList.error.message);
      }
    } catch (error) {}
  };

  const deleteClubFunction = async () => {
    try {
      const res = await userAPI.deleteClub(ClubID);
      if ('data' in res) {
        onChangeForList(0);
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
    setModal({
      ...modal,
      delete: false,
    });
  };

  const onClickForDelete = (id: string) => {
    setClubID(id);
    setModal({ ...modal, delete: true });
  };

  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const columns = [
    {
      label: 'Picture',
      path: 'image',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.image && (
            <Image
              loader={myImageLoader}
              src={`${data[key].cover}`}
              height={75}
              width={75}
              alt="..."
            />
          )}
        </td>
      ),
    },
    {
      label: 'Name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    // {
    //   label: 'View Members',
    //   path: 'members',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="text-center align-middle">
    //       <Link
    //         href={{
    //           pathname: `/clubManagement/[id]/members`,
    //           query: { id: data?.id },
    //         }}
    //         passHref
    //         legacyBehavior
    //       >
    //         <button className="btn btn-default btn-outline-info">View</button>
    //       </Link>
    //     </td>
    //   ),
    // },
    {
      label: 'Edit',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/clubManagement/edit/[id]`,
              query: { id: data?.[key] },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-info">
              <span>
                <i className="bi bi-pencil me-2 align-middle"></i>
              </span>
              Edit
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Club Details',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/clubManagement/view/[id]`,
              query: { id: data?.id },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Delete',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <button
            className="btn btn-default btn-outline-danger"
            onClick={() => onClickForDelete(data[key])}
          >
            <i className="bi bi-trash3-fill me-2 align-middle"></i>
            Delete
          </button>
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={clubs}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>
      {modal.delete ? (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              // close modal when outside of modal is clicked
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                // do not close modal if anything inside modal content is clicked
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this item?</p>
                <br />

                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{
                      border: '1px solid gray',
                      backgroundColor: 'gray',
                      color: 'white',
                      marginRight: '10px',
                    }}
                    onClick={() =>
                      setModal({
                        ...modal,
                        delete: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => deleteClubFunction()}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default ClubList;
