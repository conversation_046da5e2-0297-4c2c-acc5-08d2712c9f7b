import { string, object, number } from 'yup';
import XRegExp from 'xregexp';
import * as Yup from 'yup';

export const createClubSchema = object().shape({
  name: string()
    .min(2, 'This field must be at least 2 characters long')
    .max(50, 'This field must be at most 50 characters long')
    .required('This field must not be empty'),
  description: string()
    .min(2, 'This field must be at least 2 characters long')
    .max(50, 'This field must be at most 50 characters long'),
  cover: string().required('This field must not be empty'),
  logo: string().required('This field must not be empty'),
  addressLine1: string().required('This field must not be empty'),
  addressLine2: string().required('This field must not be empty'),
  city: string().required('This field must not be empty'),
  country: string().required('This field must not be empty'),
  postCode: number().required('This field must not be empty'),
  coordinatesx: number().required('This field must not be empty'),
  coordinatesy: number().required('This field must not be empty')
});
