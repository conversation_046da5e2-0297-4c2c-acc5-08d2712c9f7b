import { FC } from 'react';

import SingleView from '@/components/common/singleView';
import { Club } from 'models';
interface ClubProps {
  club: Club;
}

const ClubAddressCard: FC<ClubProps> = ({ club }) => {
  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="club-address"
        id="club-address"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <div className="card-title row align-items-center ps-2 pt-2">
            <i
              className="bi bi-house-door-fill col-1"
              style={{ fontSize: '20px' }}
            />
            <div className="fs-5 col text-start px-3">Club address</div>
          </div>
        </div>
        <div className="" id="clubAddressTab">
          <div className="card-body">
            <SingleView
              label="Address Line 1"
              value={club.address?.addressLine1}
            />
            <SingleView
              label="Address Line 2"
              value={club.address?.addressLine2}
            />
            <SingleView label="City" value={club.address?.city} />
            <SingleView label="Country" value={club.address?.country} />
            <SingleView label="Postal Code" value={club.address?.postCode} />
          </div>
        </div>
      </div>
    </>
  );
};

export default ClubAddressCard;
