import { FC } from 'react';

import SingleView from '@/components/common/singleView';
import { Club } from 'models';
interface ClubProps {
  club: Club;
}

const ClubInfoCard: FC<ClubProps> = ({ club }) => {
  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="club-info"
        id="club-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <div className="card-title row align-items-center ps-2 pt-2">
            <i
              className="bi bi-info-lg col-1 align-text-top"
              style={{ fontSize: '25px' }}
            />
            <div className="fs-5 col text-start px-3">Club info</div>
          </div>
        </div>
        <div className="" id="clubInfoTab">
          <div className="card-body">
            <SingleView label="Club name" value={club.name} />
            <SingleView label="Description" value={club.description} />
            <SingleView
              label="Cover picture"
              isImage={true}
              attachment={club.image.cover!}
            />
            <SingleView
              label="Logo"
              isImage={true}
              attachment={club.image.logo!}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default ClubInfoCard;
