import { FC } from 'react';

import SingleView from '@/components/common/singleView';
import { Club } from 'models';

interface ClubProps {
  club: Club;
}

const ClubLocationCard: FC<ClubProps> = ({ club }) => {
  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="club-location"
        id="club-location"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <div className="card-title row align-items-center ps-2 pt-2">
            <i
              className="bi bi-geo-alt-fill col-1 align-text-top"
              style={{ fontSize: '20px' }}
            />
            <div className="fs-5 col text-start px-3">Club location</div>
          </div>
        </div>
        <div className="" id="clubLocationTab">
          <div className="card-body">
            <SingleView
              label="Co-ordinates (x)"
              value={club.location.coordinates[0]}
            />
            <SingleView
              label="Co-ordinates (y)"
              value={club.location.coordinates[1]}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default ClubLocationCard;
