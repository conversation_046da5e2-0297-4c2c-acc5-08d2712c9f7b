import Link from 'next/link';
import ClubAddressCard from './cards/clubAddress';
import ClubInfoCard from './cards/clubInfo';
import ClubLocationCard from './cards/clubLocation';
import { Club } from 'models';
interface ClubProps {
  club: Club;
}

const ViewClub: React.FC<ClubProps> = ({ club }) => {
  return <>
    {club ? (
      <div>
        <div className="content-header clearfix mt-3">
          <h1 className="float-start">
            View club details
            <span className="fs-5 p-3">
              <Link href="/clubManagement/clubs" className="text-decoration-none ">

                <i className="bi bi-arrow-left-circle-fill p-2" />Back to club list
              </Link>
            </span>
          </h1>
        </div>

        <div className="mt-4">
          <ClubInfoCard club={club} />
          <ClubAddressCard club={club} />
          <ClubLocationCard club={club} />
        </div>
      </div>
    ) : (
      ''
    )}
  </>;
};

export default ViewClub;
