import myImageLoader from 'image/loader';
import { GetCoachProfile } from 'models';
import Image from 'next/image';
import Link from 'next/link';
import { FC } from 'react';

const isImageFile = (url: string) => {
  const ext = url.split('.').pop()?.toLowerCase();
  return ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext || '');
};

const isPdfFile = (url: string) => {
  return url.toLowerCase().endsWith('.pdf');
};

const getFileIcon = (url: string) => {
  const ext = url.split('.').pop()?.toLowerCase();
  if (isPdfFile(url)) return 'bi-file-pdf';
  if (['doc', 'docx'].includes(ext || '')) return 'bi-file-word';
  if (['xls', 'xlsx'].includes(ext || '')) return 'bi-file-excel';
  return 'bi-file-text';
};

const MediaItem: FC<{
  url: string;
  index: number;
  type: 'media' | 'credential' | 'identification';
}> = ({ url, index, type }) => {
  if (isImageFile(url)) {
    return (
      <Image
        loader={myImageLoader}
        src={url}
        alt={`${type} ${index + 1}`}
        width={200}
        height={200}
        className="img-fluid rounded"
        style={{ objectFit: 'cover' }}
      />
    );
  }

  return (
    <a
      href={url}
      target="_blank"
      rel="noopener noreferrer"
      className="d-flex flex-column align-items-center text-decoration-none"
    >
      <i className={`bi ${getFileIcon(url)} fs-1 text-primary`}></i>
      <span className="mt-2 text-break text-center">
        {type} {index + 1}
      </span>
    </a>
  );
};

const ViewCoach: FC<{ coach: GetCoachProfile }> = ({ coach }) => {
  return (
    <>
      {coach ? (
        <div>
          <div className="content-header clearfix mt-3">
            <h1 className="float-start fs-2">
              View Coach Profile
              <span className="fs-5 p-3">
                <Link href="/coach" className="text-decoration-none">
                  <i className="bi bi-arrow-left-circle-fill p-2" />
                  Back to coach list
                </Link>
              </span>
            </h1>
          </div>
          <div className="mt-4">
            <div className="card">
              <div className="card-body">
                <h5 className="card-title mb-4">Coach ID: {coach.id}</h5>
                <h5 className="card-title mb-4">User Email: {coach.userEmail}</h5>
                <div className="row">
                  <div className="col-md-6">
                    <p>
                      <strong>User Name:</strong> {coach.userName}
                    </p>
                    <p>
                      <strong>Legal Name:</strong> {coach.legalName}
                    </p>
                    <p>
                      <strong>Profile Name:</strong> {coach.profileName}
                    </p>
                    <p>
                      <strong>User Email:</strong> {coach.userEmail}
                    </p>
                    <p>
                      <strong>About:</strong> {coach.about}
                    </p>
                    <p>
                      <strong>Expertise:</strong> {coach.expertise}
                    </p>
                    <p>
                      <strong>Highlights:</strong> {coach.highlights}
                    </p>
                    <p>
                      <strong>Skill Level:</strong> {coach.skillLevel}
                    </p>
                    <p>
                      <strong>Experience:</strong> {coach.experienceInYear}{' '}
                      years
                    </p>
                    <p>
                      <strong>Review Status:</strong> {coach.reviewStatus}
                    </p>
                    <p>
                      <strong>Review Comment:</strong> {coach.reviewComment}
                    </p>
                    <div className="mb-3">
                      <strong>Category</strong>
                      <p>
                        <strong>Category Id:</strong> {coach.coachCategoryId}
                      </p>
                      <p>
                        <strong>Category Name:</strong>{coach.coachCategoryName}
                      </p>
                    </div>
                    <div className="mb-3">
                      <strong>Achievements:</strong>
                      <ul className="list-unstyled ms-3">
                        {coach.achievements.map((achievement, index) => (
                          <li key={index}>{achievement}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <h6 className="mb-3">Media</h6>
                    <div className="row g-2">
                      {coach.media && coach.media.length > 0 ? (
                        coach.media.map((media, index) => (
                          <div key={index} className="col-6">
                            <MediaItem
                              url={media.url}
                              index={index}
                              type="media"
                            />
                          </div>
                        ))
                      ) : (
                        <div className="col-12">No media available</div>
                      )}
                    </div>
                    <h6 className="mb-3 mt-4">Credentials</h6>
                    <div className="row g-2">
                      {coach.credentials && coach.credentials.length > 0 ? (
                        coach.credentials.map((credential, index) => (
                          <div key={index} className="col-6">
                            <MediaItem
                              url={credential.url}
                              index={index}
                              type="credential"
                            />
                          </div>
                        ))
                      ) : (
                        <div className="col-12">No credentials available</div>
                      )}
                    </div>

                    <h6 className="mb-3 mt-4">identifications</h6>
                    <div className="row g-2">
                      {coach.identifications && coach.identifications.length > 0 ? (
                        coach.identifications.map((identification, index) => (
                          <div key={index} className="col-6">
                            <MediaItem
                              url={identification.url}
                              index={index}
                              type="identification"
                            />
                          </div>
                        ))
                      ) : (
                        <div className="col-12">No identifications available</div>
                      )}
                    </div>

                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        'No coach data available'
      )}
    </>
  );
};

export default ViewCoach;
