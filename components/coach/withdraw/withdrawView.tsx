import { Withdraw } from 'models';
import Link from 'next/link';
import { FC } from 'react';

const ViewWithdraw: FC<{ withdraw: Withdraw }> = ({ withdraw }) => {
  return (
    <>
      {withdraw ? (
        <div>
          <div className="content-header clearfix mt-3">
            <h1 className="float-start fs-2">
              View Withdraw
              <span className="fs-5 p-3">
                <Link href="/coach/withdraw" className="text-decoration-none">
                  <i className="bi bi-arrow-left-circle-fill p-2" />
                  Back to withdraw list
                </Link>
              </span>
            </h1>
          </div>
          <div className="mt-4">
            <div className="card">
              <div className="card-body">
                <h5 className="card-title mb-4">Withdraw ID: {withdraw.id}</h5>
                <div className="row">
                  <div className="col-md-6">
                    <p>
                      <strong>Coach ID:</strong> {withdraw.coachId}
                    </p>
                    <p>
                      <strong>Coach Name:</strong> {withdraw.coachName}
                    </p>
                    <p>
                      <strong>Amount:</strong> {withdraw.moneyWithdraw}
                    </p>
                    <p>
                      <strong>Mobile Number:</strong> {withdraw.mobileNumber}
                    </p>
                    <p>
                      <strong>Review Status:</strong> {withdraw.reviewStatus}
                    </p>
                    <p>
                      <strong className="text-primary fw-bold">Coach Share:</strong> {withdraw.coachShare}
                    </p>
                    <p>
                      <strong>Fitsomnia Share:</strong> {withdraw.fitsomniaShare}
                    </p>
                    <p>
                      <strong>Review Comment:</strong> {withdraw.reviewComment}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        'No withdraw data available'
      )}
    </>
  );
};

export default ViewWithdraw;
