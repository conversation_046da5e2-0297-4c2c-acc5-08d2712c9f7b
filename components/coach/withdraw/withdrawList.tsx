import Table from '@/components/global/table/table';
import { getMultipleWithdrawRest } from 'APIs/restApi';
import { config } from 'config';
import { Withdraws } from 'models/coach/withdraw.interface';
import Link from 'next/link';
import { FC } from 'react';
import { toast } from 'react-toastify';

interface Props {
  withdrawList: Withdraws[];
  setWithdraws: Function;
  setSkip: Function;
  skip: number;
  reviewStatus: string;
}

const WithdrawList: FC<Props> = ({
  withdrawList,
  setWithdraws,
  setSkip,
  skip,
  reviewStatus,
}) => {


  const onChangeForList = async (skip: number) => {
    try {
      const withdrawsData = await getMultipleWithdrawRest(skip, config.limit, reviewStatus);
      if ('data' in withdrawsData) {
        setWithdraws(withdrawsData.data);
      } else {
        toast.error(withdrawsData.error?.message || 'Error fetching withdraws');
      }
    } catch (error) {
      console.error('Error fetching withdraws:', error);
    }
    setSkip(0);
  };



  const onClickForSort = (name: string) => {
    // Sorting functionality can be implemented here
  };

  const columns = [
    {
      label: 'Coach ID',
      path: 'coachId',
      content: (data: Withdraws) => (
        <td className="text-center align-middle">{data?.coachId || ''}</td>
      ),
    },
    {
      label: 'Mobile',
      path: 'mobileNumber',
      content: (data: Withdraws) => (
        <td className="text-center align-middle">{data?.mobileNumber || ''}</td>
      ),
    },
    {
      label: 'Amount',
      path: 'moneyWithdraw',
      content: (data: Withdraws) => (
        <td className="text-center align-middle">{data?.moneyWithdraw || 0}</td>
      ),
    },
    {
      label: 'Status',
      path: 'reviewStatus',
      content: (data: Withdraws) => (
        <td className="text-center align-middle">
          <span className={`badge ${data?.reviewStatus === 'approved' ? 'bg-success' : data?.reviewStatus === 'rejected' ? 'bg-danger' : 'bg-warning'}`}>
            {data?.reviewStatus || 'pending'}
          </span>
        </td>
      ),
    },
    {
      label: 'Comment',
      path: 'reviewComment',
      content: (data: Withdraws) => (
        <td className="text-center align-middle">
          {data?.reviewComment || ''}
        </td>
      ),
    },
    {
      label: 'View',
      path: 'id',
      content: (data: Withdraws) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/coach/withdraw/view/[id]`,
              query: { id: data.id },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },

  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={withdrawList}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>

    </>
  );
};

export default WithdrawList;