import { Refund } from 'models';
import Link from 'next/link';
import { FC } from 'react';

const ViewRefund: FC<{ refund: Refund }> = ({ refund }) => {
  return (
    <>
      {refund ? (
        <div>
          <div className="content-header clearfix mt-3">
            <h1 className="float-start fs-2">
              View Refund
              <span className="fs-5 p-3">
                <Link href="/coach/refund" className="text-decoration-none">
                  <i className="bi bi-arrow-left-circle-fill p-2" />
                  Back to refund list
                </Link>
              </span>
            </h1>
          </div>
          <div className="mt-4">
            <div className="card">
              <div className="card-body">
                <h5 className="card-title mb-4">Refund ID: {refund.id}</h5>
                <div className="row">
                  <div className="col-md-6">
                    <p>
                      <strong>User ID:</strong> {refund.userId}
                    </p>
                    <p>
                      <strong>Coach ID:</strong> {refund.coachId}
                    </p>
                    <p>
                      <strong>Program ID:</strong> {refund.programId}
                    </p>
                    <p>
                      <strong>Subscription ID:</strong> {refund.subscriptionId}
                    </p>
                    <p>
                      <strong>Request Reason:</strong> {refund.requestReason}
                    </p>
                    <p>
                      <strong>Contact Mobile Number:</strong> {refund.contactMobileNumber}
                    </p>
                    <p>
                      <strong>Contact Email:</strong> {refund.contactEmail}
                    </p>
                    <p>
                      <strong>Review Status:</strong> {refund.reviewStatus}
                    </p>
                    <p>
                      <strong>Review Comment:</strong> {refund.reviewComment}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        'No withdraw data available'
      )}
    </>
  );
};

export default ViewRefund;
