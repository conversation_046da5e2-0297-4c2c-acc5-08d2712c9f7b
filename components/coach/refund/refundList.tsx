import Table from '@/components/global/table/table';
import { approveRefundRest, getMultipleRefundRest, rejectRefundRest } from 'APIs/restApi';
import { config } from 'config';
import { Refunds } from 'models/coach/refund.interface';
import Link from 'next/link';
import { FC, useState } from 'react';
import { toast } from 'react-toastify';

interface Props {
  refundList: Refunds[];
  setRefunds: Function;
  setSkip: Function;
  skip: number;
  reviewStatus: string;
}

const RefundList: FC<Props> = ({
  refundList,
  setRefunds,
  setSkip,
  skip,
  reviewStatus,
}) => {
  const [refundId, setRefundId] = useState('');
  const [reviewComment, setReviewComment] = useState('');
  const [modal, setModal] = useState({
    approve: false,
    reject: false
  });

  const onChangeForList = async (skip: number) => {
    try {
      // Note: API needs to be updated to support pagination and filtering
      const refundsData = await getMultipleRefundRest(skip, config.limit, reviewStatus);
      if ('data' in refundsData) {
        setRefunds(refundsData.data);
      } else {
        toast.error(refundsData.error?.message || 'Error fetching refunds');
      }
    } catch (error) {
      console.error('Error fetching refunds:', error);
    }
    setSkip(0);
  };

  const onClickForApprove = async () => {
    if (!reviewComment.trim()) {
      toast.error('Review comment is required');
      return;
    }
    
    try {
      const res = await approveRefundRest(refundId, { reviewComment });
      if ('data' in res) {
        toast.success(res.data?.message || 'Refund request approved successfully');
        onChangeForList(0);
      } else {
        toast.error(res.error?.message || 'Error approving refund request');
      }
    } catch (error) {
      console.error('Error approving refund:', error);
    }
    
    setModal({
      ...modal,
      approve: false
    });
    setReviewComment('');
  };

  const onClickForReject = async () => {
    if (!reviewComment.trim()) {
      toast.error('Review comment is required');
      return;
    }
    
    try {
      const res = await rejectRefundRest(refundId, { reviewComment });
      if ('data' in res) {
        toast.success(res.data?.message || 'Refund request rejected successfully');
        onChangeForList(0);
      } else {
        toast.error(res.error?.message || 'Error rejecting refund request');
      }
    } catch (error) {
      console.error('Error rejecting refund:', error);
    }
    
    setModal({
      ...modal,
      reject: false
    });
    setReviewComment('');
  };

  const onClickForSort = (name: string) => {
    // Sorting functionality can be implemented here
  };

  const columns = [
    {
      label: 'User ID',
      path: 'userId',
      content: (data: Refunds) => (
        <td className="text-center align-middle">{data?.userId || ''}</td>
      ),
    },
    {
      label: 'Coach ID',
      path: 'coachId',
      content: (data: Refunds) => (
        <td className="text-center align-middle">{data?.coachId || ''}</td>
      ),
    },
    {
      label: 'Program ID',
      path: 'programId',
      content: (data: Refunds) => (
        <td className="text-center align-middle">{data?.programId || ''}</td>
      ),
    },
    {
      label: 'Mobile',
      path: 'contactMobileNumber',
      content: (data: Refunds) => (
        <td className="text-center align-middle">{data?.contactMobileNumber || ''}</td>
      ),
    },
    {
      label: 'Reason',
      path: 'requestReason',
      content: (data: Refunds) => (
        <td className="text-center align-middle">{data?.requestReason || ''}</td>
      ),
    },
    {
      label: 'Status',
      path: 'reviewStatus',
      content: (data: Refunds) => (
        <td className="text-center align-middle">
          <span className={`badge ${data?.reviewStatus === 'approved' ? 'bg-success' : data?.reviewStatus === 'rejected' ? 'bg-danger' : 'bg-warning'}`}>
            {data?.reviewStatus || 'pending'}
          </span>
        </td>
      ),
    },
    {
      label: 'Comment',
      path: 'reviewComment',
      content: (data: Refunds) => (
        <td className="text-center align-middle">
          {data?.reviewComment || ''}
        </td>
      ),
    },
    {
      label: 'View',
      path: 'id',
      content: (data: Refunds) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/coach/refund/view/[id]`,
              query: { id: data.id },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Actions',
      path: 'id',
      content: (data: Refunds) => (
        <td className="text-center align-middle">
          {data.reviewStatus === 'PENDING' && (
            <div className="d-flex justify-content-center gap-2">
              <button
                className="btn btn-default btn-outline-success"
                onClick={() => {
                  setRefundId(data.id);
                  setModal({ ...modal, approve: true });
                }}
              >
                <i className="bi bi-check-circle me-2 align-middle"></i>
                Approve
              </button>
              <button
                className="btn btn-default btn-outline-danger"
                onClick={() => {
                  setRefundId(data.id);
                  setModal({ ...modal, reject: true });
                }}
              >
                <i className="bi bi-x-circle me-2 align-middle"></i>
                Reject
              </button>
            </div>
          )}
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={refundList}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>

      {/* Approve Modal */}
      {modal.approve ? (
        <div
          className="modal"
          style={{ display: modal.approve ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              setModal({ ...modal, approve: false });
              setReviewComment('');
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '30%',
                marginLeft: '35%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h3>Approve Refund Request</h3>
                <hr />
                <div className="mb-3">
                  <label htmlFor="reviewComment" className="form-label">Review Comment</label>
                  <textarea
                    className="form-control"
                    id="reviewComment"
                    rows={3}
                    value={reviewComment}
                    onChange={(e) => setReviewComment(e.target.value)}
                    placeholder="Enter your comment for approval"
                  ></textarea>
                </div>
                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{ marginRight: '10px' }}
                    onClick={() => {
                      setModal({ ...modal, approve: false });
                      setReviewComment('');
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-success"
                    onClick={onClickForApprove}
                  >
                    Approve
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}

      {/* Reject Modal */}
      {modal.reject ? (
        <div
          className="modal"
          style={{ display: modal.reject ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              setModal({ ...modal, reject: false });
              setReviewComment('');
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '30%',
                marginLeft: '35%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h3>Reject Refund Request</h3>
                <hr />
                <div className="mb-3">
                  <label htmlFor="reviewComment" className="form-label">Review Comment</label>
                  <textarea
                    className="form-control"
                    id="reviewComment"
                    rows={3}
                    value={reviewComment}
                    onChange={(e) => setReviewComment(e.target.value)}
                    placeholder="Enter your reason for rejection"
                  ></textarea>
                </div>
                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{ marginRight: '10px' }}
                    onClick={() => {
                      setModal({ ...modal, reject: false });
                      setReviewComment('');
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={onClickForReject}
                  >
                    Reject
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default RefundList;