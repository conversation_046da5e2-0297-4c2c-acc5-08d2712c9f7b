import { userAPI } from '@/APIs';
import Table from '@/components/global/table/table';
import { deleteCoachProfileRest } from 'APIs/restApi';
import { config } from 'config';
import { GetCoachProfiles } from 'models';
import Link from 'next/link';
import { FC, useState } from 'react';
import { toast } from 'react-toastify';

interface Props {
  coachList: GetCoachProfiles[];
  setCoaches: Function;
  setSkip: Function;
  skip: number;
  adminReviewStatus: string;
}

const CoachList: FC<Props> = ({
  coachList,
  setCoaches,
  setSkip,
  skip,
  adminReviewStatus,
}) => {
  const [coachId, setCoachId] = useState('');
  const [modal, setModal] = useState({
    delete: false,
  });

  const onChangeForList = async (skip: number) => {
    try {
      const coachList = await userAPI.getMultipleCoachProfiles(
        skip,
        config.limit,
        adminReviewStatus
      );
      if ('data' in coachList) {
        setCoaches(coachList.data);
      } else {
        toast.error(coachList.error.message);
      }
    } catch (error) {}
    setSkip(0);
  };

  const onClickForDelete = async (id: string) => {
    try {
      const res = await deleteCoachProfileRest(id);
      if ('data' in res) {
        toast.success(res.data.message);
        onChangeForList(0);
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
    setModal({
      ...modal,
      delete: false,
    });
  };

  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const columns = [
    {
      label: 'Name',
      path: 'name',
      content: (data: GetCoachProfiles) => (
        <td className="text-center align-middle">{data?.legalName || ''}</td>
      ),
    },
    {
      label: 'About',
      path: 'about',
      content: (data: GetCoachProfiles) => (
        <td className="text-center align-middle">{data?.about || ''}</td>
      ),
    },
    {
      label: 'Review Status',
      path: 'reviewStatus',
      content: (data: GetCoachProfiles) => (
        <td className="text-center align-middle">{data?.reviewStatus || ''}</td>
      ),
    },
    {
      label: 'Review Comment',
      path: 'reviewComment',
      content: (data: GetCoachProfiles) => (
        <td className="text-center align-middle">
          {data?.reviewComment || ''}
        </td>
      ),
    },
    {
      label: 'View',
      path: 'id',
      content: (data: GetCoachProfiles) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/coach/view/[id]`,
              query: { id: data.id },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Delete',
      path: 'id',
      content: (data: GetCoachProfiles) => (
        <td className="text-center align-middle">
          <button
            className="btn btn-default btn-outline-danger"
            onClick={() => onClickForDelete(data.id)}
          >
            <i className="bi bi-trash3-fill me-2 align-middle"></i>
            Delete
          </button>
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={coachList}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>
      {modal.delete ? (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this coach?</p>
                <br />
                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{ marginRight: '10px' }}
                    onClick={() => setModal({ ...modal, delete: false })}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    // onClick={() => deleteEvent()}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default CoachList;
