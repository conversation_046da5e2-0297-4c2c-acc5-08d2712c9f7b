import {
  createCoachSubCategoryRest,
  getCoachCategoriesRest,
} from 'APIs/restApi';
import { Form, Formik } from 'formik';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import SubCategoryForm from './forms/subCategoryForm';
import { createCoachSubCategorySchema } from './schemas/createCoachSubCategorySchema';

export interface Media {
  url: string;
  name: string;
  file: File;
}

const CreateCoachSubCategory: FC = () => {
  const router = useRouter();
  const [media, setMedia] = useState<Media[]>([]);
  const [categories, setCategories] = useState<{ id: string; title: string }[]>(
    []
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await getCoachCategoriesRest();
        if (response?.data) {
          setCategories(response.data);
        } else {
          toast.error('Failed to fetch categories');
        }
        setLoading(false);
      } catch (error) {
        console.error('Error fetching categories:', error);
        toast.error('Error fetching categories');
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const handleSubmit = async (values: any) => {
    try {
      const subCategoryData = {
        parentId: values.parentId,
        title: values.title,
        description: values.description,
        media: media.map((item) => ({
          type: item.file.type.startsWith('image/') ? 'image' : 'video',
          url: item.url,
        })),
      };

      const res = await createCoachSubCategoryRest(subCategoryData);
      if ('data' in res) {
        toast.success('Coach Sub-Category Created Successfully');
        router.push(`/coach/subCategory`);
      } else {
        toast.error(
          res?.error?.message || 'Failed to Create Coach Sub-Category'
        );
      }
    } catch (error: any) {
      toast.error(error.message || 'Error creating coach sub-category');
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  if (loading) {
    return (
      <div
        className="d-flex justify-content-center align-items-center"
        style={{ minHeight: '200px' }}
      >
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="content-header clearfix" style={{ paddingTop: '10px' }}>
        <h3 className="float-start">
          Create Coach Sub-Category
          <span className="fs-5 p-3">
            <Link href="/coach/subCategory" className="text-decoration-none">
              <i className="bi bi-arrow-left-circle-fill p-2" />
              <span style={{ fontSize: '14px' }}>
                Back to Coach Sub-Categories
              </span>
            </Link>
          </span>
        </h3>
        <div className="float-end">
          <button
            type="submit"
            form="subCategoryForm"
            className="btn btn-primary m-1"
          >
            <p className="float-end mx-1 my-0">Save</p>
          </button>
        </div>
      </div>
      <div className="mt-4">
        <div className="card">
          <div className="card-body">
            <Formik
              initialValues={{
                title: '',
                description: '',
                parentId: '',
                media: [],
                files: [],
              }}
              validationSchema={createCoachSubCategorySchema}
              onSubmit={(values, actions) => {
                handleSubmit(values);
                actions.setSubmitting(false);
              }}
            >
              {({ setFieldValue }) => (
                <Form id="subCategoryForm" onKeyDown={onKeyDown}>
                  <SubCategoryForm
                    setFieldValue={setFieldValue}
                    setMedia={setMedia}
                    media={media}
                    categories={categories}
                  />
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </>
  );
};

export default CreateCoachSubCategory;
