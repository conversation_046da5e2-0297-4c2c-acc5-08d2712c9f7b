import FieldTemplate from '@/components/common/fieldTemplate';
import { ErrorMessage } from 'formik';
import myImageLoader from 'image/loader';
import { CoachSubCategory } from 'models';
import Image from 'next/image';
import { FC, useState } from 'react';

interface Media {
  url: string;
  name: string;
  file: File;
}

interface Props {
  setFieldValue: Function;
  setMedia: Function;
  media: Media[];
  subCategory?: CoachSubCategory;
  edit?: boolean;
  categories?: { id: string; title: string; }[];
}

const SubCategoryForm: FC<Props> = ({
  setFieldValue,
  setMedia,
  media,
  subCategory,
  edit,
  categories,
}) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');
  const [file, setFile] = useState<any>({});

  const toggleButton = () => {
    if (btnToggler === 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  const addMedia = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const currentFiles = event.target.form?.files || [];
      const newFiles = Array.from(files);

      newFiles.forEach((file) => {
        const fileUrl = URL.createObjectURL(file);
        setFile((prev: any) => ({
          ...prev,
          [fileUrl]: {
            src: fileUrl,
            type: file.type,
            file: file,
          },
        }));

        const mediaInfo = {
          url: fileUrl,
          name: file.name,
          file: file,
        };
        const updatedMedia = [...media, mediaInfo];
        setMedia(updatedMedia);
        setFieldValue('media', updatedMedia);
      });

      // Update the files in form values
      const existingFiles = event.target.form?.files || [];
      const updatedFiles = [...Array.from(existingFiles), ...newFiles];
      setFieldValue('files', updatedFiles);
    }
  };

  const removeMedia = (url: string) => {
    if (subCategory?.media) {
      const updatedSubCategoryMedia = subCategory.media.filter(
        (item) => item.url !== url
      );
      subCategory.media = updatedSubCategoryMedia;
    }
    const updatedMedia = media.filter((item) => item.url !== url);
    setMedia(updatedMedia);
    setFieldValue('media', updatedMedia);

    // Remove the file from the files state
    if (file[url]?.file) {
      setFieldValue('files', (currentFiles: File[]) =>
        currentFiles.filter((f) => f !== file[url].file)
      );

      // Clean up the file state
      setFile((prev: any) => {
        const newState = { ...prev };
        delete newState[url];
        return newState;
      });
    }
  };

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="subcategory-info"
        id="subcategory-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#subcategoryInfoTab"
            aria-expanded="true"
            aria-controls="subcategoryInfoTab"
            onClick={toggleButton}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-list-nested col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col px-3 text-start">Sub-Category Info</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="subcategoryInfoTab">
          <div className="card-body">
            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end me-3">
                  <label className="col-form-label col fs-5 px-1" htmlFor="parentId">
                    Parent Category
                  </label>
                  <span className="required col">*</span>
                </div>
              </div>
              <div className="col-md-9">
                <select
                  className="form-control"
                  id="parentId"
                  name="parentId"
                  onChange={(e) => setFieldValue('parentId', e.target.value)}
                  required
                >
                  <option value="">Select a category</option>
                  {categories?.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.title}
                    </option>
                  ))}
                </select>
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="parentId" />
                </div>
              </div>
            </div>

            <FieldTemplate
              label="Title"
              isRequired={true}
              fieldID="title"
              fieldType="text"
            />

            <FieldTemplate
              label="Description"
              isRequired={true}
              fieldID="description"
              fieldType="textarea"
            />

            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end me-3">
                  <label
                    className="col-form-label col fs-5 px-1"
                    htmlFor="media"
                  >
                    Media
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className="input-group mb-2">
                  <input
                    className="form-control rounded-start border-black"
                    type="file"
                    id="media"
                    name="media"
                    onChange={addMedia}
                    accept="image/*,video/*"
                    multiple
                  />
                </div>
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="media" />
                </div>
                <div className="media-preview d-flex flex-wrap gap-2">
                  {media.map((item, index) => (
                    <div
                      key={index}
                      className="position-relative"
                      style={{ width: '150px', height: '150px' }}
                    >
                      {file?.[item.url]?.type?.startsWith('image/') ? (
                        <Image
                          src={item.url}
                          alt="Preview"
                          width={150}
                          height={150}
                          style={{ objectFit: 'cover' }}
                          loader={myImageLoader}
                        />
                      ) : (
                        <video
                          src={item.url}
                          style={{
                            width: '150px',
                            height: '150px',
                            objectFit: 'cover',
                          }}
                          controls
                        />
                      )}
                      <button
                        type="button"
                        className="btn btn-danger btn-sm position-absolute top-0 end-0"
                        onClick={() => removeMedia(item.url)}
                      >
                        <i className="bi bi-x"></i>
                      </button>
                    </div>
                  ))}

                  {edit &&
                    subCategory?.media?.map((item, index) => (
                      <div
                        key={index}
                        className="position-relative"
                        style={{ width: '150px', height: '150px' }}
                      >
                        {item.type.startsWith('image/') ? (
                          <Image
                            src={item.url}
                            alt="Preview"
                            width={150}
                            height={150}
                            style={{ objectFit: 'cover' }}
                            loader={myImageLoader}
                          />
                        ) : (
                          <video
                            src={item.url}
                            style={{
                              width: '150px',
                              height: '150px',
                              objectFit: 'cover',
                            }}
                            controls
                          />
                        )}
                        <button
                          type="button"
                          className="btn btn-danger btn-sm position-absolute top-0 end-0"
                          onClick={() => removeMedia(item.url)}
                        >
                          <i className="bi bi-x"></i>
                        </button>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SubCategoryForm;