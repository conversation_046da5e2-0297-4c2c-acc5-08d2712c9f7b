import Table from '@/components/global/table/table';
import { deleteCoachSubCategoryRest } from 'APIs/restApi';
import Link from 'next/link';
import { FC, useMemo, useState } from 'react';
import { toast } from 'react-toastify';

import { CoachCategoryMap } from 'models';

interface Props {
  categoryMapList: CoachCategoryMap[];
  setCategoryMaps: (categories: CoachCategoryMap[]) => void;
}

interface SubCategoryWithParent {
  id: string;
  title: string;
  parentTitle: string;
  description: string;
  totalCoach: number;
  parentId: string;
}

const SubCategoryList: FC<Props> = ({ categoryMapList, setCategoryMaps }) => {
  const [modal, setModal] = useState({ delete: false });
  const [selectedId, setSelectedId] = useState<string>('');

  const subCategories = useMemo(() => {
    return categoryMapList.reduce<SubCategoryWithParent[]>((acc, category) => {
      const subCats = category.subCategories.map((sub) => ({
        id: sub.id,
        title: sub.title,
        parentTitle: category.title,
        description: sub.description,
        totalCoach: sub.totalCoach,
        parentId: category.id,
      }));
      return [...acc, ...subCats];
    }, []);
  }, [categoryMapList]);

  const handleDelete = async () => {
    try {
      const res = await deleteCoachSubCategoryRest(selectedId);
      if ('data' in res) {
        toast.success('Sub-category deleted successfully');
        // Find the parent category and remove the sub-category
        const updatedCategories = categoryMapList.map((category) => ({
          ...category,
          subCategories: category.subCategories.filter(
            (sub) => sub.id !== selectedId
          ),
        }));
        setCategoryMaps(updatedCategories);
      } else {
        toast.error(res?.error.message!);
      }
    } catch (error) {
      console.error('Error deleting sub-category:', error);
      toast.error('Failed to delete sub-category');
    }
    setModal({ ...modal, delete: false });
  };

  const onClickForDelete = (id: string) => {
    setSelectedId(id);
    setModal({ ...modal, delete: true });
  };

  const onClickForSort = (name: string) => {
    // Implement sorting logic here if needed
  };

  const columns = [
    {
      label: 'Sub-Category Title',
      path: 'title',
      content: (data: SubCategoryWithParent) => (
        <td className="text-center align-middle">{data.title}</td>
      ),
    },
    {
      label: 'Parent Category',
      path: 'parentTitle',
      content: (data: SubCategoryWithParent) => (
        <td className="text-center align-middle">{data.parentTitle}</td>
      ),
    },
    {
      label: 'Description',
      path: 'description',
      content: (data: SubCategoryWithParent) => (
        <td className="text-center align-middle">{data.description}</td>
      ),
    },
    {
      label: 'Total Coaches',
      path: 'totalCoach',
      content: (data: SubCategoryWithParent) => (
        <td className="text-center align-middle">{data.totalCoach}</td>
      ),
    },
    {
      label: 'View',
      path: '_id',
      content: (data: SubCategoryWithParent) => (
        <td className="text-center align-middle">
          <Link
            href={`/coach/subCategory/view/${data.id}`}
            className="btn btn-default btn-outline-info"
          >
            <i className="bi bi-eye-fill me-2 align-middle"></i>
            View
          </Link>
        </td>
      ),
    },
    {
      label: 'Edit',
      path: '_id',
      content: (data: SubCategoryWithParent) => (
        <td className="text-center align-middle">
          <Link
            href={`/coach/subCategory/edit/${data.id}`}
            className="btn btn-default btn-outline-primary"
          >
            <i className="bi bi-pencil me-2 align-middle"></i>
            Edit
          </Link>
        </td>
      ),
    },
    {
      label: 'Delete',
      path: '_id',
      content: (data: SubCategoryWithParent) => (
        <td className="text-center align-middle">
          <button
            className="btn btn-default btn-outline-danger"
            onClick={() => data.id && onClickForDelete(data.id)}
          >
            <i className="bi bi-trash3-fill me-2 align-middle"></i>
            Delete
          </button>
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={subCategories}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>
      {modal.delete && (
        <div className="modal" style={{ display: 'block' }}>
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => setModal({ ...modal, delete: false })}
          >
            <div
              className="modal-content"
              onClick={(e) => e.stopPropagation()}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this sub-category?</p>
                <br />
                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{ marginRight: '10px' }}
                    onClick={() => setModal({ ...modal, delete: false })}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={handleDelete}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SubCategoryList;
