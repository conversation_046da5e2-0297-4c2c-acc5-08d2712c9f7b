import { Form, Formik } from 'formik';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { enableEdit, showModal } from '@/toolkit/modalSlice';
import {
  getCoachCategoriesRest,
  updateCoachSubCategoryRest,
} from 'APIs/restApi';
import { CoachSubCategory } from 'models';
import { handleMediaUpload } from 'utils/handleMediaUpload';
import * as Yup from 'yup';
import SubCategoryForm from './forms/subCategoryForm';

interface Props {
  subCategoryToEdit: CoachSubCategory;
}

interface Media {
  url: string;
  name: string;
  file: File;
}

const createSubCategorySchema = Yup.object().shape({
  title: Yup.string().required('Title is required'),
  description: Yup.string().required('Description is required'),
  parentId: Yup.string().required('Parent Category is required'),
});

const EditSubCategory: FC<Props> = ({ subCategoryToEdit }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const token = useAppSelector((state) => state.persistedReducer.auth.token);
  const edit = useAppSelector((state) => state.persistedReducer.modal.edit);
  const [subCategory, setSubCategory] = useState(subCategoryToEdit);
  const [media, setMedia] = useState<Media[]>([]);
  const [updateData, setUpdateData] = useState<CoachSubCategory>();
  const [categories, setCategories] = useState<{ id: string; title: string }[]>(
    []
  );

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await getCoachCategoriesRest();
        if ('data' in response) {
          setCategories(response.data);
        } else {
          toast.error('Failed to fetch parent categories');
        }
      } catch (error) {
        console.error('Error fetching parent categories:', error);
        toast.error('Failed to fetch parent categories');
      }
    };

    fetchCategories();
  }, []);

  const handleSubmit = async (data: CoachSubCategory) => {
    try {
      let mediaUrls = [...(subCategory.media || [])];

      if (media && media.length > 0) {
        const newMediaUrls = await Promise.all(
          media.map(async (item) => {
            const fileData = {
              featureName: 'coach-subcategory',
              filenames: [item.name],
            };
            const uploadedUrl = await handleMediaUpload(
              fileData,
              item.file,
              token,
              true
            );
            return {
              type: item.file.type.startsWith('image/') ? 'image' : 'video',
              url: uploadedUrl?.toString() as string,
            };
          })
        );
        mediaUrls = [...mediaUrls, ...newMediaUrls];
      }

      const updateResponse = await updateCoachSubCategoryRest(
        subCategory.id ?? '',
        {
          ...data,
          media: mediaUrls,
        }
      );

      if ('data' in updateResponse) {
        toast.success('Sub-category updated successfully');
        router.push('/coach/subCategory');
      } else {
        toast.error(
          updateResponse?.error?.message || 'Failed to update sub-category'
        );
      }
    } catch (error) {
      console.error('Error updating sub-category:', error);
      toast.error('Failed to update sub-category');
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  if (edit === true) {
    handleSubmit(updateData!);
    dispatch(enableEdit(false));
  }

  return (
    <>
      {subCategory ? (
        <Formik
          initialValues={{
            title: subCategory.title || '',
            description: subCategory.description || '',
            parentId: subCategory.parentId || '',
            media: subCategory.media || [],
            files: [],
          }}
          onSubmit={(values, actions) => {
            const data = {
              ...subCategory,
              title: values.title,
              description: values.description,
              parentId: values.parentId,
              media: values.media,
            };
            setUpdateData(data);
            actions.setSubmitting(false);
            dispatch(showModal(true));
          }}
          validationSchema={createSubCategorySchema}
        >
          {(formikProps) => (
            <Form onKeyDown={onKeyDown} className="min-vh-100">
              <div className="content-header clearfix pt-4">
                <h1 className="float-start fs-2">
                  Edit Sub Category
                  <span className="fs-5 p-3">
                    <Link
                      href="/coach/subCategory"
                      className="text-decoration-none"
                    >
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      Back to sub category list
                    </Link>
                  </span>
                </h1>
                <div className="float-end">
                  <button type="submit" className="btn btn-primary m-1">
                    <p className="float-end mx-1 my-0">Save</p>
                  </button>
                </div>
              </div>
              <div className="mt-4 pb-5">
                <SubCategoryForm
                  setFieldValue={formikProps.setFieldValue}
                  setMedia={setMedia}
                  media={media}
                  subCategory={subCategory}
                  edit={true}
                  categories={categories}
                />
              </div>
            </Form>
          )}
        </Formik>
      ) : (
        'Something went wrong!'
      )}
    </>
  );
};

export default EditSubCategory;
