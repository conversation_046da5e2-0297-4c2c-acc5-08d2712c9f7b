import myImageLoader from 'image/loader';
import { CoachSubCategory } from 'models';
import Image from 'next/image';
import Link from 'next/link';
import { FC } from 'react';

const ViewSubCategory: FC<{ subCategory: CoachSubCategory }> = ({
  subCategory,
}) => {
  return (
    <>
      {subCategory ? (
        <div>
          <div className="content-header clearfix mt-3">
            <h1 className="float-start fs-2">
              View sub category details
              <span className="fs-5 p-3">
                <Link href="/coach/subCategory" className="text-decoration-none">
                  <i className="bi bi-arrow-left-circle-fill p-2" />
                  Back to sub category list
                </Link>
              </span>
            </h1>
          </div>
          <div className="mt-4">
            <div className="card">
              <div className="card-body">
                <h5 className="card-title mb-4">{subCategory.title}</h5>
                <div className="row">
                  <div className="col-md-6">
                    <p>
                      <strong>ID:</strong> {subCategory.id}
                    </p>
                    <p>
                      <strong>Title:</strong> {subCategory.title}
                    </p>
                    <p>
                      <strong>Description:</strong> {subCategory.description}
                    </p>
                    <p>
                      <strong>Parent Category ID:</strong> {subCategory.parentId}
                    </p>
                    {subCategory.totalCoach !== undefined && (
                      <p>
                        <strong>Total Coaches:</strong> {subCategory.totalCoach}
                      </p>
                    )}
                  </div>
                  <div className="col-md-6">
                    <h6 className="mb-3">Sub Category Media</h6>
                    <div className="row g-2">
                      {subCategory.media && subCategory.media.length > 0 ? (
                        subCategory.media.map((item, index) => (
                          <div key={index} className="col-6">
                            {item.type === 'image' ? (
                              <Image
                                loader={myImageLoader}
                                src={item.url}
                                alt={`Media ${index + 1}`}
                                width={200}
                                height={200}
                                className="img-fluid rounded"
                                style={{ objectFit: 'cover' }}
                              />
                            ) : item.type === 'video' ? (
                              <video
                                src={item.url}
                                controls
                                className="img-fluid rounded"
                                style={{ width: '200px', height: '200px' }}
                              />
                            ) : (
                              <a
                                href={item.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="btn btn-link"
                              >
                                View Media {index + 1}
                              </a>
                            )}
                          </div>
                        ))
                      ) : (
                        <div className="col-12">No media available</div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        'No sub category data available'
      )}
    </>
  );
};

export default ViewSubCategory;