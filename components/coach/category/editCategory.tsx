import { Form, Formik } from 'formik';
import { useRouter } from 'next/router';
import { FC } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import { userAPI } from '@/APIs';
import { CoachCategory } from 'models';
import Link from 'next/link';

interface Props {
  category: CoachCategory;
}

const editCoachCategorySchema = Yup.object().shape({
  title: Yup.string().required('Title is required'),
});

const EditCategory: FC<Props> = ({ category }) => {
  const router = useRouter();

  const handleSubmit = async (values: CoachCategory) => {
    try {
      const res = await userAPI.updateCoachCategory(category.id!, values);
      if ('data' in res) {
        toast.success('Category Updated Successfully');
        router.push('/coach/category');
      } else {
        toast.error(res?.error?.message || "Can't Update Category");
      }
    } catch (error: any) {
      toast.error(error.message || 'Error updating category');
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          title: category?.title || '',
        }}
        validationSchema={editCoachCategorySchema}
        onSubmit={(values, actions) => {
          handleSubmit(values);
          actions.setSubmitting(false);
        }}
      >
        {({ handleSubmit, handleChange, values, errors, touched }) => (
          <Form onSubmit={handleSubmit} onKeyDown={onKeyDown}>
            <div className="content-header clearfix pt-4">
              <h1 className="float-start">
                Edit Coach Category
                <span className="fs-5 p-3">
                  <Link href="/coach/category" className="text-decoration-none">
                    <i className="bi bi-arrow-left-circle-fill p-2" />
                    Back to category list
                  </Link>
                </span>
              </h1>
              <div className="float-end">
                <button
                  type="submit"
                  name="save"
                  className="btn btn-primary m-1"
                >
                  <i className="bi bi-save me-2" />
                  Save
                </button>
              </div>
            </div>

            <div className="mt-4 pb-5 card">
              <div className="card-body">
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label className="form-label">
                      Title <span style={{ color: 'red' }}>*</span>
                    </label>
                    <input
                      type="text"
                      name="title"
                      className={`form-control ${
                        errors.title && touched.title ? 'is-invalid' : ''
                      }`}
                      onChange={handleChange}
                      value={values.title}
                    />
                    {errors.title && touched.title && (
                      <div className="invalid-feedback">{errors.title}</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default EditCategory;
