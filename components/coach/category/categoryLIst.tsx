import Link from 'next/link';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import Table from '@/components/global/table/table';
import { CoachCategory } from 'models';
import { toast } from 'react-toastify';

interface Props {
  coachCategoryList: CoachCategory[];
  setCoachCategories: Function;
}

const CoachCategoryList: FC<Props> = ({
  coachCategoryList,
  setCoachCategories,
}) => {
  const [categoryId, setCategoryId] = useState('');
  const [modal, setModal] = useState({
    delete: false,
  });

  const loadCategories = async () => {
    try {
      const response = await userAPI.getCoachCategories();
      if ('data' in response) {
        setCoachCategories(response.data);
      } else {
        toast.error(response.error.message);
      }
    } catch (error) {
      toast.error('Failed to load categories');
    }
  };

  const deleteCategory = async () => {
    try {
      const res = await userAPI.deleteCoachCategory(categoryId);
      if ('data' in res) {
        toast.success(res.data.message);
        loadCategories();
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {
      toast.error('Failed to delete category');
    }
    setModal({
      ...modal,
      delete: false,
    });
  };

  const onClickForDelete = (id: string) => {
    setCategoryId(id);
    setModal({ ...modal, delete: true });
  };

  const onClickForSort = (name: string) => {
    // Implement sorting logic if needed
  };

  const columns = [
    {
      label: 'Title',
      path: 'title',
      content: (data: CoachCategory) => (
        <td className="text-center align-middle">{data?.title || ''}</td>
      ),
    },
    {
      label: 'View',
      path: 'id',
      content: (data: CoachCategory) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/coach/category/view/[id]`,
              query: { id: data.id },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye-fill me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Edit',
      path: 'id',
      content: (data: CoachCategory) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/coach/category/edit/[id]`,
              query: { id: data.id },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-info">
              <span>
                <i className="bi bi-pencil me-2 align-middle"></i>
              </span>
              Edit
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Delete',
      path: 'id',
      content: (data: CoachCategory) => (
        <td className="text-center align-middle">
          <button
            className="btn btn-default btn-outline-danger"
            onClick={() => onClickForDelete(data.id!)}
          >
            <i className="bi bi-trash3-fill me-2 align-middle"></i>
            Delete
          </button>
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          {coachCategoryList.length > 0 ? (
            <Table
              items={coachCategoryList}
              columns={columns}
              onClickForSort={onClickForSort}
            />
          ) : (
            <p className="text-center">No categories found</p>
          )}
        </div>
      </div>
      {modal.delete && (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this category?</p>
                <br />
                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{ marginRight: '10px' }}
                    onClick={() => setModal({ ...modal, delete: false })}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => deleteCategory()}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CoachCategoryList;
