import { userAPI } from '@/APIs';
import { Form, Formik } from 'formik';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

const createCoachCategorySchema = Yup.object().shape({
  title: Yup.string().required('Title is required'),
});

interface CoachCategoryFormValues {
  title: string;
}

const CreateCoachCategory: FC = () => {
  const router = useRouter();

  const handleSubmit = async (values: CoachCategoryFormValues) => {
    try {
      const categoryData = {
        title: values.title,
      };

      const res = await userAPI.createCoachCategory(categoryData);
      if ('data' in res) {
        toast.success('Coach Category Created Successfully');
        router.push('/coach/category');
      } else {
        toast.error(res?.error?.message || 'Failed to Create Coach Category');
      }
    } catch (error: any) {
      toast.error(error.message || 'Error creating coach category');
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          title: '',
        }}
        validationSchema={createCoachCategorySchema}
        onSubmit={(values, actions) => {
          handleSubmit(values);
          actions.setSubmitting(false);
        }}
      >
        {({ handleSubmit, handleChange, values, errors, touched }) => (
          <Form onSubmit={handleSubmit} onKeyDown={onKeyDown}>
            <div
              className="content-header clearfix"
              style={{ paddingTop: '10px' }}
            >
              <h3 className="float-start">
                Create Coach Category
                <span className="fs-5 p-3">
                  <Link href="/coach/category" className="text-decoration-none">
                    <i className="bi bi-arrow-left-circle-fill p-2" />
                    <span style={{ fontSize: '14px' }}>
                      Back to Coach Categories
                    </span>
                  </Link>
                </span>
              </h3>
              <div className="float-end">
                <button
                  type="submit"
                  name="save"
                  className="btn btn-primary m-1"
                >
                  <p className="float-end mx-1 my-0">Save</p>
                </button>
              </div>
            </div>
            <div className="mt-4">
              <div className="card">
                <div className="card-body">
                  <div className="form-group row">
                    <div className="col-md-3">
                      <label className="form-label required">
                        Category Title
                      </label>
                    </div>
                    <div className="col-md-9">
                      <input
                        type="text"
                        id="title"
                        name="title"
                        className={`form-control ${
                          errors.title && touched.title ? 'is-invalid' : ''
                        }`}
                        value={values.title}
                        onChange={handleChange}
                      />
                      {errors.title && touched.title && (
                        <div className="invalid-feedback">{errors.title}</div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default CreateCoachCategory;
