import Table from '@/components/global/table/table';
import { deleteCoachSubCategoryRest } from 'APIs/restApi';
import { CoachCategory, CoachSubCategory } from 'models';
import Link from 'next/link';
import { FC, useState } from 'react';
import { toast } from 'react-toastify';

interface Props {
  category: CoachCategory;
  subCategories: CoachSubCategory[];
}

const ViewCoachCategory: FC<Props> = ({
  category,
  subCategories: initialSubCategories,
}) => {
  const [subCategories, setSubCategories] = useState(initialSubCategories);
  const [modal, setModal] = useState({ delete: false });
  const [selectedId, setSelectedId] = useState<string>('');

  const handleDelete = async () => {
    try {
      const res = await deleteCoachSubCategoryRest(selectedId);
      if ('data' in res) {
        toast.success('Sub-category deleted successfully');
        setSubCategories(
          subCategories.filter((category) => category.id !== selectedId)
        );
      } else {
        toast.error(res?.error.message!);
      }
    } catch (error) {
      console.error('Error deleting sub-category:', error);
      toast.error('Failed to delete sub-category');
    }
    setModal({ ...modal, delete: false });
  };

  const onClickForDelete = (id: string) => {
    setSelectedId(id);
    setModal({ ...modal, delete: true });
  };

  console.log('SubCategories', subCategories);
  const columns = [
    {
      label: 'Title',
      path: 'title',
      content: (row: CoachSubCategory) => (
        <td className="text-center align-middle">{row?.title || ''}</td>
      ),
    },
    {
      label: 'Description',
      path: 'description',
      content: (row: CoachSubCategory) => (
        <td className="text-center align-middle">{row?.description || ''}</td>
      ),
    },
    {
      label: 'View',
      path: 'id',
      content: (row: CoachSubCategory) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/coach/subCategory/view/[id]`,
              query: { id: row.id },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye-fill me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Edit',
      path: 'id',
      content: (row: CoachSubCategory) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/coach/subCategory/edit/[id]`,
              query: { id: row.id },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-info">
              <span>
                <i className="bi bi-pencil me-2 align-middle"></i>
              </span>
              Edit
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Delete',
      path: 'id',
      content: (row: CoachSubCategory) => (
        <td className="text-center align-middle">
          <button
            className="btn btn-default btn-outline-danger"
            onClick={() => row.id && onClickForDelete(row.id)}
          >
            <span>
              <i className="bi bi-trash3-fill me-2 align-middle"></i>
            </span>
            Delete
          </button>
        </td>
      ),
    },
  ];

  return (
    <>
      {category ? (
        <div>
          <div className="content-header clearfix mt-3">
            <h1 className="float-start fs-2">
              View Category Details
              <span className="fs-5 p-3">
                <Link href="/coach/category" className="text-decoration-none">
                  <i className="bi bi-arrow-left-circle-fill p-2" />
                  Back to category list
                </Link>
              </span>
            </h1>
          </div>
          <div className="mt-4">
            <div className="card">
              <div className="card-body">
                <h5 className="card-title mb-4">{category.title}</h5>
                <div className="row">
                  <div className="col-md-6">
                    <p>
                      <strong>Category ID:</strong> {category.id}
                    </p>
                    <p>
                      <strong>Title:</strong> {category.title}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-4">
            <div className="card">
              <div className="card-body">
                <div className="d-flex justify-content-between align-items-center mb-4">
                  <h5 className="card-title">Sub Categories</h5>
                  <Link
                    href={{
                      pathname: '/coach/subCategory/create',
                      query: { parentId: category.id },
                    }}
                    passHref
                    legacyBehavior
                  >
                    <button className="btn btn-primary">
                      <i className="bi bi-plus-circle me-2"></i>
                      Add Sub Category
                    </button>
                  </Link>
                </div>
                <Table
                  columns={columns}
                  items={subCategories}
                  onClickForSort={() => {}}
                />
              </div>
            </div>
          </div>
        </div>
      ) : (
        'No category data available'
      )}
      {modal.delete && (
        <div className="modal" style={{ display: 'block' }}>
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => setModal({ ...modal, delete: false })}
          >
            <div
              className="modal-content"
              onClick={(e) => e.stopPropagation()}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this sub-category?</p>
                <br />
                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{ marginRight: '10px' }}
                    onClick={() => setModal({ ...modal, delete: false })}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={handleDelete}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ViewCoachCategory;
