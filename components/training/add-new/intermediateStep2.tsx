import { userAPI } from '@/APIs';
import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { saveTrainingInfo } from '@/toolkit/trainingSlice';
import { config } from 'config';
import { ErrorMessage, Form, Formik } from 'formik';
import { ICreateBaseExcercise } from 'models';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useEffect, useState } from 'react';
import ExerciseList from '../forms/create/exerciseTable';
import SelectDay from '../forms/create/selectFields/selectDay';
import SelectWeek from '../forms/create/selectFields/weekSelect';
import { intermediateSchema } from '../schema';
import { toast } from 'react-toastify';
import PrevNextPagination from '@/components/common/newPagination';

interface Options {
  label: string;
  value: number | string;
}

const IntermediateStep2: FC = () => {
  const router = useRouter();
  const currentLevel = router.query.level as string;

  const dispatch = useAppDispatch();

  const [exercises, setExercises] = useState<ICreateBaseExcercise[]>([]);
  const [selectedExercises, setSelectedExercises] = useState<
    ICreateBaseExcercise[]
  >([]);

  const trainingInfo = useAppSelector(
    (state) => state.persistedReducer.training
  );
  const [level, setLevel] = useState<string>(trainingInfo.level);
  const [bbProgram, setBBProgram] = useState<string>(trainingInfo.bbprogram!);
  const [weeks, setWeeks] = useState<number>(trainingInfo.weeks!);
  const [days, setDays] = useState<Options[]>();
  const [weekList, setWeekList] = useState<Options[]>([]);
  const [selectedDay, setSelectedDay] = useState();
  const [selectedWeek, setSelectedWeek] = useState();

  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);

  const [disableNext, setDisableNext] = useState(false);

  const getExerciseList = async () => {
    try {
      const res = await userAPI.getBaseExercise(
        undefined,
        undefined,
        skip,
        limit
      );
      if ('data' in res) {
        if (res.data.length === 0) {
          setDisableNext(true);
        } else {
          setDisableNext(false);
          setExercises(res.data);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  const handleSubmit = (data: any) => {
    const trainingInfo = {
      level,
      bbprogram: bbProgram,
      weeks,
      selectedWeek,
      selectedDay,
      selectedExercises,
    };
    dispatch(saveTrainingInfo(trainingInfo));
    router.push('/training/add-new/intermediate-step-3?level=INTERMEDIATE');
  };

  useEffect(() => {
    let list = [];
    for (let i = 1; i <= weeks; i++) {
      list.push({
        label: 'Week' + ' ' + i,
        value: i,
      });
    }
    setWeekList(list);
    setDays([
      { label: 'SATURDAY', value: 'SATURDAY' },
      { label: 'SUNDAY', value: 'SUNDAY' },
      { label: 'MONDAY', value: 'MONDAY' },
      { label: 'TUESDAY', value: 'TUESDAY' },
      { label: 'WEDNESDAY', value: 'WEDNESDAY' },
      { label: 'THURSDAY', value: 'THURSDAY' },
      { label: 'FRIDAY', value: 'FRIDAY' },
    ]);
  }, []);

  useEffect(() => {
    getExerciseList();
  }, [skip]);

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          selectedWeek: 0,
          selectedDay: '',
          selectedExerciseIDs: [],
        }}
        onSubmit={(values, actions) => {
          handleSubmit(values);
          actions.setSubmitting(false);
        }}
        validationSchema={intermediateSchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix mt-2 px-5"
                style={{ paddingTop: '10px' }}
              >
                <h3 className="float-start">
                  Create A Training
                  <span className="fs-5 p-3">
                    <Link
                      href={`/training/add-new?level=${currentLevel}`}
                      className="text-decoration-none"
                    >
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      <span style={{ fontSize: '14px' }}>Back to Step 1</span>
                    </Link>
                  </span>
                </h3>
              </div>
              <div className="m-4">
                <SelectWeek
                  weeks={weekList}
                  setSelectedWeek={setSelectedWeek}
                  setFieldValue={formikprops.setFieldValue}
                />
                <SelectDay
                  days={days!}
                  setSelectedDay={setSelectedDay}
                  setFieldValue={formikprops.setFieldValue}
                />
                {selectedDay && (
                  <div className="form-group row my-2 mb-3">
                    <div className="col-md-3">
                      <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                        <label
                          className="col-form-label col fs-5 px-1 text-center"
                          htmlFor="selectedExerciseIDs"
                        >
                          Select Exercises
                          <span className="required text-danger ">*</span>
                        </label>
                      </div>
                    </div>
                    <div className="col-md-9">
                      {' '}
                      <ExerciseList
                        exerciseList={exercises!}
                        setSelectedExercises={setSelectedExercises}
                        selectedExercises={selectedExercises}
                        setSkip={setSkip}
                        skip={skip}
                      />
                      <PrevNextPagination
                        skip={skip}
                        setSkip={setSkip}
                        limit={limit}
                        disableNext={disableNext}
                      />
                      <div className="errMsg text-danger text-red-600">
                        <ErrorMessage name="selectedExerciseIDs" />
                      </div>
                    </div>
                  </div>
                )}
                <div className="float-end">
                  <button
                    type="submit"
                    name="save"
                    className="btn btn-primary m-1"
                  >
                    <p className="mx-1 my-0">
                      Next{' '}
                      <span>
                        <i className="bi bi-arrow-right" />
                      </span>
                    </p>
                  </button>
                </div>
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default IntermediateStep2;
