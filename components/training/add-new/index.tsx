import { saveTrainingInfo } from '@/toolkit/trainingSlice';
import { Form, Formik } from 'formik';
import { ICreateBaseExcercise } from 'models';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';
import { useDispatch } from 'react-redux';
import TrainingInfo from '../forms/create/trainingInfo';
import { trainingSchema } from '../schema';

const CreateTraining: FC = () => {
  const router = useRouter();
  const level = router.query.level as string;
  const dispatch = useDispatch();

  const [selectedExercises, setSelectedExercises] = useState<
    ICreateBaseExcercise[]
  >([]);

  const handleSubmit = async (data: any) => {
    const trainingInfo = {
      level,
      category: data.category,
      muscleGroup: data.muscleGroup,
      weeks: parseInt(data.weeks),
      bbprogram: data.bbprogram,
      selectedExercises,
    };
    dispatch(saveTrainingInfo(trainingInfo));
    if (level == 'BEGINNER') {
      router.push('/training/add-new/step-2?level=BEGINNER');
    } else if (level == 'ADVANCE') {
      router.push('/training/add-new/step-2?level=ADVANCE');
    } else
      router.push('/training/add-new/intermediate-step-2?level=INTERMEDIATE');
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          level,
          category: '',
          muscleGroup: '',
          bbprogram: '',
          weeks: 0,
          selectedExerciseIDs: [],
        }}
        onSubmit={(values, actions) => {
          handleSubmit(values);
          actions.setSubmitting(false);
        }}
        validationSchema={trainingSchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix"
                style={{ paddingTop: '10px' }}
              >
                <h3 className="float-start">
                  {level === 'BEGINNER'
                    ? 'Create a beginner training'
                    : level === 'ADVANCE'
                    ? 'Create an advance training'
                    : level === 'INTERMEDIATE'
                    ? 'Create an intermediate training'
                    : 'Create a training'}
                  <span className="fs-5 p-3">
                    <Link
                      href={
                        level === 'BEGINNER'
                          ? '/training/beginner'
                          : level === 'INTERMEDIATE'
                          ? '/training/intermediate'
                          : level === 'ADVANCE'
                          ? '/training/advance'
                          : '/training'
                      }
                      className="text-decoration-none"
                    >
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      <span style={{ fontSize: '14px' }}>
                        {level
                          ? `Back to ${level.toLowerCase()} training list`
                          : 'Back to training list'}
                      </span>
                    </Link>
                  </span>
                </h3>
              </div>
              <div className="mt-4">
                <TrainingInfo
                  selectedExercises={selectedExercises}
                  setSelectedExercises={setSelectedExercises}
                  setFieldValue={formikprops.setFieldValue}
                />
              </div>
              <div className="float-end">
                <button
                  type="submit"
                  name="save"
                  className="btn btn-primary m-1"
                >
                  <p className="mx-1 my-0">
                    Next{' '}
                    <span>
                      <i className="bi bi-arrow-right" />
                    </span>
                  </p>
                </button>
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default CreateTraining;
