import { userAPI } from '@/APIs';
import UpdateExercise from '@/components/training/forms/create/updateExercise';
import { useAppSelector } from '@/redux-hooks';
import { CreateTraining, ICreateBaseExcercise } from 'models';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import { toast } from 'react-toastify';

const CreateTrainingStep2 = () => {
  const router = useRouter();
  const currentLevel = router.query.level as string;

  const trainingInfo = useAppSelector(
    (state) => state.persistedReducer.training
  );

  const [category, setCategory] = useState(trainingInfo.category);
  const [muscleGroup, setMuscleGroup] = useState(trainingInfo.muscleGroup);
  const [level, setLevel] = useState(trainingInfo.level);
  const [exercises, setExercises] = useState<ICreateBaseExcercise[]>(
    trainingInfo.selectedExercises!
  );
  const [trainings, setTrainings] = useState<CreateTraining[]>([]);
 

  const handleSubmit = async () => {
    try {
      const res: any = await userAPI.createTraining({
        trainingList: trainings,
      });

      if ('data' in res) {
        router.push('/training');
        toast.success('Training created successfully');
      } else {
        if (res.error) {
          toast.error(res.error.message);
        } else toast.error('Failed to create training');
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <>
      <div className="content-header clearfix" style={{ paddingTop: '10px' }}>
        <h3 className="float-start">
          Create A Training
          <span className="fs-5 p-3">
            <Link
              href={`/training/add-new?level=${currentLevel}`}
              className="text-decoration-none"
            >
              <i className="bi bi-arrow-left-circle-fill p-2" />
              <span style={{ fontSize: '14px' }}>Back to Step 1</span>
            </Link>
          </span>
        </h3>
        <div className="float-end">
          <button
            type="submit"
            name="save"
            className="btn btn-primary m-1"
            onClick={handleSubmit}
          >
            {/* <i className="bi bi-save" /> */}
            <p className="float-end mx-1 my-0">Save</p>
          </button>
        </div>
      </div>
      <div className="mt-4">
        {' '}
        {trainingInfo.selectedExercises?.map((exercise) => {
          return (
            <React.Fragment key={exercise.id}>
              <UpdateExercise
                exercise={exercise}
                setExercises={setExercises}
                exercises={exercises!}
                setTrainings={setTrainings}
                trainings={trainings}
                level={level}
              />
            </React.Fragment>
          );
        })}
      </div>
    </>
  );
};

export default CreateTrainingStep2;
