import TrainingInfoCard from '@/components/training/view/cards/InfoCard';
import Link from 'next/link';

const ViewTraining: React.FC<any> = ({ training }) => {
  return (
    <>
      {training ? (
        <div>
          <div className="content-header clearfix mt-3">
            <h1 className="float-start fs-2">
              View training details
              <span className="fs-5 p-3">
                <Link
                  href={
                    training.expertiseLevel === 'BEGINNER'
                      ? '/training/beginner'
                      : training.expertiseLevel === 'ADVANCE'
                      ? '/training/advance'
                      : '/training/intermediate'
                  }
                  className="text-decoration-none "
                >
                  <i className="bi bi-arrow-left-circle-fill p-2" />
                  Back to training list
                </Link>
              </span>
            </h1>
          </div>

          <div className="mt-4">
            <TrainingInfoCard training={training} />
          </div>
        </div>
      ) : (
        ''
      )}
    </>
  );
};

export default ViewTraining;
