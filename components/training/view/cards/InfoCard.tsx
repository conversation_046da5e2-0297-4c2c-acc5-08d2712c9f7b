import { FC } from 'react';

import SingleView from '@/components/common/singleView';

interface Props {
  training: any;
}

const TrainingInfoCard: FC<Props> = ({ training }) => {
  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="training-info"
        id="training-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <div className="card-title row align-items-center ps-2 pt-2">
            <i
              className="bi bi-info-lg col-1 align-text-top"
              style={{ fontSize: '25px' }}
            />
            <div className="fs-5 col text-start px-3">Training Info</div>
          </div>
        </div>
        <div className="" id="trainingInfoTab">
          <div className="card-body">
            <SingleView label="ID" value={training.id} />
            <SingleView
              label="Level"
              value={
                training.expertiseLevel
                  ? training.expertiseLevel
                  : 'INTERMEDIATE'
              }
            />
            {training.programId && (
              <SingleView label="Program ID" value={training.programId} />
            )}
            {training.week && <SingleView label="Week" value={training.week} />}
            {training.day && <SingleView label="Day" value={training.day} />}
            <SingleView label="Duration" value={training.duration} />
            <SingleView label="Name" value={training.name} />
            <SingleView label="Description" value={training.description} />
            <SingleView label="Mechanics" value={training.mechanics} />
            {/* <SingleView label="Type" value={training.type} /> */}
            <SingleView label="Force Type" value={training.forceType} />
            <SingleView label="Category" value={training.category} />
            <SingleView label="Muscle Group" value={training.primaryMuscles} />
            <SingleView
              label="Secondary Muscle Group"
              value={training.secondaryMuscles}
            />
            <SingleView label="Equipments" value={training.equipments} />
            <SingleView
              label="Attachment"
              attachment={training.preview}
              isImage={true}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default TrainingInfoCard;
