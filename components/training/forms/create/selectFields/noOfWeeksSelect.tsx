import { ErrorMessage, Field } from 'formik';
import React, { FC } from 'react';

interface Options {
  label: string;
  value: string;
}

interface Props {
  weeks: Options[];
  setSelectedWeeks: Function;
  setFieldValue: Function;
}

const SelectNoOfWeeks: FC<Props> = ({
  weeks,
  setSelectedWeeks,
  setFieldValue,
}) => {
  return (
    <>
      {weeks?.length! > 0 ? (
        <div className="form-group row my-2 mb-3">
          <div className="col-md-3">
            <div className="label-wrapper row row-cols-auto float-md-end pe-3">
              <label
                className="col-form-label col fs-5 px-1 text-center"
                htmlFor="weeks"
              >
                Select Number of Weeks
                <span className="required text-danger ">*</span>
              </label>
            </div>
          </div>
          <div className="col-md-9">
            <Field
              className="form-select custom-select w-100"
              as="select"
              id="weeks"
              name="weeks"
              onChange={(e: any) => {
                if (
                  (e.target as HTMLInputElement).value &&
                  (e.target as HTMLInputElement).value != 'Select One'
                ) {
                  let weekArray = [];
                  for (
                    let i = 1;
                    i <= parseInt((e.target as HTMLInputElement).value!);
                    i++
                  ) {
                    weekArray.push(i);
                  }
                  setSelectedWeeks(weekArray);
                  setFieldValue(
                    'weeks',
                    parseInt((e.target as HTMLInputElement).value!)
                  );
                }
              }}
            >
              <option>Select One</option>
              {weeks?.map((week) => {
                return (
                  <React.Fragment key={week.label}>
                    <option value={parseInt(week.value)}>{week.label}</option>
                  </React.Fragment>
                );
              })}
            </Field>
            <div className="errMsg text-danger text-red-600">
              <ErrorMessage name="weeks" />
            </div>
          </div>
        </div>
      ) : (
        <></>
      )}
    </>
  );
};

export default SelectNoOfWeeks;
