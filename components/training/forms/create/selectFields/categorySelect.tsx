import { ErrorMessage, Field } from 'formik';
import Link from 'next/link';
import React, { FC } from 'react';

interface Options {
  label: string;
  value: string;
}

interface Props {
  categories: Options[];
  setSelectedCategory: Function;
  selectedLevel: string;
  setFieldValue: Function;
}

const SelectCategory: FC<Props> = ({
  categories,
  setSelectedCategory,
  selectedLevel,
  setFieldValue,
}) => {
  return (
    <>
      {categories?.length! > 0 ? (
        <div className="form-group row my-2 mb-3">
          <div className="col-md-3">
            <div className="label-wrapper row row-cols-auto float-md-end pe-3">
              <label
                className="col-form-label col fs-5 px-1 text-center"
                htmlFor="category"
              >
                Select Category
                <span className="required text-danger ">*</span>
              </label>
            </div>
          </div>
          <div className="col-md-9">
            <Field
              className="form-select custom-select w-100"
              as="select"
              id="category"
              name="category"
              onChange={(e: any) => {
                if (
                  (e.target as HTMLInputElement).value &&
                  (e.target as HTMLInputElement).value != 'Select One Category'
                ) {
                  setSelectedCategory((e.target as HTMLInputElement).value!);
                  setFieldValue(
                    'category',
                    (e.target as HTMLInputElement).value!
                  );
                }
              }}
            >
              <option>Select One Category</option>
              {categories?.map((category) => {
                return (
                  <React.Fragment key={category.label}>
                    <option value={category.value}>{category.label}</option>
                  </React.Fragment>
                );
              })}
            </Field>
            <div className="errMsg text-danger text-red-600">
              <ErrorMessage name="category" />
            </div>
          </div>
        </div>
      ) : (
        selectedLevel === 'Beginner' && (
          <div className="row">
            <div className="col-md-3"></div>
            <div className="col-md-9">
              <div className="d-flex flex-wrap gap-2">
                <p>No category found under this expertise level.</p>
                <Link href="/exercise-module/category/add-new">Create One</Link>
              </div>
            </div>
          </div>
        )
      )}
    </>
  );
};

export default SelectCategory;
