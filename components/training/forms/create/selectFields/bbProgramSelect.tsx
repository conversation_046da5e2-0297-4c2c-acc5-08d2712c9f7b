import { ErrorMessage, Field } from 'formik';
import Link from 'next/link';
import React, { FC } from 'react';

interface Options {
  label: string;
  value: string;
}

interface Props {
  bbPrograms: Options[];
  setSelectedBBProgram: Function;
  selectedLevel: string;
  setFieldValue: Function;
  edit?: boolean;
}

const SelectBBProgram: FC<Props> = ({
  bbPrograms,
  setSelectedBBProgram,
  selectedLevel,
  setFieldValue,
  edit,
}) => {
  return (
    <>
      {bbPrograms?.length! > 0 ? (
        <div className="form-group row my-2 mb-3">
          <div className="col-md-3">
            <div className="label-wrapper row row-cols-auto float-md-end pe-3">
              <label
                className="col-form-label col fs-5 px-1 text-center"
                htmlFor="bbprogram"
              >
                Select Body Building Program
                <span className="required text-danger ">*</span>
              </label>
            </div>
          </div>
          <div className="col-md-9">
            <Field
              className="form-select custom-select w-100"
              as="select"
              id="bbprogram"
              name="bbprogram"
              onChange={(e: any) => {
                if (
                  (e.target as HTMLInputElement).value &&
                  (e.target as HTMLInputElement).value !=
                    'Select One Body Building Program'
                ) {
                  if (!edit) {
                    setSelectedBBProgram((e.target as HTMLInputElement).value!);
                    setFieldValue(
                      'bbprogram',
                      (e.target as HTMLInputElement).value!
                    );
                  } else {
                    setSelectedBBProgram((e.target as HTMLInputElement).value!);
                  }
                }
              }}
            >
              <option>Select One Body Building Program</option>
              {bbPrograms?.map((bbprogram) => {
                return (
                  <React.Fragment key={bbprogram.label}>
                    <option value={bbprogram.value}>{bbprogram.label}</option>
                  </React.Fragment>
                );
              })}
            </Field>
            <div className="errMsg text-danger text-red-600">
              <ErrorMessage name="bbprogram" />
            </div>
          </div>
        </div>
      ) : (
        selectedLevel === 'Intermediate' && (
          <div className="row">
            <div className="col-md-3"></div>
            <div className="col-md-9">
              <div className="d-flex flex-wrap gap-2">
                <p>
                  No body building program found under this expertise level.
                </p>
                <Link href="/exercise-module/body-building-program/add-new">
                  Create One
                </Link>
              </div>
            </div>
          </div>
        )
      )}
    </>
  );
};

export default SelectBBProgram;
