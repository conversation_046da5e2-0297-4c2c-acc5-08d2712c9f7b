import { ErrorMessage, Field } from 'formik';
import Link from 'next/link';
import React, { FC } from 'react';

interface Options {
  label: string;
  value: string;
}

interface Props {
  muscleGroup: Options[];
  setSelectedMuscleGroup: Function;
  selectedLevel: string;
  setFieldValue: Function;
}

const SelectMuscleGroup: FC<Props> = ({
  muscleGroup,
  setSelectedMuscleGroup,
  selectedLevel,
  setFieldValue,
}) => {
  return (
    <>
      {muscleGroup?.length! > 0 ? (
        <div className="form-group row my-2 mb-3">
          <div className="col-md-3">
            <div className="label-wrapper row row-cols-auto float-md-end pe-3">
              <label
                className="col-form-label col fs-5 px-1 text-center"
                htmlFor="muscleGroup"
              >
                Select Muscle Group
                <span className="required text-danger ">*</span>
              </label>
            </div>
          </div>
          <div className="col-md-9">
            <Field
              className="form-select custom-select w-100"
              id="muscleGroup"
              name="muscleGroup"
              as="select"
              onChange={(e: any) => {
                if (
                  (e.target as HTMLInputElement).value &&
                  (e.target as HTMLInputElement).value !=
                    'Select One Muscle Group'
                ) {
                  setSelectedMuscleGroup((e.target as HTMLInputElement).value);
                  setFieldValue(
                    'muscleGroup',
                    (e.target as HTMLInputElement).value
                  );
                }
              }}
            >
              <option>Select One Muscle Group</option>
              {muscleGroup?.map((muscle: any) => {
                return (
                  <React.Fragment key={muscle.name}>
                    <option value={muscle.name}>{muscle.label}</option>
                  </React.Fragment>
                );
              })}
            </Field>
            <div className="errMsg text-danger text-red-600">
              <ErrorMessage name="muscleGroup" />
            </div>
          </div>
        </div>
      ) : (
        selectedLevel === 'Advance' && (
          <div className="row">
            <div className="col-md-3"></div>
            <div className="col-md-9">
              <div className="d-flex flex-wrap gap-2">
                <p>No muscle group found under this expertise level.</p>
                <Link href="/exercise-module/muscle-group/add-new">
                  Create One
                </Link>
              </div>
            </div>
          </div>
        )
      )}
    </>
  );
};

export default SelectMuscleGroup;
