import { ErrorMessage, Field } from 'formik';
import React, { FC } from 'react';

interface Options {
  label: string;
  value: number | string;
}

interface Props {
  days: Options[];
  setSelectedDay: Function;
  setFieldValue: Function;
}

const SelectDay: FC<Props> = ({ days, setSelectedDay, setFieldValue }) => {
  return (
    <>
      {days?.length! > 0 ? (
        <div className="form-group row my-2 mb-3">
          <div className="col-md-3">
            <div className="label-wrapper row row-cols-auto float-md-end pe-3">
              <label
                className="col-form-label col fs-5 px-1 text-center"
                htmlFor="selectedDay"
              >
                Select Day
                <span className="required text-danger ">*</span>
              </label>
            </div>
          </div>
          <div className="col-md-9">
            <Field
              className="form-select custom-select w-100"
              as="select"
              id="selectedDay"
              name="selectedDay"
              onChange={(e: any) => {
                if (
                  (e.target as HTMLInputElement).value &&
                  (e.target as HTMLInputElement).value != 'Select One'
                ) {
                  setSelectedDay((e.target as HTMLInputElement).value);
                  setFieldValue(
                    'selectedDay',
                    (e.target as HTMLInputElement).value
                  );
                }
              }}
            >
              <option>Select One</option>
              {days?.map((day) => {
                return (
                  <React.Fragment key={day.label}>
                    <option value={day.value}>{day.label}</option>
                  </React.Fragment>
                );
              })}
            </Field>
            <div className="errMsg text-danger text-red-600">
              <ErrorMessage name="selectedDay" />
            </div>
          </div>
        </div>
      ) : (
        <></>
      )}
    </>
  );
};

export default SelectDay;
