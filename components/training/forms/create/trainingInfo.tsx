import { userAPI } from '@/APIs';
import { config } from 'config';
import { ErrorMessage } from 'formik';
import {
  BodyBuildingProgram,
  ExpertiseLevel,
  ICreateBaseExcercise,
  MuscleGroup,
  TrainingCategory,
} from 'models';
import { useRouter } from 'next/router';
import { FC, useEffect, useState } from 'react';
import ExerciseList from './exerciseTable';
import SelectBBProgram from './selectFields/bbProgramSelect';
import SelectCategory from './selectFields/categorySelect';
import SelectMuscleGroup from './selectFields/selectMuscleGroup';
import PrevNextPagination from '@/components/common/newPagination';

interface Props {
  selectedExercises: ICreateBaseExcercise[];
  setSelectedExercises: Function;
  setFieldValue: Function;
}

interface Options {
  label: string;
  value: string;
}

const TrainingInfo: FC<Props> = ({
  selectedExercises,
  setSelectedExercises,
  setFieldValue,
}) => {
  const router = useRouter();
  const level = router.query.level as string;

  const [btnToggler, setBtnToggler] = useState('bi-dash');
  const [levels, setLevels] = useState<Options[]>();
  const [selectedLevel, setSelectedLevel] = useState<string>(
    level ? level : ''
  );
  const [categories, setCategories] = useState<Options[]>();
  const [selectedCategory, setSelectedCategory] = useState<string>();
  const [muscleGroup, setMuscleGroup] = useState<Options[]>();
  const [selectedMuscleGroup, setSelectedMuscleGroup] = useState<string>();
  const [bbPrograms, setBBPrograms] = useState<Options[]>([]);
  const [selectedBBProgram, setSelectedBBProgram] = useState<string>();
  const [exercises, setExercises] = useState<ICreateBaseExcercise[]>([]);
  // const [weeks, setWeeks] = useState<Options[]>([]);
  const [selectedWeeks, setSelectedWeeks] = useState<number[]>();

  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  const handleGetCategories = async () => {
    try {
      muscleGroup && setMuscleGroup([]);
      bbPrograms && setBBPrograms([]);
      //weeks && setWeeks([]);
      const res = await userAPI.getTrainingCategory();
      const list = res.data as TrainingCategory[];
      const categories = list.map((item) => {
        return {
          label: item.name,
          value: item.name,
        };
      });
      setCategories(categories);
    } catch (error) {
      console.log(error);
    }
  };

  const handleGetMuscleGroup = async () => {
    categories && setCategories([]);
    bbPrograms && setBBPrograms([]);
    //weeks && setWeeks([]);
    const res = await userAPI.getMuscleGroup();
    const list = res.data as MuscleGroup[];
    const muscleList = list.map((item) => {
      return {
        label: item.name,
        value: item.name,
      };
    });
    setMuscleGroup(muscleList);
  };

  const handleGetBBPrograms = async () => {
    categories && setCategories([]);
    muscleGroup && setMuscleGroup([]);
    //weeks && setWeeks([]);
    const res = await userAPI.getBBProgram();
    const list = res.data as BodyBuildingProgram[];
    const bbList = list.map((item) => {
      return {
        label: item.name,
        value: item.id!,
      };
    });
    setBBPrograms(bbList);
  };

  const handleSetWeeks = async () => {
    const duration = (
      (await userAPI.getBBProgram(selectedBBProgram))
        .data as BodyBuildingProgram
    ).programDuration;
    let weekList = [];
    for (let i = 1; i <= duration; i++) {
      weekList.push({
        label: '' + i,
        value: '' + i,
      });
    }
    //setWeeks(weekList);
    let weekArray = [];
    for (let i = 1; i <= duration; i++) {
      weekArray.push(i);
    }
    setSelectedWeeks(weekArray);
    setFieldValue('weeks', duration);
  };

  const resetAll = () => {
    setCategories([]);
    setMuscleGroup([]);
    setBBPrograms([]);
    setExercises([]);
  };

  const getFilteredExerciseList = async (
    filterBy: string,
    stateChanged?: boolean
  ) => {
    try {
      let res;
      if (filterBy === 'CATEGORY') {
        res = await userAPI.getBaseExercise(
          filterBy,
          selectedCategory,
          skip,
          limit
        );
      } else if (filterBy === 'MUSCLE_GROUP') {
        res = await userAPI.getBaseExercise(
          filterBy,
          selectedMuscleGroup,
          skip,
          limit
        );
      } else {
        res = await userAPI.getBaseExercise(undefined, undefined, skip, limit);
      }
      if ('data' in res) {
        if (res.data.length === 0) {
          setDisableNext(true);
        } else {
          setDisableNext(false);
          setExercises(res.data);
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (level === 'BEGINNER') {
      handleGetCategories();
      selectedBBProgram && setSelectedBBProgram('');
      selectedMuscleGroup && setSelectedMuscleGroup('');
      selectedExercises && setSelectedExercises('');
    } else if (level === 'ADVANCE') {
      handleGetMuscleGroup();
      selectedBBProgram && setSelectedBBProgram('');
      selectedExercises && setSelectedExercises('');
      selectedCategory && setSelectedCategory('');
    } else if (level === 'INTERMEDIATE') {
      handleGetBBPrograms();
      selectedMuscleGroup && setSelectedMuscleGroup('');
      selectedExercises && setSelectedExercises('');
      selectedCategory && setSelectedCategory('');
    } else {
      resetAll();
    }
  }, [level]);

  useEffect(() => {
    if (level === ExpertiseLevel.BEGINNER)
      getFilteredExerciseList('CATEGORY', true);
  }, [selectedCategory, skip]);

  useEffect(() => {
    if (level === ExpertiseLevel.ADVANCE)
      getFilteredExerciseList('MUSCLE_GROUP', true);
  }, [selectedMuscleGroup, skip]);

  useEffect(() => {
    if (level === 'INTERMEDIATE') {
      getFilteredExerciseList('NONE', true);
      handleSetWeeks();
    }
  }, [selectedBBProgram, skip]);

  useEffect(() => {
    setSelectedBBProgram('');
    setSelectedCategory('');
    setSelectedMuscleGroup('');
  }, [selectedLevel]);

  return (
    <>
      <div
        className="card card-secondary card-outline mt-4 mb-2"
        data-card-name="training-info"
        data-hideattribute="ProductPage.HideInfoBlock"
        id="training-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#trainingInfoTab"
            aria-expanded="false"
            aria-controls="trainingInfoTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-info-lg col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col px-3 text-start">Training Info</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="trainingInfoTab">
          <div className="card-body">
            {/* <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                  <label
                    className="col-form-label col fs-5 px-1 text-center"
                    htmlFor="level"
                  >
                    {level ? 'Expertise Level' : 'Select Expertise Level'}
                    {!level && <span className="required text-danger ">*</span>}
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <p className="form-select custom-select w-100">{level}</p>
              </div>
            </div> */}

            <SelectCategory
              categories={categories!}
              setSelectedCategory={setSelectedCategory}
              selectedLevel={selectedLevel!}
              setFieldValue={setFieldValue}
            />

            <SelectMuscleGroup
              muscleGroup={muscleGroup!}
              setSelectedMuscleGroup={setSelectedMuscleGroup}
              selectedLevel={selectedLevel!}
              setFieldValue={setFieldValue}
            />

            <SelectBBProgram
              bbPrograms={bbPrograms}
              setSelectedBBProgram={setSelectedBBProgram}
              selectedLevel={selectedLevel!}
              setFieldValue={setFieldValue}
            />

            {(selectedCategory != '' || selectedMuscleGroup != '') && (
              <div className="form-group row my-2 mb-3">
                <div className="col-md-3">
                  <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                    <label
                      className="col-form-label col fs-5 px-1 text-center"
                      htmlFor="selectedExerciseIDs"
                    >
                      Select Exercises
                      <span className="required text-danger ">*</span>
                    </label>
                  </div>
                </div>
                <div className="col-md-9">
                  {' '}
                  <ExerciseList
                    exerciseList={exercises!}
                    setSelectedExercises={setSelectedExercises}
                    selectedExercises={selectedExercises}
                    setSkip={setSkip}
                    skip={skip}
                  />
                  <PrevNextPagination
                    skip={skip}
                    setSkip={setSkip}
                    limit={limit}
                    disableNext={disableNext}
                  />
                  <div className="errMsg text-danger text-red-600">
                    <ErrorMessage name="selectedExerciseIDs" />
                  </div>
                </div>
              </div>
            )}

            {/* {selectedBBProgram && (
              <>
                <SelectNoOfWeeks
                  weeks={weeks}
                  setSelectedWeeks={setSelectedWeeks}
                  setFieldValue={setFieldValue}
                />
              </>
            )} */}
          </div>
        </div>
      </div>
    </>
  );
};

export default TrainingInfo;
