import { userAPI } from '@/APIs';
import CustomSelect from '@/components/common/customSelectInput';
import FieldTemplate from '@/components/common/fieldTemplate';
import { ErrorMessage, Field } from 'formik';
import myImageLoader from 'image/loader';
import { ICreateBaseExcercise, MuscleGroup, TrainingCategory } from 'models';
import Image from 'next/image';
import { FC, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

interface Props {
  setFieldValue?: Function;
  setEquipments?: Function;
  setForceType?: Function;
  forceType: string[];
  equipments?: string[];
  exercise?: ICreateBaseExcercise;
}

interface Options {
  label: string;
  value: string;
}

const ExerciseInfo: FC<Props> = ({
  setFieldValue,
  setEquipments,
  setForceType,
  forceType,
  equipments,
  exercise,
}) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');
  const [exercises, setExercises] = useState<ICreateBaseExcercise[]>();
  const [file, setFile] = useState<any>();

  const onClickNotify = () => {
    toast.success(`Exercise Info updated`);
  };

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') {
      setBtnToggler('bi-dash');
      //toast.success(`Exercise Info updated`);
    } else setBtnToggler('bi-plus-lg');
    //toast.success(`Exercise Info updated`);
  };

  const addItem = () => {
    const equipmentsList = (
      document.getElementById('equipments') as HTMLInputElement
    ).value;
    if (equipmentsList.length > 0) {
      setFieldValue!('equipments', '');
      setEquipments!([...equipments!, equipmentsList]);
    }
  };

  const removeItem = (equipment: string) => {
    const equipmentsList = equipments?.filter(
      (singleitem) => singleitem != equipment
    );
    setEquipments!(equipmentsList);
  };

  const addForceType = () => {
    const forces = (document.getElementById('forceType') as HTMLInputElement)
      .value;
    if (forces.length > 0) setForceType!([...forceType, forces]);
  };

  const removeForceType = (force: string) => {
    const forces = forceType.filter((singleitem) => singleitem != force);
    setForceType!(forces);
  };

  const [muscleOptions, setMuscleOptions] = useState<Options[]>([]);
  const [categoryOptions, setCategoryOptions] = useState<Options[]>([]);

  const getMuscleGroupList = async () => {
    try {
      const res = await userAPI.getMuscleGroup();
      const list = res.data as MuscleGroup[];
      const muscleList = list.map((item) => {
        return {
          label: item.name,
          value: item.name,
        };
      });
      setMuscleOptions(muscleList);
    } catch (error) {
      console.log(error);
    }
  };

  const getCategoryList = async () => {
    try {
      const res = await userAPI.getTrainingCategory();
      const list = res.data as TrainingCategory[];
      const categories = list.map((item) => {
        return {
          label: item.name,
          value: item.name,
        };
      });
      setCategoryOptions(categories);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getCategoryList();
    getMuscleGroupList();
  }, []);

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="training-exercise-info"
        data-hideattribute="ProductPage.HideInfoBlock"
        id="training-exercise-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target={`#${exercise?.name.replace(/\s/g, '')}`}
            aria-expanded="true"
            aria-controls={exercise?.name.replace(/\s/g, '')}
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-info-lg col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col px-3 text-start">
                Exercise Info:{' '}
                <span className="text-primary">{exercise?.name}</span>
              </div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id={exercise?.name.replace(/\s/g, '')}>
          <div className="card-body">
            <FieldTemplate
              label="ID"
              fieldID="id"
              fieldType="text"
              disabled={true}
            />
            <FieldTemplate label="Name" fieldID="name" fieldType="text" />
            <FieldTemplate
              label="Description/Instruction"
              fieldID="description"
              fieldType="text"
            />
            <FieldTemplate
              label="Mechanics"
              fieldID="mechanics"
              fieldType="text"
            />

            {/* <FieldTemplate
              label="Force Type"
              fieldID="forceType"
              fieldType="text"
            /> */}

            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end me-3">
                  <label
                    className="col-form-label col fs-5 px-1"
                    htmlFor="forceType"
                  >
                    Force Types
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className="input-group mb-2">
                  <Field
                    className="form-control rounded-start border-black"
                    id="forceType"
                    name="forceType"
                  />
                  <span
                    className="btn btn-secondary rounded-end"
                    onClick={addForceType}
                  >
                    +
                  </span>
                </div>
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="forceType" />
                </div>
                <div>
                  <ul>
                    {forceType.map((item) => (
                      <li key={item} className="border btn m-2 px-3 pe-none">
                        {item}
                        <span
                          onClick={() => removeForceType(item)}
                          className="pe-auto ms-4 text-danger"
                        >
                          x
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>

            <FieldTemplate
              label="Category"
              fieldID="category"
              fieldType="none"
              fieldClass="custom-select w-100"
              options={categoryOptions}
              component={CustomSelect}
              placeholder="Select Category..."
              ismulti={true}
            />
            <FieldTemplate
              label="Muscle Group"
              fieldID="primaryMuscles"
              fieldType="none"
              fieldClass="custom-select w-100"
              options={muscleOptions}
              component={CustomSelect}
              placeholder="Select Muscle Group..."
              ismulti={true}
            />
            <FieldTemplate
              label="Secondary Muscle Group"
              fieldID="secondaryMuscles"
              fieldType="none"
              fieldClass="custom-select w-100"
              options={muscleOptions}
              component={CustomSelect}
              placeholder="Select Secondary Muscle Group..."
              ismulti={true}
            />
            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end me-3">
                  <label
                    className="col-form-label col fs-5 px-1"
                    htmlFor="equipments"
                  >
                    Equipments
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className="input-group mb-2">
                  <Field
                    className="form-control rounded-start border-black"
                    id="equipments"
                    name="equipments"
                  />
                  <span
                    className="btn btn-secondary rounded-end"
                    onClick={addItem}
                  >
                    +
                  </span>
                </div>
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="equipments" />
                </div>
                <div>
                  <ul>
                    {equipments?.map((item) => (
                      <li key={item} className="border btn m-2 px-3 pe-none">
                        {item}
                        <span
                          onClick={() => removeItem(item)}
                          className="pe-auto ms-4 text-danger"
                        >
                          x
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                  <label
                    className="col-form-label col fs-5 px-1 text-center"
                    htmlFor="file"
                  >
                    Upload Attachment
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className={`input-group mt-2`}>
                  <input
                    id="preview"
                    name="preview"
                    type="file"
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFieldValue!('preview', event?.target?.files![0]);
                      setFile({
                        src: URL.createObjectURL(event?.target?.files![0]),
                        type: event?.target?.files![0].type,
                      });
                    }}
                  />
                </div>
                <br />
                {file && (
                  <>
                    {file.type.includes('image') ? (
                      <Image
                        loader={myImageLoader}
                        className="mt-3"
                        src={file.src}
                        height={200}
                        width={200}
                        alt="animation"
                      />
                    ) : (
                      <div className="embed-responsive embed-responsive-16by9">
                        <video
                          width="240"
                          height="200"
                          controls={true}
                          className="embed-responsive-item"
                        >
                          <source src={file.src} type="video/mp4" />
                        </video>
                      </div>
                    )}
                  </>
                )}

                {!file &&
                  exercise &&
                  (exercise.preview ? (
                    <div className="embed-responsive embed-responsive-16by9">
                      <Image
                        src={exercise.preview}
                        height={100}
                        width={100}
                        loader={myImageLoader}
                        alt="Preview"
                      />
                    </div>
                  ) : (
                    'No attachment found'
                  ))}
              </div>
            </div>
            <FieldTemplate
              isRequired={true}
              label="Duration"
              fieldID="duration"
              fieldType="number"
              min={1}
            />
            <div className="row">
              <div className="col-md-3"></div>
              <div className="col-md-9">
                <button
                  type="submit"
                  name="save"
                  className="btn btn-primary my-3"
                  onClick={onClickNotify}
                >
                  {/* <i className="bi bi-save" /> */}
                  <p className="float-end mx-1 my-0">Update </p>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ExerciseInfo;
