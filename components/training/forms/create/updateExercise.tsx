import { useAppSelector } from '@/redux-hooks';
import { Form, Formik } from 'formik';
import { ExpertiseLevel, ICreateBaseExcercise } from 'models';
import { FC, useState } from 'react';
import { handleMediaUpload } from 'utils/handleMediaUpload';
import ExerciseInfo from './exerciseInfo';

interface Props {
  exercise: ICreateBaseExcercise;
  exercises: ICreateBaseExcercise[];
  setExercises: Function;
  setTrainings?: Function;
  trainings?: any;
  level?: string;
}

const UpdateExercise: FC<Props> = ({
  exercise,
  setExercises,
  exercises,
  setTrainings,
  trainings,
  level,
}) => {
  const [equipments, setEquipments] = useState<string[]>(exercise.equipments!);
  const [forceType, setforceType] = useState<string[]>(exercise.forceType!);
  const trainingInfo = useAppSelector(
    (state) => state.persistedReducer.training
  );
  const token = useAppSelector((state) => state.persistedReducer.auth.token);

  const handleSubmit = async (data: any, duration: any) => {
    if (data.preview.name) {
      const fileData = {
        featureName: 'training',
        filenames: [data.preview.name],
      };
      const fileInfo = (await handleMediaUpload(
        fileData,
        data.preview,
        token,
        true
      )) as string;
      data.preview = fileInfo;
    } else {
      data.preview = exercise?.previewKey;
    }
    const filteredExercise = exercises.filter(
      (exercise) => exercise.id !== data.id
    );
    data.equipments = equipments;
    data.forceType = forceType;
    filteredExercise.push(data!);
    setExercises(filteredExercise);
    const trainingList = trainings;

    if (level !== 'INTERMEDIATE') {
      const indexToRemove = trainingList.findIndex(
        (item: any) => item.baseExercise.id === data.id
      );

      if (indexToRemove !== -1) {
        trainingList.splice(indexToRemove, 1);
      }

      trainingList.push({
        baseExercise: data,
        duration: duration,
        expertiseLevel:
          level === 'BEGINNER'
            ? ExpertiseLevel.BEGINNER
            : ExpertiseLevel.ADVANCE,
      });
    } else {
      const indexToRemove = trainingList.findIndex(
        (item: any) => item.baseExercise.id === data.id
      );

      if (indexToRemove !== -1) {
        trainingList.splice(indexToRemove, 1);
      }

      trainingList.push({
        baseExercise: data,
        duration: duration,
        programId: trainingInfo.bbprogram,
        week: trainingInfo.selectedWeek,
        day: trainingInfo.selectedDay,
      });
    }
    console.log(trainingList);
    setTrainings!(trainingList);
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          id: exercise?.id!,
          name: exercise?.name || '',
          description: exercise?.description || '',
          mechanics: exercise?.mechanics || '',
          category: exercise?.category || [],
          type: exercise?.type || '',
          forceType: exercise?.forceType || [],
          primaryMuscles: exercise?.primaryMuscles || [],
          secondaryMuscles: exercise?.secondaryMuscles || [],
          equipments: exercise?.equipments || [],
          preview: exercise?.previewKey || '',
          duration: 5,
        }}
        onSubmit={(values, actions) => {
          const exercise = {
            id: values.id!,
            name: values.name,
            description: values.description,
            mechanics: values.mechanics,
            category: values.category,
            type: values.type,
            forceType: values.forceType,
            primaryMuscles: values.primaryMuscles,
            secondaryMuscles: values.secondaryMuscles,
            equipments: values.equipments,
            preview: values.preview,
          };
          handleSubmit(exercise, values.duration);
          actions.setSubmitting(false);
        }}
        //validationSchema={updateTrainingExerciseSchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div>
                <ExerciseInfo
                  setEquipments={setEquipments}
                  setFieldValue={formikprops.setFieldValue}
                  equipments={equipments}
                  forceType={forceType}
                  setForceType={setforceType}
                  exercise={exercise}
                />
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default UpdateExercise;
