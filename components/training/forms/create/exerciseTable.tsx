import Link from 'next/link';
import { FC } from 'react';

import Table from '@/components/global/table/table';
import { config } from 'config';
import { Field } from 'formik';
import myImageLoader from 'image/loader';
import { ICreateBaseExcercise } from 'models';
import Image from 'next/image';
import { handlePagination } from 'utils/handlePagination';

interface Props {
  exerciseList: ICreateBaseExcercise[];
  setFieldValue?: Function;
  setEquipments?: Function;
  edit?: boolean;
  setSelectedExercises: Function;
  selectedExercises: ICreateBaseExcercise[];
  setSkip: Function;
  skip: number;
}

const ExerciseList: FC<Props> = ({
  exerciseList,
  setFieldValue,
  setEquipments,
  edit,
  setSelectedExercises,
  selectedExercises,
  setSkip,
  skip,
}) => {
  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const columns = [
    {
      label: 'Select',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Field
            type="checkbox"
            name="selectedExerciseIDs"
            id="selectedExerciseIDs"
            value={data[key]}
            onClick={(e: any) => {
              if (e.target.checked) {
                const list = new Set(selectedExercises);
                list.add(data);
                setSelectedExercises(Array.from(list));
              } else {
                const list = new Set(selectedExercises);
                list.delete(data);
                setSelectedExercises(Array.from(list));
              }
            }}
          />
        </td>
      ),
    },
    {
      label: 'Attachment',
      path: 'preview',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Image
            loader={myImageLoader}
            height={70}
            width={70}
            src={data[key]}
            alt="Exercise Preview"
          />
        </td>
      ),
    },
    {
      label: 'Name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    // {
    //   label: 'Type',
    //   path: 'type',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="text-center align-middle">{data[key]}</td>
    //   ),
    // },
    {
      label: 'Categories',
      path: 'category',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.category ? data?.category[0] : '---'}
          {data?.category?.map((category: any, index: any) =>
            index > 0 ? ` , ${category}` : ''
          )}
        </td>
      ),
    },
    {
      label: 'Muscle',
      path: 'primaryMuscles',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.primaryMuscles ? data?.primaryMuscles[0] : '---'}
          {data?.primaryMuscles?.map((muscle: any, index: any) =>
            index > 0 ? ` , ${muscle}` : ''
          )}
        </td>
      ),
    },

    // {
    //   label: 'View',
    //   path: 'id',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="text-center align-middle">
    //       <Link
    //         href={{
    //           pathname: `/exercise-module/exercises/view/[id]`,
    //           query: { id: data?.[key] },
    //         }}
    //         passHref
    //         legacyBehavior
    //       >
    //         <button className="btn btn-default btn-outline-primary">
    //           <span>
    //             <i className="bi bi-eye me-2 align-middle"></i>
    //           </span>
    //           View
    //         </button>
    //       </Link>
    //     </td>
    //   ),
    // },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          {exerciseList?.length! > 0 ? (
            <>
              {' '}
              <Table
                items={exerciseList}
                columns={columns}
                onClickForSort={onClickForSort}
              />
            </>
          ) : (
            <p>
              No exercise found.{' '}
              <span>
                <Link href="/exercise-module/exercises/add-new">
                  Create one first.
                </Link>
              </span>
            </p>
          )}
        </div>
      </div>
    </>
  );
};

export default ExerciseList;
