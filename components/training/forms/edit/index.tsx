import { userAPI } from '@/APIs';
import CustomSelect from '@/components/common/customSelectInput';
import FieldTemplate from '@/components/common/fieldTemplate';
import { ErrorMessage, Field } from 'formik';
import myImageLoader from 'image/loader';
import { BodyBuildingProgram, MuscleGroup, TrainingCategory } from 'models';
import Image from 'next/image';
import { FC, useEffect, useState } from 'react';
import SelectBBProgram from '../create/selectFields/bbProgramSelect';

interface Options {
  label: string;
  value: string;
}

interface Props {
  setFieldValue: Function;
  setEquipments: Function;
  equipments: string[];
  forceType: string[];
  setForceType: Function;
  training: any;
  level: string;
}

const TrainingEditForm: FC<Props> = ({
  setFieldValue,
  setEquipments,
  equipments,
  forceType,
  setForceType,
  training,
  level,
}) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');
  const [file, setFile] = useState<any>();
  const [levels, setLevels] = useState<Options[]>([
    {
      label: 'BEGINNER',
      value: 'Beginner',
    },
    // {
    //   label: 'Intermediate',
    //   value: 'Intermediate',
    // },
    {
      label: 'ADVANCE',
      value: 'Advance',
    },
  ]);

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  const addEquipment = () => {
    const equipmentsList = (
      document.getElementById('equipments') as HTMLInputElement
    ).value;
    if (equipmentsList.length > 0) {
      setFieldValue('equipments', '');
      setEquipments([...equipments, equipmentsList]);
    }
  };

  const removeEquipment = (equipment: string) => {
    const equipmentsList = equipments.filter(
      (singleitem) => singleitem != equipment
    );
    setEquipments(equipmentsList);
  };

  const addForceType = () => {
    const forces = (document.getElementById('forceType') as HTMLInputElement)
      .value;
    setFieldValue('forceType', '');
    if (forces) setForceType([...forceType, forces]);
  };

  const removeForceType = (force: string) => {
    const forces = forceType.filter((singleitem) => singleitem != force);
    setForceType(forces);
  };

  const [muscleOptions, setMuscleOptions] = useState<Options[]>([]);
  const [categoryOptions, setCategoryOptions] = useState<Options[]>([]);
  const [bbPrograms, setBBPrograms] = useState<Options[]>([]);
  const [weeks, setWeeks] = useState<Options[]>([]);
  const [selectedBBProgram, setSelectedBBProgram] = useState<string>();
  const [days, setDays] = useState<Options[]>([
    { label: 'SATURDAY', value: 'SATURDAY' },
    { label: 'SUNDAY', value: 'SUNDAY' },
    { label: 'MONDAY', value: 'MONDAY' },
    { label: 'TUESDAY', value: 'TUESDAY' },
    { label: 'WEDNESDAY', value: 'WEDNESDAY' },
    { label: 'THURSDAY', value: 'THURSDAY' },
    { label: 'FRIDAY', value: 'FRIDAY' },
  ]);

  const handleSetWeeks = async () => {
    const duration = (
      (await userAPI.getBBProgram(selectedBBProgram))
        .data as BodyBuildingProgram
    ).programDuration;
    let weekList = [];
    for (let i = 1; i <= duration; i++) {
      weekList.push({
        label: '' + i,
        value: '' + i,
      });
    }
    setWeeks(weekList);
  };

  const getMuscleGroupList = async () => {
    try {
      const res = await userAPI.getMuscleGroup();
      const list = res.data as MuscleGroup[];
      const muscleList = list.map((item) => {
        return {
          label: item.name,
          value: item.name,
        };
      });
      setMuscleOptions(muscleList);
    } catch (error) {
      console.log(error);
    }
  };

  const getCategoryList = async () => {
    try {
      const res = await userAPI.getTrainingCategory();
      const list = res.data as TrainingCategory[];
      const categories = list.map((item) => {
        return {
          label: item.name,
          value: item.name,
        };
      });
      setCategoryOptions(categories);
    } catch (error) {
      console.log(error);
    }
  };

  const getBBPrograms = async () => {
    const res = await userAPI.getBBProgram();
    const list = res.data as BodyBuildingProgram[];
    const bbList = list.map((item) => {
      return {
        label: item.name,
        value: item.id!,
      };
    });
    setBBPrograms(bbList);
  };

  useEffect(() => {
    getCategoryList();
    getMuscleGroupList();
    getBBPrograms();
  }, []);

  useEffect(() => {
    handleSetWeeks();
  }, [selectedBBProgram]);

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="training-info"
        data-hideattribute="ProductPage.HideInfoBlock"
        id="training-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#trainingInfoTab"
            aria-expanded="true"
            aria-controls="trainingInfoTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-info-lg col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col px-3 text-start">Training Info</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="trainingInfoTab">
          <div className="card-body">
            <FieldTemplate
              label="ID"
              fieldID="id"
              fieldType="text"
              disabled={true}
            />

            {level !== 'INTERMEDIATE' && (
              <FieldTemplate
                label="Level"
                fieldID="expertiseLevel"
                fieldType="none"
                fieldClass="custom-select w-100"
                options={levels}
                component={CustomSelect}
                placeholder={training.expertiseLevel}
                ismulti={false}
                disabled={true}
              />
            )}

            {level === 'INTERMEDIATE' && (
              <SelectBBProgram
                bbPrograms={bbPrograms}
                setSelectedBBProgram={setSelectedBBProgram}
                selectedLevel={`INTERMEDIATE`}
                setFieldValue={setFieldValue}
                edit={true}
              />
            )}

            {level === 'INTERMEDIATE' && (
              <FieldTemplate
                label="Weeks"
                fieldID="week"
                fieldType="none"
                fieldClass="custom-select w-100"
                options={weeks}
                component={CustomSelect}
                placeholder="Select Week..."
                ismulti={false}
              />
            )}

            {level === 'INTERMEDIATE' && (
              <FieldTemplate
                label="Day"
                fieldID="day"
                fieldType="none"
                fieldClass="custom-select w-100"
                options={days}
                component={CustomSelect}
                placeholder="Select Day..."
                ismulti={false}
              />
            )}

            <FieldTemplate
              label="Name"
              fieldID="name"
              fieldType="text"
              //disabled={edit ? true : false}
            />

            <FieldTemplate
              label="Duration"
              fieldID="duration"
              fieldType="number"
              min={1}
              //disabled={edit ? true : false}
            />

            <FieldTemplate
              label="Description/Instruction"
              fieldID="description"
              fieldType="text"
            />

            <FieldTemplate
              label="Mechanics"
              fieldID="mechanics"
              fieldType="text"
            />

            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end me-3">
                  <label
                    className="col-form-label col fs-5 px-1"
                    htmlFor="forceType"
                  >
                    Force Types
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className="input-group mb-2">
                  <Field
                    className="form-control rounded-start border-black"
                    id="forceType"
                    name="forceType"
                  />
                  <span
                    className="btn btn-secondary rounded-end"
                    onClick={addForceType}
                  >
                    +
                  </span>
                </div>
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="forceType" />
                </div>
                <div>
                  <ul>
                    {forceType.map((item) => (
                      <li key={item} className="border btn m-2 px-3 pe-none">
                        {item}
                        <span
                          onClick={() => removeForceType(item)}
                          className="pe-auto ms-4 text-danger"
                        >
                          x
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>

            <FieldTemplate
              label="Category"
              fieldID="category"
              fieldType="none"
              fieldClass="custom-select w-100"
              options={categoryOptions}
              component={CustomSelect}
              placeholder="Select Category..."
              ismulti={true}
            />

            <FieldTemplate
              label="Muscle Group"
              fieldID="primaryMuscles"
              fieldType="none"
              fieldClass="custom-select w-100"
              options={muscleOptions}
              component={CustomSelect}
              placeholder="Select Muscle Group..."
              ismulti={true}
            />

            <FieldTemplate
              label="Secondary Muscle Group"
              fieldID="secondaryMuscles"
              fieldType="none"
              fieldClass="custom-select w-100"
              options={muscleOptions}
              component={CustomSelect}
              placeholder="Select Secondary Muscle Group..."
              ismulti={true}
            />

            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end me-3">
                  <label
                    className="col-form-label col fs-5 px-1"
                    htmlFor="equipments"
                  >
                    Equipments
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className="input-group mb-2">
                  <Field
                    className="form-control rounded-start border-black"
                    id="equipments"
                    name="equipments"
                  />
                  <span
                    className="btn btn-secondary rounded-end"
                    onClick={addEquipment}
                  >
                    +
                  </span>
                </div>
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="equipments" />
                </div>
                <div>
                  <ul>
                    {equipments.map((item) => (
                      <li key={item} className="border btn m-2 px-3 pe-none">
                        {item}
                        <span
                          onClick={() => removeEquipment(item)}
                          className="pe-auto ms-4 text-danger"
                        >
                          x
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>

            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                  <label
                    className="col-form-label col fs-5 px-1 text-center"
                    htmlFor="file"
                  >
                    Upload Attachment
                    <span className="required text-danger ">*</span>
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className={`input-group mt-2`}>
                  <input
                    id="preview"
                    name="preview"
                    type="file"
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFieldValue!('preview', event?.target?.files![0]);
                      setFile({
                        src: URL.createObjectURL(event?.target?.files![0]),
                        type: event?.target?.files![0].type,
                      });
                    }}
                  />
                </div>
                <br />
                {file && (
                  <>
                    {file.type.includes('image') ? (
                      <Image
                        loader={myImageLoader}
                        className="mt-3"
                        src={file.src}
                        height={200}
                        width={200}
                        alt="animation"
                      />
                    ) : (
                      <div className="embed-responsive embed-responsive-16by9">
                        <video
                          width="240"
                          height="200"
                          controls={true}
                          className="embed-responsive-item"
                        >
                          <source src={file.src} type="video/mp4" />
                        </video>
                      </div>
                    )}
                  </>
                )}

                {!file &&
                  training &&
                  (training.preview ? (
                    <div className="embed-responsive embed-responsive-16by9">
                      <Image
                        loader={myImageLoader}
                        className="mt-3"
                        src={training.preview}
                        height={200}
                        width={200}
                        alt="animation"
                      />
                    </div>
                  ) : (
                    'No attachment found'
                  ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TrainingEditForm;
