import { array, number, object, string } from 'yup';

export const trainingSchema = object().shape({
  //level: string().required('This field must not be empty'),
  category: string().when('level', {
    is: 'BEGINNER',
    then: string().required('One category is required'),
    otherwise: string().notRequired(),
  }),
  muscleGroup: string().when('level', {
    is: 'ADVANCE',
    then: string().required('One muscle group is required'),
    otherwise: string().notRequired(),
  }),
  bbprogram: string().when('level', {
    is: 'INTERMEDIATE',
    then: string().required('One body building program is required'),
    otherwise: string().notRequired(),
  }),
  weeks: number().when('bbprogram', {
    is: true,
    then: number().required('Field is required'),
  }),
  selectedExerciseIDs: array().when('level', {
    is: 'BEGINNER' || 'ADVANCE',
    then: array().min(1, 'Select at least one exercise to proceed'),
    otherwise: array().notRequired(),
  }),
});
