import { array, mixed, number, object, string } from 'yup';

export const updateTrainingExerciseSchema = object().shape({
  name: string()
    .min(2, 'This field must be at least 2 characters long')
    .max(5000, 'This field must be at most 50 characters long')
    .required('This field must not be empty'),
  description: string(),
  mechanics: string(),
  category: array()
    .min(1, 'You must select one')
    .required('This field is required'),
  //type: string().required('This field must not be empty'),
  forceType: array().nullable(),
  primaryMuscles: array()
    .min(1, 'You must select one')
    .required('This field is required'),
  secondaryMuscles: array(),
  equipments: array().nullable(),
  preview: mixed().required('File is required'),
  duration: number().min(1).required('This field must not be empty'),
});
