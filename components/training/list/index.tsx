import Link from 'next/link';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import Table from '@/components/global/table/table';
import { config } from 'config';
import myImageLoader from 'image/loader';
import { ExpertiseLevel } from 'models';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { toast } from 'react-toastify';
import { handlePagination } from 'utils/handlePagination';

interface Props {
  trainingList: any;
  setTrainings: Function;
  setSkip: Function;
  skip: number;
}

const TrainingList: FC<Props> = ({
  trainingList,
  setTrainings,
  setSkip,
  skip,
}) => {
  const router = useRouter();
  const level = router.query.level as string;

  const [TrainingID, setTrainingID] = useState('');

  const onChangeForList = async (skip: number) => {
    try {
      let res;
      if (router.pathname.includes('beginner')) {
        res = await userAPI.getTrainingList(
          ExpertiseLevel.BEGINNER,
          skip,
          config.limit
        );
      } else if (router.pathname.includes('intermediate')) {
        res = await userAPI.getIntermediateTrainingList(skip, config.limit);
      } else {
        res = await userAPI.getTrainingList(
          ExpertiseLevel.ADVANCE,
          skip,
          config.limit
        );
      }
      if ('data' in res) {
        setTrainings(res.data);
        setSkip(0);
      } else toast.error('Failed to fetch updated list');
    } catch (error) {
      console.log(error);
    }
  };

  const deleteTraining = async () => {
    try {
      let res;
      res = await userAPI.deleteTraining(TrainingID);
      if ('data' in res) {
        onChangeForList(0);
        toast.success('Training deleted successfully');
      } else {
        res = await userAPI.deleteIntermediateTraining(TrainingID);
        if ('data' in res) {
          onChangeForList(0);
          toast.success('Training deleted successfully');
        } else {
          toast.error(`Can't delete training`);
        }
      }
    } catch (error) {}
    setModal({
      ...modal,
      delete: false,
    });
  };

  const onClickForDelete = (id: string) => {
    setTrainingID(id);
    setModal({ ...modal, delete: true });
  };

  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const [modal, setModal] = useState({
    delete: false,
  });

  const columns = [
    // {
    //   label: 'ID',
    //   path: 'id',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="text-center align-middle">{data[key]}</td>
    //   ),
    // },
    {
      label: 'Attachment',
      path: 'preview',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Image
            src={data[key]}
            height={100}
            width={100}
            alt="Training Attachment"
            loader={myImageLoader}
          />
        </td>
      ),
    },
    {
      label: 'Level',
      path: 'expertiseLevel',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data[key] ? data[key] : 'INTERMEDIATE'}
        </td>
      ),
    },
    {
      label: 'Name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Duration',
      path: 'duration',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Week',
      path: 'week',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data[key] ? data[key] : '---'}
        </td>
      ),
    },
    {
      label: 'Day',
      path: 'day',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data[key] ? data[key] : '---'}
        </td>
      ),
    },
    // {
    //   label: 'Categories',
    //   path: 'category',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="text-center align-middle">
    //       {data?.category[0] ? data?.category[0] : '---'}
    //       {data?.category?.map((category: any, index: any) =>
    //         index > 0 ? ` , ${category}` : ''
    //       )}
    //     </td>
    //   ),
    // },
    // {
    //   label: 'Muscle',
    //   path: 'primaryMuscles',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="text-center align-middle">
    //       {data?.primaryMuscles[0] ? data?.primaryMuscles[0] : '---'}
    //       {data?.primaryMuscles?.map((muscle: any, index: any) =>
    //         index > 0 ? ` , ${muscle}` : ''
    //       )}
    //     </td>
    //   ),
    // },
    {
      label: 'Edit',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/training/edit/[id]`,
              query: { id: data?.[key] },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-info">
              <span>
                <i className="bi bi-pencil me-2 align-middle"></i>
              </span>
              Edit
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'View',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/training/view/[id]`,
              query: { id: data?.[key] },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },

    {
      label: 'Delete',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <button
            className="btn btn-default btn-outline-danger"
            onClick={() => onClickForDelete(data.id)}
          >
            <i className="bi bi-trash3-fill me-2 align-middle"></i>
            Delete
          </button>
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={trainingList}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>
      {modal.delete ? (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              // close modal when outside of modal is clicked
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                // do not close modal if anything inside modal content is clicked
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this item?</p>
                <br />

                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{
                      border: '1px solid gray',
                      backgroundColor: 'gray',
                      color: 'white',
                      marginRight: '10px',
                    }}
                    onClick={() =>
                      setModal({
                        ...modal,
                        delete: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => deleteTraining()}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default TrainingList;
