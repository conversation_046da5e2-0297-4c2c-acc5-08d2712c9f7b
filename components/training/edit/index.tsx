import { Form, Formik } from 'formik';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import TrainingEditForm from '@/components/training/forms/edit';
import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { ExpertiseLevel } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';
import { handleMediaUpload } from 'utils/handleMediaUpload';
import { enableEdit, showModal } from '@/toolkit/modalSlice';

interface Props {
  trainingToEdit: any;
  level: string;
}

const EditTraining: FC<Props> = ({ trainingToEdit, level }) => {
  const router = useRouter();
  const token = useAppSelector((state) => state.persistedReducer.auth.token);

  const [training, setTraining] = useState(trainingToEdit);
  const [equipments, setEquipments] = useState<string[]>(training.equipments!);
  const [forceType, setForceTypes] = useState<string[]>(training.forceType!);

  const dispatch = useAppDispatch();
  const edit = useAppSelector((state) => state.persistedReducer.modal.edit);
  const [updateData, setUpdateData] = useState<any>();

  const handleSubmit = async (data: any) => {
    try {
      let attachment;
      if (data.preview.name) {
        const fileData = {
          featureName: 'training',
          filenames: [data.preview.name],
        };
        const fileInfo = (await handleMediaUpload(
          fileData,
          data.preview,
          token,
          true
        )) as string;
        attachment = fileInfo;
      } else {
        attachment = trainingToEdit.previewKey;
      }
      if (data.expertiseLevel === 'Beginner') {
        data.expertiseLevel = ExpertiseLevel.BEGINNER;
      } else if (data.expertiseLevel === 'Advance') {
        data.expertiseLevel = ExpertiseLevel.ADVANCE;
      }
      let res;
      if (level !== 'INTERMEDIATE') {
        const toUpdate = {
          ...data,
          forceType,
          equipments,
          preview: attachment,
        };
        res = await userAPI.updateTraining(trainingToEdit?.id!, toUpdate);
      } else {
        const toUpdate = {
          ...data,
          forceType,
          equipments,
          preview: attachment,
          week: parseInt(data.week),
        };
        res = await userAPI.updateIntermediateTraining(training.id, toUpdate);
      }
      if ('data' in res) {
        router.push('/training/intermediate');
        toast.success('Training Updated Successfully');
      } else {
        toast.error('Failed To Update Training');
      }
    } catch (error: any) {
      toast.error(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  if (edit === true) {
    handleSubmit(updateData!);
    dispatch(enableEdit(false));
  }

  return (
    <>
      {training ? (
        <Formik
          initialValues={{
            id: training?.id || '',
            expertiseLevel: training?.expertiseLevel || ExpertiseLevel.BEGINNER,
            name: training?.name || '',
            description: training?.description || '',
            mechanics: training?.mechanics || '',
            category: training?.category || [],
            type: training?.type || '',
            forceType: training?.forceType || [],
            primaryMuscles: training?.primaryMuscles || [],
            secondaryMuscles: training?.secondaryMuscles || [],
            equipments: training?.equipments || [],
            preview: training?.preview || '',
            duration: training?.duration || '',
            day: training?.day || '',
            week: training?.week || 1,
            programId: training?.programId || '',
          }}
          onSubmit={(values, actions) => {
            dispatch(showModal(true));
            setUpdateData(values);

            actions.setSubmitting(false);
          }}
          //validationSchema={}
        >
          {(formikprops) => {
            return (
              <Form
                onSubmit={formikprops.handleSubmit}
                onKeyDown={onKeyDown}
                className="min-vh-100"
              >
                <div className="content-header clearfix pt-4">
                  <h1 className="float-start fs-2">
                    Edit A Training
                    <span className="fs-5 p-3">
                      <Link
                        href={
                          trainingToEdit.expertiseLevel === 'BEGINNER'
                            ? '/training/beginner'
                            : trainingToEdit.expertiseLevel === 'ADVANCE'
                            ? '/training/advance'
                            : '/training/intermediate'
                        }
                        className="text-decoration-none "
                      >
                        <i className="bi bi-arrow-left-circle-fill p-2" />
                        Back to training list
                      </Link>
                    </span>
                  </h1>
                  <div className="float-end">
                    <button
                      type="submit"
                      name="save"
                      className="btn btn-primary m-1"
                    >
                      {/* <i className="bi bi-save" /> */}
                      <p className="float-end mx-1 my-0">Save </p>
                    </button>
                  </div>
                </div>
                <div className="mt-4 pb-5">
                  <TrainingEditForm
                    setFieldValue={formikprops.setFieldValue}
                    setEquipments={setEquipments}
                    setForceType={setForceTypes}
                    forceType={forceType}
                    equipments={equipments}
                    training={training}
                    level={level}
                  />
                </div>
              </Form>
            );
          }}
        </Formik>
      ) : (
        'Something went wrong!'
      )}
    </>
  );
};

export default EditTraining;
