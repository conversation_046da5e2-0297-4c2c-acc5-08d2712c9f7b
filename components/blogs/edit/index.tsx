import React, { useRef, useState, FC } from 'react';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import { userAPI } from '@/APIs';
import { Blog, BlogCategory } from 'models';
import { toast } from 'react-toastify';
import Editor from '@/components/blogs/forms/editor';
import { useRouter } from 'next/router';
import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { enableEdit, showModal } from '@/toolkit/modalSlice';

interface Props {
  blog: Blog;
}

const EditBlog: FC<Props> = ({ blog }) => {
  const router = useRouter();
  const [content, setContent] = useState(blog.content);
  const [title, setTitle] = useState(blog.title);
  const [category, setCategory] = useState<BlogCategory>(blog.category);
  const dispatch = useAppDispatch();
  const edit = useAppSelector((state) => state.persistedReducer.modal.edit);
  const [updateData, setUpdateData] = useState<any>();

  const handleSubmit = async () => {
    try {
      const res = await userAPI.updateBlog(blog.id, {
        title,
        content,
        category,
      });
      if ('data' in res) {
        toast.success('Blog updated successfully');
        router.push('/blog');
      } else {
        toast.error(res?.error.message);
      }
    } catch (error) {}
  };

  if (edit === true) {
    handleSubmit();
    dispatch(enableEdit(false));
  }

  return (
    <div className="">
      <div
        className="content-header clearfix mb-3"
        style={{ paddingTop: '10px' }}
      >
        <h3 className="float-start fs-2">
          Edit Blog
          <span className="fs-5 p-3">
            <Link href="/blog/" className="text-decoration-none">
              <i className="bi bi-arrow-left-circle-fill p-2" />
              <span style={{ fontSize: '14px' }}>Back to Blog list</span>
            </Link>
          </span>
        </h3>
        <div className="float-end">
          <div className="d-flex flex-wrap">
            {' '}
            <button
              type="submit"
              name="save"
              className="btn btn-primary m-1"
              onClick={() => {
                dispatch(showModal(true));
              }}
            >
              {/* <i className="bi bi-save" /> */}
              <p className="float-end mx-1 my-0">Save</p>
            </button>
            <button className="btn btn-primary m-1" onClick={handleSubmit}>
              Save & publish
            </button>
          </div>
        </div>
      </div>

      <Editor
        setContent={setContent}
        setTitle={setTitle}
        setCategory={setCategory}
        edit={true}
        blog={blog}
      />

      {/* <button onClick={handleShow}>Show</button>
      <div id="contentDiv"></div> */}
    </div>
  );
};

export default EditBlog;
