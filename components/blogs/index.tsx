import { config } from 'config';
import { Blog, Manufacturer } from 'models';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { userAPI } from '../../APIs/index';
import BlogList from './List/blogList';
import PrevNextPagination from '../common/newPagination';
import { useAppSelector } from '@/redux-hooks';

const List = () => {
  const [blogData, setBlogData] = useState([]);
  const [skip, setSkip] = useState(1);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const token = useAppSelector((state) => state.persistedReducer.auth.token);

  const getAllBlogs = async () => {
    try {
      // const res = await userAPI.getWpBlogs(skip, limit);
      // if ('data' in res!) {
      //   if (res.data.length === 0) {
      //     setDisableNext(true);
      //     if (skip === 0) setBlogData(res.data);
      //   } else {
      //     setDisableNext(false);
      //     setBlogData(res.data);
      //   }
      // } else {
      //   toast.error(res?.error.message);
      // }
      const res = await userAPI.getWpBlogs(skip, limit, token);
      if ('status' in res.data) {
        setDisableNext(true);
      } else {
        setBlogData(res.data);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getAllBlogs();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Blogs</div>
          <button
            className="btn btn-primary"
            onClick={() => window.open(config.wpBlogDashboard)}
          >
            Add new
          </button>
        </div>
        <div>
          {blogData ? (
            <>
              <BlogList
                blogData={blogData}
                setBlogData={setBlogData}
                setSkip={setSkip}
                skip={skip}
              />
              {/* <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              /> */}
              <div className="d-flex justify-content-between mt-3">
                <button
                  className="btn btn-primary p-2"
                  onClick={() => {
                    const l = skip;
                    setSkip(l - 1);
                  }}
                  disabled={skip === 1}
                >
                  Previous
                </button>
                <button
                  className="btn btn-primary p-2"
                  onClick={() => {
                    const l = skip;
                    setSkip(l + 1);
                  }}
                  disabled={disableNext}
                >
                  Next
                </button>
              </div>
            </>
          ) : (
            'No data found'
          )}
        </div>
      </main>
    </>
  );
};

export default List;
