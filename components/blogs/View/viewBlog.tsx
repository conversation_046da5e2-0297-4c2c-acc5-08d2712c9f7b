import Link from 'next/link';
import { FC } from 'react';
import BlogInfoCard from './viewCards/blogInfo';
import { Blog } from 'models';
interface Props {
  blog: any;
}
const ViewBlogComp: FC<Props> = ({ blog }) => {
  console.log(blog);
  return (
    <>
      {blog ? (
        <div>
          <div className="content-header clearfix mt-3">
            <h1 className="float-start fs-2">
              View Blogs details
              <span className="fs-5 p-3">
                <Link href={'/blog'} className="text-decoration-none">
                  <i className="bi bi-arrow-left-circle-fill p-2" />
                  Back to Blogs list
                </Link>
              </span>
            </h1>
          </div>

          <div className="mt-4">
            <BlogInfoCard blog={blog} />
          </div>
        </div>
      ) : (
        ''
      )}
    </>
  );
};

export default ViewBlogComp;
