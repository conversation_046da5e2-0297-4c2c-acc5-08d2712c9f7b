import myImageLoader from 'image/loader';
import Image from 'next/image';
import { FC, useEffect } from 'react';
import SingleView from './singleView';
import { Blog } from 'models';
import { trimDescription } from 'utils/trim';
interface Props {
  blog: any;
}
const BlogInfoCard: FC<Props> = ({ blog }) => {
  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="meta"
        id="meta"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <div className="card-title row align-items-center  ps-2 pt-2">
            <i className="bi bi-meta col-1" style={{ fontSize: '25px' }} />
            <div className="fs-5 col px-3 text-start">Blogs Info</div>
          </div>
        </div>
        <div className="" id="metaTab">
          <div className="card-body">
            <SingleView
              label="Blog Title"
              value={blog.title.rendered}
              toolkitMessage="toolkitMessage"
            />
            {/* <SingleView
              label="Category"
              value={blog.category}
              toolkitMessage="toolkitMessage"
            /> */}
            <SingleView
              label="Author Name"
              value={blog._embedded!.author[0].name}
              toolkitMessage="toolkitMessage"
            />
            <div className="form-group row container">
              <div className="col-md-4">
                <div className="label-wrapper row float-md-end">
                  <label
                    className="col-form-label col pe-2 fw-bold mb-2"
                    htmlFor="content"
                  >
                    Blog Content:
                  </label>
                </div>
              </div>
              <div className="col-md-8 ps-4 py-auto my-auto">
                <div
                  className="row text-break"
                  id="blogContent"
                  dangerouslySetInnerHTML={{
                    __html: trimDescription(blog['content']['rendered'], 1000),
                  }}
                ></div>
                <button
                  className="text-primary bg-transparent border-0"
                  onClick={() => window.open(blog.link)}
                >
                  Read More...
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BlogInfoCard;
