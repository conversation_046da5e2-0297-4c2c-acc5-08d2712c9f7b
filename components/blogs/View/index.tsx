import { useRouter } from 'next/router';
import { FC, useEffect, useState } from 'react';
import { userAPI } from '../../../APIs';
import ViewBlogComp from './viewBlog';
import { Blog } from 'models';
import { toast } from 'react-toastify';
import { useAppSelector } from '@/redux-hooks';

const ViewSingleBlog: FC = () => {
  const router = useRouter();
  const [ready, setReady] = useState(false);
  const [blog, setBlog] = useState();
  const id = '' + `${router.query.id}`;

  const token = useAppSelector((state) => state.persistedReducer.auth.token);

  const getBlog = async () => {
    try {
      // const res = await userAPI.getBlogById(id);
      // if ('data' in res) {
      //   setBlog(res.data);
      // } else {
      //   toast.error(res?.error?.message);
      // }
      const res = await userAPI.getWpSingleBlog(id, token);
      setBlog(res[0]);
    } catch (error) {}
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getBlog();
    }
  }, [router.isReady]);

  if (!ready) {
    return null;
  }

  return (
    <>
      <div className="bg-light px-5">
        <main>{blog ? <ViewBlogComp blog={blog} /> : 'No blog Found'}</main>
      </div>
    </>
  );
};

export default ViewSingleBlog;
