import { Blog, BlogCategory } from 'models';
import dynamic from 'next/dynamic';
import { useState } from 'react';

interface Props {
  setContent: (content: string) => void;
  setTitle: Function;
  setCategory: Function;
  edit?: boolean;
  blog?: Blog;
}

const SunEditor = dynamic(() => import('suneditor-react'), {
  ssr: false,
});

const Editor: React.FC<Props> = ({
  setContent,
  setTitle,
  setCategory,
  edit,
  blog,
}) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');

  const options = {
    buttonList: [
      ['font', 'fontSize', 'formatBlock'],
      ['paragraphStyle', 'blockquote'],
      ['bold', 'underline', 'italic', 'strike', 'subscript', 'superscript'],
      ['align', 'horizontalRule', 'list', 'table', 'lineHeight'],
      ['fontColor', 'hiliteColor', 'textStyle', 'template'],
      ['outdent', 'indent'],
      ['undo', 'redo'],
      ['removeFormat'],
      ['outdent', 'indent'],
      ['link', 'image', 'video', 'audio'],
      ['preview', 'print'],
      ['fullScreen', 'showBlocks', 'codeView'],
    ],
  };

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };
  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="editor-content"
        id="editor-content"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#editorTab"
            aria-expanded="true"
            aria-controls="editorTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <div className="fs-5 col text-start px-3">
                <i
                  className="bi bi-info-lg col-1 px-1"
                  style={{ fontSize: '25px' }}
                />
                Blog Content
              </div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="editorTab">
          <div className="card-body">
            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                  <label
                    className="col-form-label col fs-5 px-1 text-center"
                    htmlFor="title"
                  >
                    Blog Title
                    <span className="required text-danger ">*</span>
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <textarea
                  defaultValue={blog?.title}
                  id="title"
                  name="title"
                  className="border-bottom rounded-0 border border-0 border-2 p-2 shadow-none w-100"
                  onChange={(e) => {
                    setTitle(e.target.value);
                  }}
                />
              </div>
            </div>
            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                  <label
                    className="col-form-label col fs-5 px-1 text-center"
                    htmlFor="category"
                  >
                    Blog Category
                    <span className="required text-danger ">*</span>
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                {/* <input
                  id="category"
                  name="category"
                  className="border-bottom rounded-0 border border-0 border-2 p-2 shadow-none w-100"
                  onChange={(e) => {
                    setCategory(e.target.value);
                  }}
                /> */}
                <select
                  defaultValue={blog?.category}
                  id="category"
                  name="category"
                  className="w-100 rounded border border-gray p-2"
                  onChange={(e) => {
                    e.preventDefault();
                    setCategory(e.target.value);
                  }}
                >
                  <option className="text-capitalize" value="" disabled>
                    Please select one
                  </option>
                  <option
                    className="text-capitalize"
                    value={BlogCategory.ENTERTAINMENT}
                  >
                    {BlogCategory.ENTERTAINMENT}
                  </option>
                  <option
                    className="text-capitalize"
                    value={BlogCategory.GENERAL}
                  >
                    {BlogCategory.GENERAL}
                  </option>
                  <option
                    className="text-capitalize"
                    value={BlogCategory.LIFESTYLE}
                  >
                    {BlogCategory.LIFESTYLE}
                  </option>
                  <option
                    className="text-capitalize"
                    value={BlogCategory.SPORTS}
                  >
                    {BlogCategory.SPORTS}
                  </option>
                  <option
                    className="text-capitalize"
                    value={BlogCategory.TECHNOLOGY}
                  >
                    {BlogCategory.TECHNOLOGY}
                  </option>
                </select>
              </div>
            </div>
            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                  <label
                    className="col-form-label col fs-5 px-1 text-center"
                    htmlFor="title"
                  >
                    Content
                    <span className="required text-danger ">*</span>
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <SunEditor
                  setOptions={options}
                  setContents={blog?.content}
                  //getSunEditorInstance={getSunEditorInstance}
                  onChange={setContent}
                  height="60vh"
                />
              </div>
            </div>

            <br />
          </div>
        </div>
      </div>
    </>
  );
};

export default Editor;
