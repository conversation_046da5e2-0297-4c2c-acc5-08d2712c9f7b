import { config } from 'config';
import myImageLoader from 'image/loader';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';
import { userAPI } from '../../../APIs';
import Table from '../../global/table/table';
import { Blog } from 'models';
import { toast } from 'react-toastify';
import moment from 'moment';
import { handlePagination } from 'utils/handlePagination';
import { trimDescription } from 'utils/trim';

interface Props {
  blogData: any;
  setBlogData: Function;
  setSkip: Function;
  skip: number;
}

const BlogList: FC<Props> = ({ blogData, setBlogData, setSkip, skip }) => {
  const router = useRouter();
  const [BlogID, setBlogID] = useState('');

  const onChangeForList = async (skip: number) => {
    try {
      const blogData = await userAPI.getBlogs(skip, config.limit);
      if ('data' in blogData) {
        setBlogData(blogData.data);
      } else {
        toast.error(blogData?.error.message);
      }
    } catch (error) {}
    setSkip(0);
  };

  const deleteBlogFunction = async () => {
    try {
      const res = await userAPI.deleteBlogs(BlogID);
      if ('data' in res) {
        onChangeForList(0);
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
    setModal({
      ...modal,
      delete: false,
    });
  };

  const onClickForDelete = (id: string) => {
    setBlogID(id);
    setModal({ ...modal, delete: true });
  };
  const [modal, setModal] = useState({
    delete: false,
  });

  const columns = [
    {
      label: 'Author',
      path: 'author',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          {data?._embedded!.author[0].name}
        </td>
      ),
    },
    {
      label: 'Title',
      path: 'title',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          {trimDescription(data?.title.rendered, 15)}
        </td>
      ),
    },
    // {
    //   label: 'Category',
    //   path: 'category',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="p-auto m-auto text-center align-middle">
    //       {data?.category}
    //     </td>
    //   ),
    // },
    {
      label: 'Created At',
      path: 'createdAt',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          {moment(data?.date).utc().local().format('lll')}
        </td>
      ),
    },
    {
      label: 'Edit',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          <button
            className="btn btn-outline-info"
            onClick={() => window.open(config.wpBlogDashboard)}
          >
            <span>
              <i className="bi bi-pencil me-2 align-middle"></i>
            </span>
            Edit
          </button>
        </td>
      ),
    },
    {
      label: 'View',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          <Link
            href={{
              pathname: `/blog/View/[id]`,
              query: { id: data?.slug },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Delete',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          <button
            className="btn btn-outline-danger"
            //onClick={() => onClickForDelete(data.id)}
            onClick={() => window.open(config.wpBlogDashboard)}
          >
            <i className="bi bi-pencil me-2 align-middle"></i>
            Delete
          </button>
        </td>
      ),
    },
  ];
  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          {blogData?.length > 0 ? (
            <>
              <Table items={blogData} columns={columns} />
            </>
          ) : (
            'No blog found'
          )}
        </div>
      </div>
      {modal.delete ? (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '30%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this item?</p>
                <br />

                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{
                      border: '1px solid gray',
                      backgroundColor: 'gray',
                      color: 'white',
                      marginRight: '10px',
                    }}
                    onClick={() =>
                      setModal({
                        ...modal,
                        delete: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => deleteBlogFunction()}
                  >
                    <span id="deleteModal"> Delete</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default BlogList;
