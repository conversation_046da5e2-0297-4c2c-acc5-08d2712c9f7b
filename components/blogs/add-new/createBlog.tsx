import React, { useRef, useState } from 'react';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import Editor from '../forms/editor';
import { userAPI } from '@/APIs';
import { BlogCategory } from 'models';
import { toast } from 'react-toastify';
import { useRouter } from 'next/router';

const CreateBlog = () => {
  const router = useRouter();
  const [content, setContent] = useState('');
  const [title, setTitle] = useState('');
  const [category, setCategory] = useState<BlogCategory>(
    BlogCategory.ENTERTAINMENT
  );

  const handleSubmit = async () => {
    try {
      if (title.length === 0) {
        toast.error('Title must not be empty');
      } else if (content.length === 0) {
        toast.error('Content must not be empty');
      } else {
        const res = await userAPI.createBlog({ title, content, category });
        if ('data' in res) {
          toast.success('Blog created successfully');
          router.push('/blog');
        } else {
          toast.error(res?.error.message);
        }
      }
    } catch (error) {}
  };

  return (
    <div className="">
      <div
        className="content-header clearfix mb-3"
        style={{ paddingTop: '10px' }}
      >
        <h3 className="float-start fs-2">
          Create a New Blog
          <span className="fs-5 p-3">
            <Link href="/blog/" className="text-decoration-none">
              <i className="bi bi-arrow-left-circle-fill p-2" />
              <span style={{ fontSize: '14px' }}>Back to Blog list</span>
            </Link>
          </span>
        </h3>
        <div className="float-end">
          <div className="d-flex flex-wrap">
            {' '}
            <button
              type="submit"
              name="save"
              className="btn btn-primary m-1"
              onClick={handleSubmit}
            >
              {/* <i className="bi bi-save" /> */}
              <p className="float-end mx-1 my-0">Save</p>
            </button>
            <button className="btn btn-primary m-1" onClick={handleSubmit}>
              Save & publish
            </button>
          </div>
        </div>
      </div>

      <Editor
        setContent={setContent}
        setTitle={setTitle}
        setCategory={setCategory}
      />

      {/* <button onClick={handleShow}>Show</button>
      <div id="contentDiv"></div> */}
    </div>
  );
};

export default CreateBlog;
