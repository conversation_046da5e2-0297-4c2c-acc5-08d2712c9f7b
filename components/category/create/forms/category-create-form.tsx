import Accordion from '@/components/global/accordion';
import { useAppSelector } from '@/redux-hooks';
import { Field } from 'formik';
import myImageLoader from 'image/loader';
import Image from 'next/image';
import { FC, useState } from 'react';
import { handleMediaUpload } from 'utils/handleMediaUpload';
import CustomInput from './text-input-field';

interface AllCategories {
  name: string;
  id: string;
  slug: string;
}

interface Props {
  categoryList: AllCategories[];
  setFieldValue: Function;
}

const CategoryCreateForm: FC<Props> = ({
  categoryList,
  setFieldValue,
}: Props) => {
  const [file, setFile] = useState<any>();
  const token = useAppSelector((state) => state.persistedReducer.auth.token);

  return (
    <>
      <Accordion id={1} title="Category info" show={true} icon="bi bi-info-lg">
        <CustomInput label="Name" id="name" required={true} />
        <CustomInput label="Description" id="description" />

        <div className="form-group row my-2">
          <div className="col-md-3">
            <div className="label-wrapper row row-cols-auto float-md-end">
              <label className="col-form-label col px-1" htmlFor="parentSlug">
                Parent category
              </label>
            </div>
          </div>
          <div className="col-md-9">
            <div className="input-group ">
              <Field
                name="parentSlug"
                as="select"
                className="ms-2 form-select"
                placeholder="Select parent category"
              >
                <option defaultValue={''}>Choose parent category</option>
                {categoryList.map((category) => (
                  <option key={category.id} value={category.slug}>
                    {category.name}
                  </option>
                ))}
              </Field>
            </div>
          </div>
        </div>

        {/* <CustomInput label="Photo URL" id="photo.url" /> */}
        <div className="form-group row my-4">
          <div className="col-md-3">
            <div className="label-wrapper row row-cols-auto float-md-end">
              <label className="col-form-label col px-1" htmlFor="photo.url">
                Picture
              </label>
            </div>
          </div>
          <div className="col-md-9">
            <input
              id="photo.url"
              name="photo.url"
              type="file"
              className="ms-1"
              onChange={async (event: React.ChangeEvent<HTMLInputElement>) => {
                const fileData = {
                  featureName: 'manufacturer',
                  filenames: [event?.target?.files![0].name],
                };
                const fileInfo = await handleMediaUpload(
                  fileData,
                  event?.target?.files![0],
                  token,
                  true
                );
                console.log(fileInfo);
                setFieldValue('photo.url', fileInfo);
                setFile({
                  src: URL.createObjectURL(event?.target?.files![0]),
                  type: event?.target?.files![0].type,
                });
              }}
            />
            <br />
            {file && (
              <>
                {file.type.includes('image') ? (
                  <Image
                    loader={myImageLoader}
                    className="mt-3"
                    src={file.src}
                    height={200}
                    width={200}
                    alt="animation"
                  />
                ) : (
                  <div className="embed-responsive embed-responsive-16by9">
                    <video
                      width="240"
                      height="200"
                      controls={true}
                      className="embed-responsive-item"
                    >
                      <source src={file.src} type="video/mp4" />
                    </video>
                  </div>
                )}
              </>
            )}
          </div>
          {/* <Field
                    className="border-bottom form-control rounded-0 border-2 border border-0 shadow-none"
                    id="photo.url"
                    name="picture"
                    type="text"
                  /> */}
        </div>
        <CustomInput label="Photo Alt" id="photo.alt" />
      </Accordion>
      <Accordion id={2} title="Display" show={true} icon="bi bi-display">
        <CustomInput label="Published" id="published" type="checkbox" />
        <CustomInput
          label="Show on home page"
          id="showOnHomePage"
          type="checkbox"
        />
        <CustomInput
          label="Include in top menu"
          id="includeInTopMenu"
          type="checkbox"
        />
        <CustomInput
          label="Allow to customers select page size"
          id="allowToSelectPageSize"
          type="checkbox"
        />
        <CustomInput label="Display order" id="displayOrder" type="number" />
      </Accordion>

      <Accordion id={3} title="SEO" show={true} icon="bi bi-search-heart">
        <CustomInput label="Search engine friendly page name" id="meta.SEFN" />
        <CustomInput label="Meta title" id="meta.title" />
        <CustomInput label="Meta keywords" id="meta.keywords" />
        <CustomInput label="Meta description" id="meta.description" />
      </Accordion>
    </>
  );
};

export default CategoryCreateForm;
