import { Form, Formik } from 'formik';
import {
  CreateCategoryRequest,
  NestedCategoryList,
  SubCategoryList,
} from 'models';
import Link from 'next/link';
import { useRouter } from 'next/router';

import { userAPI } from '@/APIs';
import { CategorySchema } from '@/components/category/schema/category.schema';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import CategoryCreateForm from './forms/category-create-form';

interface AllCategories {
  name: string;
  id: string;
  slug: string;
}

const CreateCategoryComponent: React.FC = () => {
  const [categoryList, setCategoryList] = useState<AllCategories[]>();
  const router = useRouter();
  const handleSubmit = async (data: CreateCategoryRequest) => {
    try {
      const response = await userAPI.createCategory(data, router);
      if ('data' in response) {
        router.push('/category');
        toast.success('Create Successful');
      } else {
        toast.error(response?.error.message);
      }
    } catch (error) {}
  };

  async function loadCategories() {
    try {
      const response = await userAPI.getCategoryList();
      if ('data' in response) {
        let allCategoriesList: AllCategories[] = [];
        response?.data?.categories.forEach((data: NestedCategoryList) => {
          allCategoriesList.push({
            id: data.id,
            name: data.name,
            slug: data.slug,
          });
        });
        response?.data.categories.forEach((data: NestedCategoryList) => {
          const newList = createList(data.subCategories!);
          allCategoriesList = allCategoriesList.concat(newList);
        });
        setCategoryList(allCategoriesList);
      } else {
        toast.error(response.error.message);
      }
    } catch (error) {}
  }

  const createList = (subCategory: SubCategoryList[]) => {
    let list: any = [];
    subCategory.forEach((category) => {
      list.push({
        id: category.id,
        name: category.name,
        slug: category.slug,
      });
      if (category.subCategories && category.subCategories.length > 0) {
        const subCategoryList = createList(category.subCategories);
        list.push(...subCategoryList);
      }
    });
    return list;
  };

  useEffect(() => {
    loadCategories();
  }, []);

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <Formik
      initialValues={{
        name: '',
        description: '',
        parentSlug: '',
        photo: {
          url: '',
          alt: '',
        },
        showOnHomePage: false,
        includeInTopMenu: false,
        allowToSelectPageSize: false,
        published: false,
        displayOrder: 0,
        meta: {
          keywords: '',
          description: '',
          title: '',
          SEFN: '',
        },
      }}
      onSubmit={(values, actions) => {
        const metaKeywords = values.meta.keywords.split(' ');
        const structuredValues = {
          ...values,
          meta: { ...values.meta, keywords: metaKeywords },
        };

        handleSubmit(structuredValues);
        actions.setSubmitting(false);
      }}
      validationSchema={CategorySchema}
    >
      {(formikProps) => {
        return (
          <Form onSubmit={formikProps.handleSubmit} onKeyDown={onKeyDown}>
            <div className="content-header clearfix">
              <h1 className="float-start">
                Add a new category
                <span className="fs-5 p-3">
                  <Link href="/category" className="text-decoration-none">
                    <i className="bi bi-arrow-left-circle-fill p-2" />
                    back to category list
                  </Link>
                </span>
              </h1>
              <div className="float-end">
                <button
                  type="submit"
                  name="save"
                  className="btn btn-primary m-1"
                >
                  {/* <i className="bi bi-save" /> */}
                  <p className="float-end mx-1 my-0">Save</p>
                </button>
              </div>
            </div>
            {categoryList ? (
              <CategoryCreateForm
                categoryList={categoryList!}
                setFieldValue={formikProps.setFieldValue}
              />
            ) : (
              ''
            )}
          </Form>
        );
      }}
    </Formik>
  );
};

export default CreateCategoryComponent;
