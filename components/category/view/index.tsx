import { userAPI } from '@/APIs';
import Accordion from '@/components/global/accordion';
import { Category } from 'models';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import InputView from './input-view';
import TextView from './text-view';
import { toast } from 'react-toastify';

const ViewCategory: React.FC = () => {
  const [category, setCategory] = useState<Category>();
  const router = useRouter();
  const id: string = router.query.id as string;

  async function loadCategoryDetails() {
    try {
      const response = await userAPI.getCategory({ categoryId: id! });
      if ('data' in response) setCategory(response?.data);
      else toast.error(response.error.message);
    } catch (error) {}
  }

  useEffect(() => {
    loadCategoryDetails();
  }, [id]);

  return (
    <div className="mt-2 px-5">
      <h2>
        Category details - {category?.name}{' '}
        <span className="fs-5 p-3">
          <Link href={'/category'} className="text-decoration-none">
            <i className="bi bi-arrow-left-circle-fill p-2" />
            Back to category list
          </Link>
        </span>
      </h2>
      <div className="mt-4">
        <Accordion
          title="Category info"
          id={1}
          show={true}
          icon={'bi bi-info-lg'}
        >
          <div className="d-flex flex-column">
            <TextView label="Name" text={category?.name!} />
            {category?.description ? (
              <TextView label="Description" text={category?.description!} />
            ) : (
              ''
            )}
          </div>
        </Accordion>
      </div>
      <div className="mt-4">
        <Accordion
          title="Display info"
          icon="bi bi-display"
          id={2}
          show={false}
        >
          <div className="d-flex flex-column align-items-start">
            <InputView label="Published" checked={category?.published!} />
            <InputView
              label="Show on home page"
              checked={category?.showOnHomePage!}
            />
            <InputView
              label="Allow customers to select page size"
              checked={category?.allowToSelectPageSize!}
            />
            <TextView label="Display order" value={category?.displayOrder!} />
          </div>
        </Accordion>
      </div>
    </div>
  );
};

export default ViewCategory;
