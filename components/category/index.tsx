import { NestedCategoryList, SubCategoryList } from 'models';
import type { NextComponentType } from 'next';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';

import Link from 'next/link';
import CategoryTable from './category-table';
import { toast } from 'react-toastify';

const CategoryComponent: NextComponentType = ({}) => {
  const [categoryList, setCategoryList] = useState<NestedCategoryList[]>();

  async function loadCategories() {
    try {
      const response = await userAPI.getCategoryList();
      let allCategoriesList: NestedCategoryList[] = [];
      if ('data' in response!) {
        response?.data?.categories.forEach((data: NestedCategoryList) => {
          allCategoriesList.push(data);
        });
        response?.data.categories.forEach((data: NestedCategoryList) => {
          const newList = createList(data.subCategories!);
          allCategoriesList = allCategoriesList.concat(newList);
        });
        setCategoryList(allCategoriesList);
      } else {
        toast.error(response?.error.message);
      }
    } catch (error) {}
  }

  const createList = (subCategory: SubCategoryList[]) => {
    let list: NestedCategoryList[] = [];
    subCategory.forEach((category) => {
      list.push(category);
      if (category.subCategories && category.subCategories.length > 0) {
        const subCategoryList = createList(category.subCategories);
        list.push(...subCategoryList);
      }
    });
    return list;
  };

  useEffect(() => {
    loadCategories();
  }, []);

  return (
    <main className="px-5">
      {/* Page heading */}
      <div className="d-flex justify-content-between align-items-center flex-row">
        <div className="fs-2">Categories</div>
        <div className="">
          <Link href="/category/create" passHref legacyBehavior>
            <button className="btn btn-primary">
              {/* <i className="bi bi-plus-square me-1 pl-2" /> */}
              Add new
            </button>
          </Link>
        </div>
      </div>
      <div className="mt-3">
        {/* Category table */}
        {categoryList?.length! > 0 ? (
          <div className="rounded border bg-white p-3">
            <CategoryTable categories={categoryList!} />
          </div>
        ) : (
          <div className="rounded border bg-white p-3">
            There&apos;s no category!
          </div>
        )}
      </div>
    </main>
  );
};

export default CategoryComponent;
