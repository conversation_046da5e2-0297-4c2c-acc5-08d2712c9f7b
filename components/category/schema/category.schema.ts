import * as Yup from 'yup';
import { number, object, string } from 'yup';

export const CategorySchema = object().shape({
  name: string().required('Category name is required'),
  description: string()
    .min(2, 'This field must be at least 2 characters long')
    .max(200, 'This field must be at most 200 characters long'),
  parentSlug: string(),
  photo: object()
    .shape({
      url: string(),
      alt: string(),
    })
    .required('This field must not be empty'),
  showOnHomePage: Yup.boolean(),
  includeInTopMenu: Yup.boolean(),
  allowToSelectPageSize: Yup.boolean(),
  published: Yup.boolean(),
  displayOrder: number().required(
    'This field should not be empty and should give a positive number'
  ),
  meta: object().shape({
    keywords: string().required('This field must not be empty'),
    description: string(),
    title: string().required('This field must not be empty'),
    SEFN: string().required('This field must not be empty'),
  }),
});
