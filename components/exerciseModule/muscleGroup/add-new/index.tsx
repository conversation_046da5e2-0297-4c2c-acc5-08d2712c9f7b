import { userAP<PERSON> } from '@/APIs';
import Animation from '@/components/exerciseModule/muscleGroup/forms/muscleAnimation';
import Info from '@/components/exerciseModule/muscleGroup/forms/muscleInfo';
import { muscleGroupSchema } from '@/components/exerciseModule/muscleGroup/schema';
import { useAppSelector } from '@/redux-hooks';
import { Form, Formik } from 'formik';
import { FileUploadResponse } from 'models';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC } from 'react';
import { toast } from 'react-toastify';
import { handleMediaUpload } from 'utils/handleMediaUpload';

const CreateMuscleGroup: FC = () => {
  const router = useRouter();
  const token = useAppSelector((state) => state.persistedReducer.auth.token);

  const handleSubmit = async (name: string, file: any) => {
    try {
      const fileData = {
        featureName: 'muscle-group',
        filenames: [file.name],
      };
      const fileInfo = (await handleMediaUpload(
        fileData,
        file,
        token,
        false
      )) as FileUploadResponse[];
      const data = {
        name,
        imageURL: fileInfo![0].s3UploadedURLKey,
      };
      const res = await userAPI.createMuscleGroup(data);
      if (!res.data) {
        toast.error("Can't Create Muscle Group");
      } else {
        router.push('/exercise-module/muscle-group');
        toast.success('Muscle Group Created Successfully');
      }
    } catch (error: any) {
      toast.error(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          name: '',
          file: null,
        }}
        onSubmit={(values, actions) => {
          handleSubmit(values.name, values.file);
          actions.setSubmitting(false);
        }}
        validationSchema={muscleGroupSchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix"
                style={{ paddingTop: '10px' }}
              >
                <h3 className="float-start">
                  Create A Muscle Group
                  <span className="fs-5 p-3">
                    <Link
                      href="/exercise-module/muscle-group"
                      className="text-decoration-none"
                    >
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      <span style={{ fontSize: '14px' }}>
                        Back to muscle group list
                      </span>
                    </Link>
                  </span>
                </h3>
                <div className="float-end">
                  <button
                    type="submit"
                    name="save"
                    className="btn btn-primary m-1"
                  >
                    {/* <i className="bi bi-save" /> */}
                    <p className="float-end mx-1 my-0">Save</p>
                  </button>
                </div>
              </div>
              <div className="mt-4">
                <Info />
                <Animation setFieldValue={formikprops.setFieldValue} />
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default CreateMuscleGroup;
