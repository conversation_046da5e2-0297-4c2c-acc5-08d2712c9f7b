import { Form, Formik } from 'formik';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import Animation from '@/components/exerciseModule/muscleGroup/forms/muscleAnimation';
import Info from '@/components/exerciseModule/muscleGroup/forms/muscleInfo';
import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { FileUploadResponse, MuscleGroup } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';
import { handleMediaUpload } from 'utils/handleMediaUpload';
import { enableEdit, showModal } from '@/toolkit/modalSlice';

interface Props {
  muscle: MuscleGroup;
}

const EditMuscleGroup: FC<Props> = ({ muscle }) => {
  const router = useRouter();
  const token = useAppSelector((state) => state.persistedReducer.auth.token);
  const dispatch = useAppDispatch();
  const edit = useAppSelector((state) => state.persistedReducer.modal.edit);
  const [updateData, setUpdateData] = useState<any>();

  const handleSubmit = async (name: string, file: any) => {
    try {
      let attachment;
      if (file.name) {
        const fileData = {
          featureName: 'muscle-group',
          filenames: [file.name],
        };
        const fileInfo = (await handleMediaUpload(
          fileData,
          file,
          token,
          false
        )) as FileUploadResponse[];
        attachment = fileInfo![0].s3UploadedURLKey;
      } else {
        attachment = muscle.imageURL;
      }

      let data;

      if (attachment === muscle.imageURL) {
        data = {
          name,
        };
      } else {
        data = { name, imageURL: attachment };
      }
      const res = await userAPI.updateMuscleGroup(muscle.id!, data);
      if (!res.data) {
        toast.error("Can't Update Muscle Group");
      } else {
        router.push('/exercise-module/muscle-group');
        toast.success('Muscle Group Updated Successfully');
      }
    } catch (error: any) {
      toast.error(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  if (edit === true) {
    handleSubmit(updateData.name, updateData.file);
    dispatch(enableEdit(false));
  }

  return (
    <>
      {muscle ? (
        <Formik
          initialValues={{
            name: muscle.name || '',
            file: muscle.imageURL || null,
          }}
          onSubmit={(values, actions) => {
            dispatch(showModal(true));
            setUpdateData({ name: values.name, file: values.file });
            actions.setSubmitting(false);
          }}
        >
          {(formikprops) => {
            return (
              <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
                <div
                  className="content-header clearfix"
                  style={{ paddingTop: '10px' }}
                >
                  <h3 className="float-start">
                    Edit A Muscle Group
                    <span className="fs-5 p-3">
                      <Link
                        href="/exercise-module/muscle-group"
                        className="text-decoration-none"
                      >
                        <i className="bi bi-arrow-left-circle-fill p-2" />
                        <span style={{ fontSize: '14px' }}>
                          Back to muscle group list
                        </span>
                      </Link>
                    </span>
                  </h3>
                  <div className="float-end">
                    <button
                      type="submit"
                      name="save"
                      className="btn btn-primary m-1"
                    >
                      {/* <i className="bi bi-save" /> */}
                      <p className="float-end mx-1 my-0">Save</p>
                    </button>
                  </div>
                </div>
                <div className="mt-4">
                  <Info edit={true} />
                  <Animation
                    edit={true}
                    setFieldValue={formikprops.setFieldValue}
                    muscleToEdit={muscle}
                  />
                </div>
              </Form>
            );
          }}
        </Formik>
      ) : (
        'Something went wrong!'
      )}
    </>
  );
};

export default EditMuscleGroup;
