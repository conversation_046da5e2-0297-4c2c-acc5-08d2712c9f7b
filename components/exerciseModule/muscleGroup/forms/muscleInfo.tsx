import FieldTemplate from '@/components/common/fieldTemplate';
import { FC, useState } from 'react';

interface Props {
  edit?: true;
}

const Info: FC<Props> = ({ edit }) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };
  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="muscle-info"
        data-hideattribute="ProductPage.HideInfoBlock"
        id="muscle-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#muscleInfoTab"
            aria-expanded="true"
            aria-controls="muscleInfoTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-info-lg col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col px-3 text-start">Muscle Group Info</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="muscleInfoTab">
          <div className="card-body">
            <FieldTemplate
              label="Name"
              isRequired={!edit ? true : false}
              fieldID="name"
              fieldType="text"
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default Info;
