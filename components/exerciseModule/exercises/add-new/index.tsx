import { userAPI } from '@/APIs';
import Animation from '@/components/exerciseModule/exercises/forms/exerciseAnimation';
import ExerciseInfo from '@/components/exerciseModule/exercises/forms/exerciseInfo';
import { useAppSelector } from '@/redux-hooks';
import { Form, Formik } from 'formik';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';
import { toast } from 'react-toastify';
import { handleMediaUpload } from 'utils/handleMediaUpload';
import { createExerciseSchema } from '../schema';

const CreateExercise: FC = () => {
  const router = useRouter();
  const token = useAppSelector((state) => state.persistedReducer.auth.token);

  const [equipments, setEquipments] = useState<string[]>([]);
  const [forceType, setForceTypes] = useState<string[]>([]);

  const handleSubmit = async (data: any) => {
    try {
      const fileData = {
        featureName: 'exercise',
        filenames: [data.file.name],
      };
      const fileInfo = await handleMediaUpload(
        fileData,
        data.file,
        token,
        true
      );
      const exercise = {
        name: data.name || '',
        description: data.description || '',
        mechanics: data.mechanics || '',
        type: data.type || '',
        category: data.categories || [],
        forceType: forceType || [],
        primaryMuscles: data.muscleGroup || [],
        secondaryMuscles: data.secondaryMuscle || [],
        equipments: equipments || [],
        preview: (fileInfo as string) || '',
      };
      const res = await userAPI.createBaseExercise(exercise);
      if (!res.data) {
        toast.error("Can't Create Exercise");
      } else {
        router.push('/exercise-module/exercises');
        toast.success('Exercise Created Successfully');
      }
    } catch (error: any) {
      toast.error(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          name: '',
          description: '',
          mechanics: '',
          // type: '',
          category: [],
          forceType: [],
          primaryMuscles: [],
          secondaryMuscles: [],
          equipments: [],
          preview: '',
        }}
        onSubmit={(values, actions) => {
          console.log(values);
          handleSubmit(values);
          actions.setSubmitting(false);
        }}
        validationSchema={createExerciseSchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix"
                style={{ paddingTop: '10px' }}
              >
                <h3 className="float-start">
                  Create An Exercise
                  <span className="fs-5 p-3">
                    <Link
                      href="/exercise-module/exercises"
                      className="text-decoration-none"
                    >
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      <span style={{ fontSize: '14px' }}>
                        Back to Exercise List
                      </span>
                    </Link>
                  </span>
                </h3>
                <div className="float-end">
                  <button
                    type="submit"
                    name="save"
                    className="btn btn-primary m-1"
                  >
                    {/* <i className="bi bi-save" /> */}
                    <p className="float-end mx-1 my-0">Save</p>
                  </button>
                </div>
              </div>
              <div className="mt-4">
                <ExerciseInfo
                  setEquipments={setEquipments}
                  equipments={equipments}
                  setFieldValue={formikprops.setFieldValue}
                  forceType={forceType}
                  setForceType={setForceTypes}
                />
                <Animation setFieldValue={formikprops.setFieldValue} />
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default CreateExercise;
