import { Form, Formik } from 'formik';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import Animation from '@/components/exerciseModule/exercises/forms/exerciseAnimation';
import ExerciseInfo from '@/components/exerciseModule/exercises/forms/exerciseInfo';
import { createExerciseSchema } from '@/components/exerciseModule/exercises/schema';
import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { ICreateBaseExcercise } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';
import { handleMediaUpload } from 'utils/handleMediaUpload';
import { enableEdit, showModal } from '@/toolkit/modalSlice';

interface Props {
  exerciseToEdit: ICreateBaseExcercise;
}

const EditExercise: FC<Props> = ({ exerciseToEdit }) => {
  const router = useRouter();
  const token = useAppSelector((state) => state.persistedReducer.auth.token);
  const dispatch = useAppDispatch();
  const edit = useAppSelector((state) => state.persistedReducer.modal.edit);
  const [updateData, setUpdateData] = useState<any>();

  const [exercise, setExercise] = useState(exerciseToEdit);
  const [equipments, setEquipments] = useState<string[]>(exercise.equipments!);
  const [forceType, setForceTypes] = useState<string[]>(exercise.forceType!);

  const handleSubmit = async (data: any) => {
    try {
      let attachment;
      if (data.file.name) {
        const fileData = {
          featureName: 'exercise',
          filenames: [data.file.name],
        };
        const fileInfo = await handleMediaUpload(
          fileData,
          data.file,
          token,
          true
        );
        attachment = fileInfo;
      } else {
        attachment = exerciseToEdit.preview;
      }
      let newData;

      if (attachment === exerciseToEdit.preview) {
        newData = {
          ...data,
          category: data.categories,
          primaryMuscles: data.muscleGroup,
          secondaryMuscles: data.secondaryMuscle,
          forceType,
          equipments,
        };
      } else {
        newData = {
          ...data,
          preview: attachment,
          category: data.categories,
          primaryMuscles: data.muscleGroup,
          secondaryMuscles: data.secondaryMuscle,
          forceType,
          equipments,
        };
      }

      const res = await userAPI.updateBaseExercise(exerciseToEdit.id!, newData);

      if (!res.data) {
        toast.error("Can't Update Exercise");
      } else {
        router.push('/exercise-module/exercises');
        toast.success('Exercise Updated Successfully');
      }
    } catch (error: any) {
      toast.error(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  if (edit === true) {
    handleSubmit(updateData!);
    dispatch(enableEdit(false));
  }

  return (
    <>
      {exercise ? (
        <Formik
          initialValues={{
            name: exercise?.name || '',
            description: exercise?.description || '',
            mechanics: exercise?.mechanics || '',
            categories: exercise?.category || [],
            type: exercise?.type || '',
            forceType: exercise?.forceType || [],
            muscleGroup: exercise?.primaryMuscles || [],
            secondaryMuscle: exercise?.secondaryMuscles || [],
            equipments: exercise?.equipments || [],
            file: exercise?.preview || '',
          }}
          onSubmit={(values, actions) => {
            dispatch(showModal(true));
            setUpdateData(values);
            //handleSubmit(values);

            actions.setSubmitting(false);
          }}
          validationSchema={createExerciseSchema}
        >
          {(formikprops) => {
            return (
              <Form
                onSubmit={formikprops.handleSubmit}
                onKeyDown={onKeyDown}
                className="min-vh-100"
              >
                <div className="content-header clearfix pt-4">
                  <h1 className="float-start fs-2">
                    Edit Exercise details
                    <span className="fs-5 p-3">
                      <Link
                        href="/exercise-module/exercises"
                        className="text-decoration-none "
                      >
                        <i className="bi bi-arrow-left-circle-fill p-2" />
                        Back to exercise list
                      </Link>
                    </span>
                  </h1>
                  <div className="float-end">
                    <button
                      type="submit"
                      name="save"
                      className="btn btn-primary m-1"
                    >
                      {/* <i className="bi bi-save" /> */}
                      <p className="float-end mx-1 my-0">Save </p>
                    </button>
                  </div>
                </div>
                <div className="mt-4 pb-5">
                  <ExerciseInfo
                    equipments={equipments}
                    setEquipments={setEquipments}
                    forceType={forceType}
                    setForceType={setForceTypes}
                    setFieldValue={formikprops.setFieldValue}
                    edit={true}
                  />
                  <Animation
                    setFieldValue={formikprops.setFieldValue}
                    exerciseToEdit={exercise}
                  />
                </div>
              </Form>
            );
          }}
        </Formik>
      ) : (
        'Something went wrong!'
      )}
    </>
  );
};

export default EditExercise;
