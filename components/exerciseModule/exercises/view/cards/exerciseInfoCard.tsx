import { FC } from 'react';

import SingleView from '@/components/common/singleView';
import { ICreateBaseExcercise } from 'models';

interface Props {
  exercise: ICreateBaseExcercise;
}

const ExerciseInfoCard: FC<Props> = ({ exercise }) => {
  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="exercise-info"
        id="exercise-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <div className="card-title row align-items-center ps-2 pt-2">
            <i
              className="bi bi-info-lg col-1 align-text-top"
              style={{ fontSize: '25px' }}
            />
            <div className="fs-5 col text-start px-3">Exercise Info</div>
          </div>
        </div>
        <div className="" id="exerciseInfoTab">
          <div className="card-body">
            <SingleView label="Name" value={exercise.name} />
            <SingleView label="Description" value={exercise.description} />
            <SingleView label="Mechanics" value={exercise.mechanics} />
            {/* <SingleView label="Type" value={exercise.type} /> */}
            <SingleView label="Force Type" value={exercise.forceType} />
            <SingleView label="Category" value={exercise.category} />
            <SingleView label="Muscle Group" value={exercise.primaryMuscles} />
            <SingleView
              label="Secondary Muscle Group"
              value={exercise.secondaryMuscles}
            />
            <SingleView label="Equipments" value={exercise.equipments} />
            <SingleView
              label="Attachment"
              attachment={exercise.preview}
              isImage={true}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default ExerciseInfoCard;
