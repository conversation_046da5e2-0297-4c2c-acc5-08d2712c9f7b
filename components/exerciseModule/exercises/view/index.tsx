import ExerciseInfoCard from '@/components/exerciseModule/exercises/view/cards/exerciseInfoCard';
import { ICreateBaseExcercise } from 'models';
import Link from 'next/link';

interface Props {
  exercise: ICreateBaseExcercise;
}

const ViewExercise: React.FC<Props> = ({ exercise }) => {
  return (
    <>
      {exercise ? (
        <div>
          <div className="content-header clearfix mt-3">
            <h1 className="float-start fs-2">
              View exercise details
              <span className="fs-5 p-3">
                <Link
                  href="/exercise-module/exercises"
                  className="text-decoration-none "
                >
                  <i className="bi bi-arrow-left-circle-fill p-2" />
                  Back to exercise list
                </Link>
              </span>
            </h1>
          </div>

          <div className="mt-4">
            <ExerciseInfoCard exercise={exercise} />
          </div>
        </div>
      ) : (
        ''
      )}
    </>
  );
};

export default ViewExercise;
