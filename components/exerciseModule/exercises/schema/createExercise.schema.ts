import { array, mixed, object, string } from 'yup';

export const createExerciseSchema = object().shape({
  name: string()
    .min(2, 'This field must be at least 2 characters long')
    .max(200, 'This field must be at most 200 characters long')
    .required('This field must not be empty'),
  description: string()
    .min(2, 'This field must be at least 2 characters long')
    .max(200, 'This field must be at most 200 characters long')
    .required('This field must not be empty'),
  mechanics: string(),
  categories: array()
    .min(1, 'You must select one')
    .required('This field is required'),
  //type: string().required('This field must not be empty'),
  forceType: array().nullable(),
  muscleGroup: array()
    .min(1, 'You must select one')
    .required('This field is required'),
  secondaryMuscle: array(),
  equipments: array().nullable(),
  file: mixed().required('File is required'),
});
