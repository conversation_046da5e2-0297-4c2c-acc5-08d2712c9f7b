import { userAPI } from '@/APIs';
import CustomSelect from '@/components/common/customSelectInput';
import FieldTemplate from '@/components/common/fieldTemplate';
import { ErrorMessage, Field } from 'formik';
import { MuscleGroup, TrainingCategory } from 'models';
import { FC, useEffect, useState } from 'react';

interface Props {
  setFieldValue: Function;
  setEquipments: Function;
  equipments: string[];
  forceType: string[];
  setForceType: Function;
  edit?: boolean;
}

interface Options {
  label: string;
  value: string;
}

const Info: FC<Props> = ({
  setFieldValue,
  setEquipments,
  equipments,
  forceType,
  setForceType,
  edit,
}) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');
  const [muscleOptions, setMuscleOptions] = useState<Options[]>([]);
  const [categoryOptions, setCategoryOptions] = useState<Options[]>([]);

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  const addEquipment = () => {
    const equipmentsList = (
      document.getElementById('equipments') as HTMLInputElement
    ).value;
    if (equipmentsList.length > 0) {
      setFieldValue!('equipments', '');
      setEquipments([...equipments, equipmentsList]);
    }
  };

  const removeEquipment = (equipment: string) => {
    const equipmentsList = equipments.filter(
      (singleitem) => singleitem != equipment
    );
    setEquipments(equipmentsList);
  };

  const addForceType = () => {
    const forces = (document.getElementById('forceType') as HTMLInputElement)
      .value;
    if (forces.length > 0) {
      setFieldValue('forceType', '');
      if (forces) setForceType([...forceType, forces]);
    }
  };

  const removeForceType = (force: string) => {
    const forces = forceType.filter((singleitem) => singleitem != force);
    setForceType(forces);
  };

  const getMuscleGroupList = async () => {
    try {
      const res = await userAPI.getMuscleGroup();
      const list = res.data as MuscleGroup[];
      const muscleList = list.map((item) => {
        return {
          label: item.name,
          value: item.name,
        };
      });
      setMuscleOptions(muscleList);
    } catch (error) {
      console.log(error);
    }
  };

  const getCategoryList = async () => {
    try {
      const res = await userAPI.getTrainingCategory();
      const list = res.data as TrainingCategory[];
      const categories = list.map((item) => {
        return {
          label: item.name,
          value: item.name,
        };
      });
      setCategoryOptions(categories);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getCategoryList();
    getMuscleGroupList();
  }, []);

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="exercise-info"
        data-hideattribute="ProductPage.HideInfoBlock"
        id="exercise-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#exerciseInfoTab"
            aria-expanded="true"
            aria-controls="exerciseInfoTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-info-lg col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col px-3 text-start">Exercise Info</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="exerciseInfoTab">
          <div className="card-body">
            <FieldTemplate
              label="Name"
              isRequired={!edit ? true : false}
              fieldID="name"
              fieldType="text"
              disabled={edit ? true : false}
            />

            <FieldTemplate
              label="Description/Instruction"
              fieldID="description"
              fieldType="text"
            />

            <FieldTemplate
              label="Mechanics"
              fieldID="mechanics"
              fieldType="text"
            />

            {/* <FieldTemplate
              label="Type"
              isRequired={!edit ? true : false}
              fieldID="type"
              fieldType="text"
            /> */}

            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end me-3">
                  <label
                    className="col-form-label col fs-5 px-1"
                    htmlFor="forceType"
                  >
                    Force Types
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className="input-group mb-2">
                  <Field
                    className="form-control rounded-start border-black"
                    id="forceType"
                    name="forceType"
                  />
                  <span
                    className="btn btn-secondary rounded-end"
                    onClick={addForceType}
                  >
                    +
                  </span>
                </div>
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="forceType" />
                </div>
                <div>
                  <ul>
                    {forceType.map((item) => (
                      <li key={item} className="border btn m-2 px-3 pe-none">
                        {item}
                        <span
                          onClick={() => removeForceType(item)}
                          className="pe-auto ms-4 text-danger"
                        >
                          x
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>

            <FieldTemplate
              label="Category"
              isRequired={!edit ? true : false}
              fieldID="categories"
              fieldType="none"
              fieldClass="custom-select w-100"
              options={categoryOptions}
              component={CustomSelect}
              placeholder="Select Category..."
              ismulti={true}
            />

            <FieldTemplate
              label="Muscle Group"
              isRequired={!edit ? true : false}
              fieldID="muscleGroup"
              fieldType="none"
              fieldClass="custom-select w-100"
              options={muscleOptions}
              component={CustomSelect}
              placeholder="Select Muscle Group..."
              ismulti={true}
            />

            <FieldTemplate
              label="Secondary Muscle Group"
              fieldID="secondaryMuscle"
              fieldType="none"
              fieldClass="custom-select w-100"
              options={muscleOptions}
              component={CustomSelect}
              placeholder="Select Secondary Muscle Group..."
              ismulti={true}
            />

            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end me-3">
                  <label
                    className="col-form-label col fs-5 px-1"
                    htmlFor="equipments"
                  >
                    Equipments
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className="input-group mb-2">
                  <Field
                    className="form-control rounded-start border-black"
                    id="equipments"
                    name="equipments"
                  />
                  <span
                    className="btn btn-secondary rounded-end"
                    onClick={addEquipment}
                  >
                    +
                  </span>
                </div>
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="equipments" />
                </div>
                <div>
                  <ul>
                    {equipments.map((item) => (
                      <li key={item} className="border btn m-2 px-3 pe-none">
                        {item}
                        <span
                          onClick={() => removeEquipment(item)}
                          className="pe-auto ms-4 text-danger"
                        >
                          x
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Info;
