import { userAPI } from '@/APIs';
import Animation from '@/components/exerciseModule/category/forms/categoryAnimation';
import Info from '@/components/exerciseModule/category/forms/categoryInfo';
import { categorySchema } from '@/components/exerciseModule/category/schema';
import { useAppSelector } from '@/redux-hooks';
import { Form, Formik } from 'formik';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC } from 'react';
import { toast } from 'react-toastify';
import { handleMediaUpload } from 'utils/handleMediaUpload';

const CreateExerciseCategory: FC = () => {
  const router = useRouter();
  const token = useAppSelector((state) => state.persistedReducer.auth.token);

  const handleSubmit = async (name: string, file: any) => {
    try {
      const fileData = {
        featureName: 'exercise-category',
        filenames: [file.name],
      };
      const fileInfo = (await handleMediaUpload(
        fileData,
        file,
        token,
        true
      )) as string;
      const exerciseCategoryData = {
        name,
        imageURL: fileInfo,
      };
      const res = await userAPI.createTrainingCategory(exerciseCategoryData);
      if (!res.data) {
        toast.error("Can't Create Training Category");
      } else {
        router.push('/exercise-module/category');
        toast.success('Category Created Successfully');
      }
    } catch (error: any) {
      toast.error(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          name: '',
          file: null,
        }}
        onSubmit={(values, actions) => {
          handleSubmit(values.name, values.file);
          actions.setSubmitting(false);
        }}
        validationSchema={categorySchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix"
                style={{ paddingTop: '10px' }}
              >
                <h3 className="float-start">
                  Create An Exercise Category
                  <span className="fs-5 p-3">
                    <Link
                      href="/exercise-module/category"
                      className="text-decoration-none"
                    >
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      <span style={{ fontSize: '14px' }}>
                        Back to exercise category list
                      </span>
                    </Link>
                  </span>
                </h3>
                <div className="float-end">
                  <button
                    type="submit"
                    name="save"
                    className="btn btn-primary m-1"
                  >
                    {/* <i className="bi bi-save" /> */}
                    <p className="float-end mx-1 my-0">Save</p>
                  </button>
                </div>
              </div>
              <div className="mt-4">
                <Info />
                <Animation setFieldValue={formikprops.setFieldValue} />
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default CreateExerciseCategory;
