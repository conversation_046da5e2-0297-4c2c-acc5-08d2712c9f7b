import { Form, Formik } from 'formik';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { UpdateTrainingCategoryParam } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';
import { handleMediaUpload } from 'utils/handleMediaUpload';
import Animation from '../forms/categoryAnimation';
import Info from '../forms/categoryInfo';
import { enableEdit, showModal } from '@/toolkit/modalSlice';

const EditExerciseCategory: FC<any> = ({ category }) => {
  const router = useRouter();
  const token = useAppSelector((state) => state.persistedReducer.auth.token);
  const dispatch = useAppDispatch();
  const edit = useAppSelector((state) => state.persistedReducer.modal.edit);
  const [updateData, setUpdateData] = useState<any>();

  const handleSubmit = async (
    id: UpdateTrainingCategoryParam,
    name: string,
    file: any
  ) => {
    try {
      let attachment;
      if (file.name) {
        const fileData = {
          featureName: 'exercise-category',
          filenames: [file.name],
        };
        const fileInfo = (await handleMediaUpload(
          fileData,
          file,
          token,
          true
        )) as string;
        attachment = fileInfo;
      } else {
        attachment = category.image;
      }
      const exerciseCategoryData = {
        name,
        imageURL: attachment,
      };
      const res = await userAPI.updateTrainingCategory(
        category.id,
        exerciseCategoryData
      );
      if (!res.data) {
        toast.error("Can't Update Training Category");
      } else {
        router.push('/exercise-module/category');
        toast.success('Category Updated Successfully');
      }
    } catch (error: any) {
      toast.error(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  if (edit === true) {
    handleSubmit(updateData.id, updateData.name, updateData.file);
    dispatch(enableEdit(false));
  }

  return (
    <>
      {category ? (
        <Formik
          initialValues={{
            name: category.name || '',
            file: category.image || '',
          }}
          onSubmit={(values, actions) => {
            dispatch(showModal(true));
            setUpdateData({
              id: category.id,
              name: values.name,
              file: values.file,
            });
            actions.setSubmitting(false);
          }}
          // validationSchema={CategorySchema}
        >
          {(formikprops) => {
            return (
              <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
                <div
                  className="content-header clearfix"
                  style={{ paddingTop: '10px' }}
                >
                  <h3 className="float-start">
                    Edit An Exercise Category
                    <span className="fs-5 p-3">
                      <Link
                        href="/exercise-module/category"
                        className="text-decoration-none"
                      >
                        <i className="bi bi-arrow-left-circle-fill p-2" />
                        <span style={{ fontSize: '14px' }}>
                          Back to exercise category list
                        </span>
                      </Link>
                    </span>
                  </h3>
                  <div className="float-end">
                    <button
                      type="submit"
                      name="save"
                      className="btn btn-primary m-1"
                    >
                      {/* <i className="bi bi-save" /> */}
                      <p className="float-end mx-1 my-0">Save</p>
                    </button>
                  </div>
                </div>
                <div className="mt-4">
                  <Info edit={true} />
                  <Animation
                    edit={true}
                    category={category}
                    setFieldValue={formikprops.setFieldValue}
                  />
                </div>
              </Form>
            );
          }}
        </Formik>
      ) : (
        'Something went wrong!'
      )}
    </>
  );
};

export default EditExerciseCategory;
