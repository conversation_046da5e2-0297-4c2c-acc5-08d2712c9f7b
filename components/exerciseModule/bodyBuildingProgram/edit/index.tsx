import { Form, Formik } from 'formik';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { BodyBuildingProgram, FileUploadResponse } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';
import { handleMediaUpload } from 'utils/handleMediaUpload';
import Animation from '../forms/bbProgramAnimation';
import Info from '../forms/bbProgramInfo';
import { enableEdit, showModal } from '@/toolkit/modalSlice';

interface Props {
  bbProgram: BodyBuildingProgram;
}

const EditBodyBuildingProgram: FC<Props> = ({ bbProgram }) => {
  const router = useRouter();
  const token = useAppSelector((state) => state.persistedReducer.auth.token);
  const dispatch = useAppDispatch();
  const edit = useAppSelector((state) => state.persistedReducer.modal.edit);
  const [updateData, setUpdateData] = useState<any>();

  const handleSubmit = async (
    id: string,
    name: string,
    file: any,
    programDuration: number
  ) => {
    try {
      let attachment;
      if (file.name) {
        const fileData = {
          featureName: 'body-building-program',
          filenames: [file.name],
        };
        const fileInfo = (await handleMediaUpload(
          fileData,
          file,
          token,
          false
        )) as FileUploadResponse[];
        attachment = fileInfo![0].s3UploadedURLKey;
      } else {
        attachment = bbProgram.imageURL;
      }

      let bbProgramData;
      if (attachment === bbProgram.imageURL) {
        bbProgramData = {
          name,
          programDuration,
        };
      } else {
        bbProgramData = {
          name,
          programDuration,
          imageURL: attachment,
        };
      }
      const res = await userAPI.updateBBProgram(bbProgram.id!, bbProgramData);
      if (!res.data) {
        toast.error("Can't Update Body Building Program");
      } else {
        router.push('/exercise-module/body-building-program');
        toast.success('Body Building Program Updated Successfully');
      }
    } catch (error: any) {
      toast.error(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  if (edit === true) {
    handleSubmit(
      updateData.id,
      updateData.name,
      updateData.file,
      updateData.programDuration!
    );
    dispatch(enableEdit(false));
  }

  return (
    <>
      {bbProgram ? (
        <Formik
          initialValues={{
            name: bbProgram.name || '',
            programDuration: bbProgram.programDuration || 0,
            file: bbProgram.imageURL || '',
          }}
          onSubmit={(values, actions) => {
            dispatch(showModal(true));
            setUpdateData({
              id: bbProgram.id,
              name: values.name,
              file: values.file,
              duration: values.programDuration,
            });

            actions.setSubmitting(false);
          }}
        >
          {(formikprops) => {
            return (
              <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
                <div
                  className="content-header clearfix"
                  style={{ paddingTop: '10px' }}
                >
                  <h3 className="float-start">
                    Edit A Body Building Program
                    <span className="fs-5 p-3">
                      <Link
                        href="/exercise-module/category"
                        className="text-decoration-none"
                      >
                        <i className="bi bi-arrow-left-circle-fill p-2" />
                        <span style={{ fontSize: '14px' }}>
                          Back to body building program list
                        </span>
                      </Link>
                    </span>
                  </h3>
                  <div className="float-end">
                    <button
                      type="submit"
                      name="save"
                      className="btn btn-primary m-1"
                    >
                      {/* <i className="bi bi-save" /> */}
                      <p className="float-end mx-1 my-0">Save</p>
                    </button>
                  </div>
                </div>
                <div className="mt-4">
                  <Info edit={true} />
                  <Animation
                    edit={true}
                    bbProgram={bbProgram}
                    setFieldValue={formikprops.setFieldValue}
                  />
                </div>
              </Form>
            );
          }}
        </Formik>
      ) : (
        'Something went wrong!'
      )}
    </>
  );
};

export default EditBodyBuildingProgram;
