import { mixed, number, object, string } from 'yup';

export const bbProgramSchema = object().shape({
  name: string()
    .min(2, 'This field must be at least 2 characters long')
    .max(200, 'This field must be at most 200 characters long')
    .required('This field must not be empty'),
  programDuration: number()
    .min(1)
    .integer()
    .required('ProgramDuration must be greater than or equal to 1'),
  file: mixed().required('File is required'),
});
