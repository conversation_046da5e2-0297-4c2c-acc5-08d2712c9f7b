import FieldTemplate from '@/components/common/fieldTemplate';
import { FC, useState } from 'react';

interface Props {
  edit?: boolean;
}

const Info: FC<Props> = ({ edit }) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="bb-program-info"
        data-hideattribute="ProductPage.HideInfoBlock"
        id="bb-program-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#bbProgramInfoTab"
            aria-expanded="true"
            aria-controls="bbProgramInfoTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-info-lg col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col px-3 text-start">
                Body Building Program Info
              </div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="bbProgramInfoTab">
          <div className="card-body">
            <FieldTemplate
              label="Name"
              isRequired={!edit ? true : false}
              fieldID="name"
              fieldType="text"
            />
            <FieldTemplate
              label="Program Duration"
              isRequired={!edit ? true : false}
              fieldID="programDuration"
              fieldType="number"
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default Info;
