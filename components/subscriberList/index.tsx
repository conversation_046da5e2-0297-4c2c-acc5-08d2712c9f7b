import moment from 'moment';
import { FC } from 'react';
import Table from '../global/table/table';
import { ISubscriber, ISubscriptionList } from 'models';
import { config } from 'config';
import { handlePagination } from 'utils/handlePagination';

interface Props {
  subscriber: ISubscriptionList[];
  setSubscriber: Function;
  setSkip: Function;
  skip: number;
  // showSeeMore: boolean;
  // setLoadData: Function;
  // loadData: boolean;
}
const List: FC<Props> = ({
  subscriber,
  setSubscriber,
  setSkip,
  skip,
  // showSeeMore,
  // setLoadData,
  // loadData,
}) => {
  const columns = [
    {
      label: 'User',
      path: 'userInfo',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          {data?.userInfo?.name!}
        </td>
      ),
    },
    {
      label: 'Emails',
      path: 'userInfo',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          {data?.userInfo?.email!}
        </td>
      ),
    },
    {
      label: 'Package Name',
      path: 'packageInfo',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          {data?.packageInfo?.name!}
        </td>
      ),
    },
    {
      label: 'Title',
      path: 'packageInfo',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          {data?.packageInfo?.title!}
        </td>
      ),
    },
    {
      label: 'Type',
      path: 'packageInfo',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          {data?.packageInfo?.type!}
        </td>
      ),
    },
    {
      label: 'Subscription Date',
      path: 'createdAt',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          {moment(data?.createdAt!).utc().local().format('lll')}
        </td>
      ),
    },
    {
      label: 'Expiration Date',
      path: 'expireAt',
      content: (data: any, key: any, index: any) => (
        <td className="p-auto m-auto text-center align-middle">
          {moment(data?.expireAt!).utc().local().format('lll')}
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          {subscriber.length > 0 && (
            <>
              <Table items={subscriber} columns={columns} />
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default List;
