import { userAP<PERSON> } from '@/APIs';
import { Form, Formik, Field, ErrorMessage } from 'formik';
import { IUpdateRefundReq } from 'models';
import { useRouter } from 'next/router';
import { FC } from 'react';
import { toast } from 'react-toastify';
import { updateRefundSchema } from '../schema';

interface Props {
  refundId: string;
  initialValues?: {
    reviewComment: string;
  };
  onSuccess?: () => void;
}

const RefundForm: FC<Props> = ({ 
  refundId, 
  initialValues = { reviewComment: '' },
  onSuccess 
}) => {
  const router = useRouter();

  const handleSubmit = async (data: IUpdateRefundReq) => {
    try {
      // Note: This form is for display purposes only as there's no general update endpoint
      // Only approve/reject actions are available through the list component
      toast.info('Please use the approve/reject buttons in the refund list to update refund status');
      if (onSuccess) {
        onSuccess();
      } else {
        router.push('/accounting/refunds');
      }
    } catch (error) {
      toast.error('An error occurred');
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={(values, actions) => {
        handleSubmit(values);
        actions.setSubmitting(false);
      }}
      validationSchema={updateRefundSchema}
    >
      {(formikProps) => (
        <Form onSubmit={formikProps.handleSubmit} onKeyDown={onKeyDown}>
          <div className="row">
            <div className="col-12">
              <div className="mb-3">
                <label htmlFor="reviewComment" className="form-label">
                  Review Comment <span className="text-danger">*</span>
                </label>
                <Field
                  as="textarea"
                  className="form-control"
                  id="reviewComment"
                  name="reviewComment"
                  rows={4}
                  placeholder="Enter your review comment..."
                />
                <div className="text-danger">
                  <ErrorMessage name="reviewComment" />
                </div>
              </div>
            </div>
          </div>

          <div className="d-flex justify-content-end gap-2">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={() => router.back()}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={formikProps.isSubmitting || !formikProps.isValid}
            >
              {formikProps.isSubmitting ? 'Updating...' : 'Update Refund'}
            </button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default RefundForm;
