import { IRefundEntry } from 'models';
import moment from 'moment';
import { FC } from 'react';

interface Props {
  refund: IRefundEntry;
}

const RefundDetailsCard: FC<Props> = ({ refund }) => {
  const getStatusBadge = (status: string) => {
    const statusColors: { [key: string]: string } = {
      pending: 'bg-warning text-dark',
      approved: 'bg-success',
      rejected: 'bg-danger',
      success: 'bg-primary',
      failed: 'bg-dark',
    };
    return statusColors[status] || 'bg-secondary';
  };

  return (
    <div className="card">
      <div className="card-header">
        <h5 className="card-title mb-0">Refund Details</h5>
      </div>
      <div className="card-body">
        <div className="row">
          <div className="col-md-6">
            <div className="mb-3">
              <label className="form-label fw-bold">Refund ID:</label>
              <p className="mb-0">{refund.id}</p>
            </div>
            <div className="mb-3">
              <label className="form-label fw-bold">User ID:</label>
              <p className="mb-0">{refund.userId}</p>
            </div>
            <div className="mb-3">
              <label className="form-label fw-bold">Coach ID:</label>
              <p className="mb-0">{refund.coachId}</p>
            </div>
            <div className="mb-3">
              <label className="form-label fw-bold">Program ID:</label>
              <p className="mb-0">{refund.programId}</p>
            </div>
            <div className="mb-3">
              <label className="form-label fw-bold">Subscription ID:</label>
              <p className="mb-0">{refund.subscriptionId}</p>
            </div>
          </div>
          <div className="col-md-6">
            <div className="mb-3">
              <label className="form-label fw-bold">Status:</label>
              <p className="mb-0">
                <span className={`badge ${getStatusBadge(refund.reviewStatus)}`}>
                  {refund.reviewStatus.toUpperCase()}
                </span>
              </p>
            </div>
            <div className="mb-3">
              <label className="form-label fw-bold">Contact Email:</label>
              <p className="mb-0">{refund.contactEmail}</p>
            </div>
            <div className="mb-3">
              <label className="form-label fw-bold">Contact Phone:</label>
              <p className="mb-0">{refund.contactMobileNumber}</p>
            </div>
            <div className="mb-3">
              <label className="form-label fw-bold">Created At:</label>
              <p className="mb-0">
                {moment(refund.createdAt).format('MMMM DD, YYYY HH:mm')}
              </p>
            </div>
            {refund.updatedAt && (
              <div className="mb-3">
                <label className="form-label fw-bold">Updated At:</label>
                <p className="mb-0">
                  {moment(refund.updatedAt).format('MMMM DD, YYYY HH:mm')}
                </p>
              </div>
            )}
          </div>
        </div>
        
        {refund.requestReason && (
          <div className="mb-3">
            <label className="form-label fw-bold">Request Reason:</label>
            <div className="border rounded p-3 bg-light">
              <p className="mb-0">{refund.requestReason}</p>
            </div>
          </div>
        )}
        
        {refund.reviewComment && (
          <div className="mb-3">
            <label className="form-label fw-bold">Review Comment:</label>
            <div className="border rounded p-3 bg-light">
              <p className="mb-0">{refund.reviewComment}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RefundDetailsCard;
