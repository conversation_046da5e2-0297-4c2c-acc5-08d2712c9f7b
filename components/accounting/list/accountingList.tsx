import Link from 'next/link';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import Table from '@/components/global/table/table';
import { config } from 'config';
import { IAccountingEntry } from 'models';
import moment from 'moment';
import { toast } from 'react-toastify';

interface Props {
  accountingList: IAccountingEntry[];
  setAccountingEntries: Function;
  setSkip: Function;
  skip: number;
}

const AccountingList: FC<Props> = ({ 
  accountingList, 
  setAccountingEntries, 
  setSkip, 
  skip 
}) => {
  const [modal, setModal] = useState({
    delete: false,
  });

  const onChangeForList = async (skip: number) => {
    try {
      const accountingEntries = await userAPI.getAccountingEntries(skip, config.limit);
      if ('data' in accountingEntries) {
        setAccountingEntries(accountingEntries.data);
      } else {
        toast.error(accountingEntries.error.message);
      }
    } catch (error) {}
    setSkip(0);
  };

  const onClickForSort = (sortBy: string) => {
    // Implement sorting logic if needed
    console.log('Sort by:', sortBy);
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      render: (text: string) => (
        <span className="text-truncate" style={{ maxWidth: '100px' }}>
          {text}
        </span>
      ),
    },
    {
      title: 'Transaction Type',
      dataIndex: 'transactionType',
      key: 'transactionType',
      render: (type: string) => (
        <span className={`badge ${type === 'credit' ? 'bg-success' : 'bg-danger'}`}>
          {type.toUpperCase()}
        </span>
      ),
    },
    {
      title: 'Reference',
      dataIndex: 'reference',
      key: 'reference',
      render: (reference: string) => (
        <span className="badge bg-info">
          {reference.toUpperCase()}
        </span>
      ),
    },
    {
      title: 'Reference Type',
      dataIndex: 'referenceType',
      key: 'referenceType',
      render: (type: string) => (
        <span className="badge bg-secondary">
          {type.toUpperCase()}
        </span>
      ),
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span className="fw-bold">
          ${amount.toFixed(2)}
        </span>
      ),
    },
    {
      title: 'Reference ID',
      dataIndex: 'referenceId',
      key: 'referenceId',
      render: (text: string) => (
        <span className="text-truncate" style={{ maxWidth: '120px' }}>
          {text}
        </span>
      ),
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => (
        <span>{moment(date).format('MMM DD, YYYY HH:mm')}</span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: IAccountingEntry) => (
        <div className="d-flex gap-2">
          <Link
            href={`/accounting/view/${record.id}`}
            className="btn btn-sm btn-outline-primary"
          >
            <i className="bi bi-eye"></i>
          </Link>
        </div>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={accountingList}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>
    </>
  );
};

export default AccountingList;
