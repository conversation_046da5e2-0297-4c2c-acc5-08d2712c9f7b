import Link from 'next/link';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import Table from '@/components/global/table/table';
import { config } from 'config';
import { IRefundEntry, ReviewStatus } from 'models';
import moment from 'moment';
import { toast } from 'react-toastify';

interface Props {
  refundList: IRefundEntry[];
  setRefundEntries: Function;
  setSkip: Function;
  skip: number;
}

const RefundList: FC<Props> = ({ 
  refundList, 
  setRefundEntries, 
  setSkip, 
  skip 
}) => {
  const [refundId, setRefundId] = useState('');
  const [modal, setModal] = useState({
    approve: false,
    reject: false,
  });

  const onChangeForList = async (skip: number) => {
    try {
      const refundEntries = await userAPI.getRefundEntries(skip, config.limit);
      if ('data' in refundEntries) {
        setRefundEntries(refundEntries.data);
      } else {
        toast.error(refundEntries.error.message);
      }
    } catch (error) {}
    setSkip(0);
  };

  const approveRefund = async (reviewComment: string) => {
    try {
      const res = await userAPI.approveRefundEntry(refundId, { reviewComment });
      if ('data' in res) {
        toast.success('Refund approved successfully');
        onChangeForList(0);
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
    setModal({
      ...modal,
      approve: false,
    });
  };

  const rejectRefund = async (reviewComment: string) => {
    try {
      const res = await userAPI.rejectRefundEntry(refundId, { reviewComment });
      if ('data' in res) {
        toast.success('Refund rejected successfully');
        onChangeForList(0);
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
    setModal({
      ...modal,
      reject: false,
    });
  };

  const onClickForSort = (sortBy: string) => {
    // Implement sorting logic if needed
    console.log('Sort by:', sortBy);
  };

  const getStatusBadge = (status: string) => {
    const statusColors: { [key: string]: string } = {
      pending: 'bg-warning',
      approved: 'bg-success',
      rejected: 'bg-danger',
      success: 'bg-primary',
      failed: 'bg-dark',
    };
    return statusColors[status] || 'bg-secondary';
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      render: (text: string) => (
        <span className="text-truncate" style={{ maxWidth: '100px' }}>
          {text}
        </span>
      ),
    },
    {
      title: 'User ID',
      dataIndex: 'userId',
      key: 'userId',
      render: (text: string) => (
        <span className="text-truncate" style={{ maxWidth: '100px' }}>
          {text}
        </span>
      ),
    },
    {
      title: 'Coach ID',
      dataIndex: 'coachId',
      key: 'coachId',
      render: (text: string) => (
        <span className="text-truncate" style={{ maxWidth: '100px' }}>
          {text}
        </span>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'reviewStatus',
      key: 'reviewStatus',
      render: (status: string) => (
        <span className={`badge ${getStatusBadge(status)}`}>
          {status.toUpperCase()}
        </span>
      ),
    },
    {
      title: 'Contact Email',
      dataIndex: 'contactEmail',
      key: 'contactEmail',
      render: (email: string) => (
        <span className="text-truncate" style={{ maxWidth: '150px' }}>
          {email}
        </span>
      ),
    },
    {
      title: 'Contact Phone',
      dataIndex: 'contactMobileNumber',
      key: 'contactMobileNumber',
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => (
        <span>{moment(date).format('MMM DD, YYYY HH:mm')}</span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: IRefundEntry) => (
        <div className="d-flex gap-2">
          <Link
            href={`/accounting/refunds/view/${record.id}`}
            className="btn btn-sm btn-outline-primary"
          >
            <i className="bi bi-eye"></i>
          </Link>
          {record.reviewStatus === ReviewStatus.PENDING && (
            <>
              <button
                className="btn btn-sm btn-outline-success"
                onClick={() => {
                  setRefundId(record.id);
                  setModal({ ...modal, approve: true });
                }}
              >
                <i className="bi bi-check-circle"></i>
              </button>
              <button
                className="btn btn-sm btn-outline-danger"
                onClick={() => {
                  setRefundId(record.id);
                  setModal({ ...modal, reject: true });
                }}
              >
                <i className="bi bi-x-circle"></i>
              </button>
            </>
          )}
        </div>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={refundList}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>

      {/* Approve Modal */}
      {modal.approve && (
        <div
          className="modal"
          style={{ display: modal.approve ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              setModal({ ...modal, approve: false });
            }}
          ></div>
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Approve Refund</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setModal({ ...modal, approve: false });
                  }}
                ></button>
              </div>
              <div className="modal-body">
                <div className="mb-3">
                  <label className="form-label">Review Comment</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Enter your review comment..."
                    id="approveComment"
                  ></textarea>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setModal({ ...modal, approve: false });
                  }}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-success"
                  onClick={() => {
                    const comment = (document.getElementById('approveComment') as HTMLTextAreaElement)?.value;
                    if (comment) {
                      approveRefund(comment);
                    } else {
                      toast.error('Please enter a review comment');
                    }
                  }}
                >
                  Approve
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reject Modal */}
      {modal.reject && (
        <div
          className="modal"
          style={{ display: modal.reject ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              setModal({ ...modal, reject: false });
            }}
          ></div>
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Reject Refund</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setModal({ ...modal, reject: false });
                  }}
                ></button>
              </div>
              <div className="modal-body">
                <div className="mb-3">
                  <label className="form-label">Review Comment</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Enter your review comment..."
                    id="rejectComment"
                  ></textarea>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setModal({ ...modal, reject: false });
                  }}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-danger"
                  onClick={() => {
                    const comment = (document.getElementById('rejectComment') as HTMLTextAreaElement)?.value;
                    if (comment) {
                      rejectRefund(comment);
                    } else {
                      toast.error('Please enter a review comment');
                    }
                  }}
                >
                  Reject
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default RefundList;
