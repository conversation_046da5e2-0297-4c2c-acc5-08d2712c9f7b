import { object, string } from 'yup';

export const updateRefundSchema = object().shape({
  reviewComment: string()
    .min(10, 'Review comment must be at least 10 characters long')
    .max(500, 'Review comment must be at most 500 characters long')
    .required('Review comment is required'),
});

export const createRefundSchema = object().shape({
  requestReason: string()
    .min(10, 'Request reason must be at least 10 characters long')
    .max(500, 'Request reason must be at most 500 characters long')
    .required('Request reason is required'),
  contactMobileNumber: string()
    .matches(/^[0-9+\-\s()]+$/, 'Please enter a valid phone number')
    .min(10, 'Phone number must be at least 10 digits')
    .max(20, 'Phone number must be at most 20 characters')
    .required('Contact mobile number is required'),
  contactEmail: string()
    .email('Please enter a valid email address')
    .required('Contact email is required'),
});
