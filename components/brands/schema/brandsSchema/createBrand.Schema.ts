import * as Yup from 'yup';
import { number, object, string } from 'yup';

export const brandSchema = object().shape({
  name: string().required('This field must not be empty'),
  description: string()
    .min(2, 'This field must be at least 2 characters long')
    .max(200, 'This field must be at most 200 characters long'),
  allowToSelectPageSize: Yup.boolean(),
  published: Yup.boolean(),
  displayOrder: number().required(
    'This field should not be empty and should give a positive number'
  ),
  pageSizeOptions: Yup.array(),
  keywords: string().required('This field must not be empty'),
  metaTitle: string().required('This field must not be empty'),
  metaDescription: string()
    .min(2, 'This field must be at least 2 characters long')
    .max(200, 'This field must be at most 200 characters long'),
  SEFN: string().required('This field must not be empty'),
});
