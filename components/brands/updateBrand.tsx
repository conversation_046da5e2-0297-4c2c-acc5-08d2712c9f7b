import { Form, Formik } from 'formik';
import { useRouter } from 'next/router';

import { Brand, UpdateBrandRequest } from 'models';

import { userAPI } from '@/APIs';
import BrandInfoForm from '@/components/brands/forms/brandInfoForm';
import BrandMetaForm from '@/components/brands/forms/brandMetaForm';
import Link from 'next/link';
import { FC, useState } from 'react';
import { toast } from 'react-toastify';
import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { enableEdit, showModal } from '@/toolkit/modalSlice';

const UpdateBrand: FC<{ brand: Brand }> = ({ brand }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const edit = useAppSelector((state) => state.persistedReducer.modal.edit);
  const [updateData, setUpdateData] = useState<UpdateBrandRequest>();

  const handleSubmit = async (data: UpdateBrandRequest) => {
    try {
      const res = await userAPI.updateBrand(brand.id, data, router);
      if ('data' in res) {
        router.push('/Brands');
        toast.success('Successfully updated');
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  if (edit === true) {
    handleSubmit(updateData!);
    dispatch(enableEdit(false));
  }

  return (
    <>
      {brand ? (
        <Formik
          initialValues={{
            name: brand?.info?.name,
            description: brand?.info?.description,
            allowToSelectPageSize: brand?.info?.allowToSelectPageSize,
            published: brand?.info?.published,
            displayOrder: brand?.info?.displayOrder,
            pageSizeOptions: brand?.info?.pageSizeOptions,
            keywords: brand?.meta?.keywords,
            metaTitle: brand?.meta?.title,
            metaDescription: brand?.meta?.description,
            SEFN: brand?.meta?.SEFN,
          }}
          onSubmit={(values, actions) => {
            const info = {
              // name: values?.name,
              description: values?.description,
              allowToSelectPageSize: values?.allowToSelectPageSize,
              published: values?.published,
              displayOrder: values?.displayOrder,
              pageSizeOptions: values?.pageSizeOptions,
            };
            const meta = {
              keywords: values?.keywords,
              title: values?.metaTitle,
              description: values?.metaDescription,
              SEFN: values?.SEFN,
            };
            const newData = {
              info: info,
              meta: meta,
            };
            dispatch(showModal(true));
            setUpdateData(newData);
            actions.setSubmitting(false);
          }}
          // validationSchema={brandSchema}
        >
          {(formikprops) => {
            return (
              <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
                <div className="content-header clearfix pt-4">
                  <h1 className="float-start fs-2">
                    Edit brand
                    <span className="fs-5 p-3">
                      <Link href="/Brands" className="text-decoration-none">
                        <i className="bi bi-arrow-left-circle-fill p-2" />
                        back to brands list
                      </Link>
                    </span>
                  </h1>
                  <div className="float-end">
                    <button
                      type="submit"
                      name="save"
                      className="btn btn-primary m-1"
                    >
                      {/* <i className="bi bi-save" /> */}
                      <p className="float-end mx-1 my-0">Save</p>
                    </button>
                  </div>
                </div>
                <div className="mt-4">
                  <BrandInfoForm editMode={brand ? true : false} />
                  <BrandMetaForm />
                </div>
              </Form>
            );
          }}
        </Formik>
      ) : (
        'Error getting brnad'
      )}
    </>
  );
};

export default UpdateBrand;
