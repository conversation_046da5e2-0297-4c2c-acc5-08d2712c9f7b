import { NextComponentType } from 'next';
import { useState } from 'react';

import FieldTemplate from '@/components/common/fieldTemplate';

const BrandMetaForm: NextComponentType = () => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };
  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="brand-meta"
        id="brand-meta"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#brandMetaTab"
            aria-expanded="true"
            aria-controls="brandMetaTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <div className="fs-5 col px-3 text-start">
                <i
                  className="bi bi-meta col-1 px-1"
                  style={{ fontSize: '25px' }}
                />
                Brand Meta
              </div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="brandMetaTab">
          <div className="card-body">
            <FieldTemplate
              label="Keywords"
              isRequired={false}
              fieldID="keywords"
              fieldType="text"
            />
            <FieldTemplate
              label="Title"
              isRequired={false}
              fieldID="metaTitle"
              fieldType="text"
            />
            <FieldTemplate
              label="Description"
              isRequired={false}
              fieldID="metaDescription"
              fieldAs="textarea"
            />
            <FieldTemplate
              label="SEFN"
              isRequired={false}
              fieldID="SEFN"
              fieldType="text"
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default BrandMetaForm;
