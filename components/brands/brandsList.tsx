import Link from 'next/link';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import Table from '@/components/global/table/table';
import { config } from 'config';
import { Brand } from 'models';
import { handlePagination } from 'utils/handlePagination';
import { toast } from 'react-toastify';

interface Props {
  brandsList: Brand[];
  setBrands: Function;
  setSkip: Function;
  skip: number;
  // showSeeMore: boolean;
  // setLoadData: Function;
  // loadData: boolean;
  // setShowSeeMore: Function;
}

const BrandsList: FC<Props> = ({
  brandsList,
  setBrands,
  setSkip,
  skip,
  // showSeeMore,
  // setLoadData,
  // loadData,
  // setShowSeeMore,
}) => {
  const [BrandID, setBrandID] = useState('');

  const onChangeForList = async (skip: number) => {
    try {
      const brandsList = await userAPI.getBrands(skip, config.limit);
      if ('data' in brandsList!) setBrands(brandsList?.data?.brands);
      else toast.error(brandsList.error.message);
      setSkip(0);
    } catch (error) {}
  };

  const deleteProductFunction = async () => {
    const res = await userAPI.deleteBrand(BrandID);
    if (res) {
      onChangeForList(0);
    } else toast.error(res.error.message);
    setModal({
      ...modal,
      delete: false,
    });
  };

  const onClickForDelete = (id: string) => {
    setBrandID(id);
    setModal({ ...modal, delete: true });
  };

  const [modal, setModal] = useState({
    delete: false,
  });

  const columns = [
    {
      label: 'Brand Name',
      path: 'name',
      content: (data: Brand, key: keyof typeof data.info) => (
        <td className="text-center align-middle">{data?.info[key]}</td>
      ),
    },
    {
      label: 'Display Order',
      path: 'displayOrder',
      content: (data: Brand, key: keyof typeof data.info) => (
        <td className="text-center align-middle">{data?.info[key]}</td>
      ),
    },
    {
      label: 'Published',
      path: 'published',
      content: (data: Brand, key: keyof typeof data.info) => (
        <td className="p-auto m-auto text-center align-middle">
          {data?.info[key] ? <i className="bi bi-check-lg"></i> : 'X'}
        </td>
      ),
    },
    {
      label: 'Allow To Select Page Size',
      path: 'allowToSelectPageSize',
      content: (data: Brand, key: keyof typeof data.info) => (
        <td className="p-auto m-auto text-center align-middle">
          {data?.info[key] ? <i className="bi bi-check-lg"></i> : 'X'}
        </td>
      ),
    },
    {
      label: 'Edit',
      path: 'id',
      content: (data: Brand) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/Brands/Edit/[id]`,
              query: { id: data.id },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-info">
              <span>
                <i className="bi bi-pencil me-2 align-middle"></i>
              </span>
              Edit
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'View',
      path: 'id',
      content: (data: Brand) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/Brands/View/[id]`,
              query: { id: data.id },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Delete',
      path: 'id',
      content: (data: Brand) => (
        <td className="text-center align-middle">
          <button
            className="btn btn-default btn-outline-danger"
            onClick={() => onClickForDelete(data.id)}
          >
            <i className="bi bi-trash3-fill me-2 align-middle"></i>
            Delete
          </button>
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          {brandsList?.length > 0 ? (
            <>
              <Table items={brandsList} columns={columns} />
            </>
          ) : (
            'No brand found'
          )}
        </div>
      </div>
      {modal.delete ? (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              // close modal when outside of modal is clicked
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                // do not close modal if anything inside modal content is clicked
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this item?</p>
                <br />

                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{
                      border: '1px solid gray',
                      backgroundColor: 'gray',
                      color: 'white',
                      marginRight: '10px',
                    }}
                    onClick={() =>
                      setModal({
                        ...modal,
                        delete: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => deleteProductFunction()}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default BrandsList;
