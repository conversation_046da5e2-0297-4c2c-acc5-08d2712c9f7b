import Link from 'next/link';
import { useState } from 'react';

import SidebarLink from './sidebarLink';

import { SidebarData } from './SidebarData';

import layout from '../styles/layout.module.css';

interface Props {
  toggleSidebar: (sideBarStatus: boolean) => void;
  showSidebar: boolean;
}

const Sidebar = ({ toggleSidebar, showSidebar }: Props) => {
  const [expandedMenuId, setExpandedMenuId] = useState<number>(-1);
  const [selectedMenuId, setSelectedMenuId] = useState<number>(0);

  const handleShowSubMenu = (id: number) => {
    if (id === expandedMenuId) setExpandedMenuId(-1);
    else setExpandedMenuId(id);
  };

  const handleSelectedId = (id: number) => {
    setSelectedMenuId(id);
  };

  return (
    <div
      style={{
        height: '100%',
        minHeight: '100vh',
        width: showSidebar ? '250px' : '60px',
        backgroundColor: '#343a40',
        position: 'fixed',
        transition: 'width 0.3s linear',
        boxShadow: '0 14px 28px rgba(0,0,0,.25),0 10px 10px rgba(0,0,0,.22)',
      }}
      onClick={() => toggleSidebar(true)}
      // onMouseEnter={() => toggleSidebar(true)}
    >
      <div
        className={`d-flex justify-content-start ${
          showSidebar ? `ms-3` : `ms-1`
        } align-items-center`}
        style={{ height: '56px', borderBottom: '1px solid #4b545c' }}
      >
        {showSidebar ? (
          <h3 className="text-white">Fitsomnia</h3>
        ) : (
          <p className="text-white" style={{ fontSize: '12px' }}>
            Fitsomnia
          </p>
        )}
      </div>

      {SidebarData.map((menu) => (
        <div
          key={menu.id}
          className={`d-flex flex-column justify-content-start ${layout.link}`}
          // style={{ color: "#C2C7D0" }}
        >
          {menu.to !== '' ? (
            <Link href={menu.to} passHref legacyBehavior>
              <div onClick={() => setSelectedMenuId(menu.id)}>
                <SidebarLink
                  menu={menu}
                  expandedMenuId={expandedMenuId}
                  handleShowSubMenu={handleShowSubMenu}
                  selectedMenuId={selectedMenuId}
                  handleSelectedId={handleSelectedId}
                  showSidebar={showSidebar}
                />
              </div>
            </Link>
          ) : (
            <SidebarLink
              menu={menu}
              expandedMenuId={expandedMenuId}
              handleShowSubMenu={handleShowSubMenu}
              selectedMenuId={selectedMenuId}
              handleSelectedId={handleSelectedId}
              showSidebar={showSidebar}
            />
          )}
        </div>
      ))}
    </div>
  );
};

export default Sidebar;
