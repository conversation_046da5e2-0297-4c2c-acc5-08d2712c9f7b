import { userAPI } from '@/APIs';
import { FieldArray, Form, Formik } from 'formik';
import { ICreateBasePollReq, IPollOption } from 'models';
import { PollStatus, PollType } from 'models/poll/enums.poll.interface';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';
import { toast } from 'react-toastify';
import { createPollSchema } from './schemas';

const CreatePoll: FC = () => {
  const router = useRouter();
  const [options, setOptions] = useState<IPollOption[]>([]);

  const handleSubmit = async (data: ICreateBasePollReq) => {
    try {
      const res = await userAPI.createPoll(data);
      if ('data' in res) {
        router.push('/poll');
        toast.success('Poll Created Successfully');
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Can't Create Poll");
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          pollType: PollType.GENERAL,
          title: '',
          subTitle: '',
          status: PollStatus.ACTIVE,
          options: [],
          description: '',
          isPinned: false,
          winnerCount: 0,
          image: '',
          expirationDate: new Date(),
        }}
        onSubmit={(values, actions) => {
          const data: ICreateBasePollReq = {
            pollType: values.pollType,
            title: values.title,
            subTitle: values.subTitle,
            status: values.status,
            isPinned: values.isPinned,
            options: values.options,
            description: values.description,
            image: values.image,
            expirationDate: values.expirationDate,
            winnerCount: values.winnerCount,
          };
          handleSubmit(data);
          actions.setSubmitting(false);
        }}
        validationSchema={createPollSchema}
      >
        {(formikprops) => (
          <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
            <div
              className="content-header clearfix"
              style={{ paddingTop: '10px' }}
            >
              <h3 className="float-start">
                Create A Poll
                <span className="fs-5 p-3">
                  <Link href="/polls" className="text-decoration-none">
                    <i className="bi bi-arrow-left-circle-fill p-2" />
                    <span style={{ fontSize: '14px' }}>Back to poll list</span>
                  </Link>
                </span>
              </h3>
              <div className="float-end">
                <button
                  type="submit"
                  name="save"
                  className="btn btn-primary m-1"
                  disabled={formikprops.isSubmitting || !formikprops.isValid}
                >
                  <p className="float-end mx-1 my-0">
                    {formikprops.isSubmitting ? 'Saving...' : 'Save'}
                  </p>
                </button>
              </div>
            </div>

            <div className="mt-4">
              <div className="form-group mb-3">
                <label>Title</label>
                <input
                  type="text"
                  name="title"
                  className="form-control"
                  value={formikprops.values.title}
                  onChange={formikprops.handleChange}
                />
              </div>
              <div className="form-group mb-3">
                <label>Subtitle</label>
                <input
                  type="text"
                  name="subTitle"
                  className="form-control"
                  value={formikprops.values.subTitle}
                  onChange={formikprops.handleChange}
                />
              </div>
              <div className="form-group mb-3">
                <label>Poll Type</label>
                <select
                  name="pollType"
                  className="form-control"
                  value={formikprops.values.pollType}
                  onChange={formikprops.handleChange}
                >
                  <option value={PollType.GENERAL}>General</option>
                  <option value={PollType.TEAM}>Team</option>
                  <option value={PollType.PLAYER}>Player</option>
                </select>
              </div>
              <div className="form-group mb-3">
                <label>Poll Status</label>
                <select
                  name="status"
                  className="form-control"
                  value={formikprops.values.status}
                  onChange={formikprops.handleChange}
                >
                  <option value={PollStatus.CREATED}>Created</option>
                  <option value={PollStatus.ACTIVE}>Active</option>
                  <option value={PollStatus.INACTIVE}>Inactive</option>
                  <option value={PollStatus.CLOSED}>Closed</option>
                  <option value={PollStatus.DELETED}>Deleted</option>
                </select>
              </div>
              <div className="form-group mb-3 m">
                <label className="form-label">Is pinned?</label>
                <input
                  type="checkbox"
                  name="isPinned"
                  className="form-check-input"
                  onChange={formikprops.handleChange}
                  checked={formikprops.values.isPinned}
                />
              </div>
              <div className="form-group mb-3">
                <label>Description</label>
                <textarea
                  name="description"
                  className="form-control"
                  value={formikprops.values.description}
                  onChange={formikprops.handleChange}
                />
              </div>
              <div className="col-md-6 mb-3">
                <label className="form-label">Winner Count</label>
                <input
                  type="number"
                  name="winnerCount"
                  className="form-control"
                  onChange={formikprops.handleChange}
                  value={formikprops.values.winnerCount}
                />
              </div>
              <div className="form-group mb-3">
                <label>Expiration Date and Time</label>
                <input
                  type="datetime-local"
                  name="expirationDate"
                  className="form-control"
                  value={formikprops.values.expirationDate
                    .toISOString()
                    .slice(0, 16)}
                  onChange={(e) => {
                    formikprops.setFieldValue(
                      'expirationDate',
                      new Date(e.target.value)
                    );
                  }}
                />
              </div>
              <div className="form-group mb-3">
                <label>Image URL</label>
                <input
                  type="text"
                  name="image"
                  className="form-control"
                  value={formikprops.values.image}
                  onChange={formikprops.handleChange}
                />
              </div>
              <div className="form-group mb-3">
                <label>Options</label>
                <FieldArray name="options">
                  {({ push, remove }) => (
                    <div>
                      {formikprops.values.options.map(
                        (option: IPollOption, index) => (
                          <div key={index} className="mb-2">
                            <input
                              type="text"
                              name={`options.${index}.title`}
                              placeholder="Title"
                              className="form-control mb-1"
                              value={option.title}
                              onChange={formikprops.handleChange}
                            />
                            <input
                              type="text"
                              name={`options.${index}.description`}
                              placeholder="Description"
                              className="form-control mb-1"
                              value={option.description}
                              onChange={formikprops.handleChange}
                            />
                            <input
                              type="text"
                              name={`options.${index}.image`}
                              placeholder="Image URL"
                              className="form-control mb-1"
                              value={option.image}
                              onChange={formikprops.handleChange}
                            />
                            <button
                              type="button"
                              onClick={() => remove(index)}
                              className="btn btn-danger"
                            >
                              Remove
                            </button>
                          </div>
                        )
                      )}
                      <button
                        type="button"
                        onClick={() =>
                          push({ title: '', description: '', image: '' })
                        }
                        className="btn btn-secondary mt-2"
                      >
                        Add Option
                      </button>
                    </div>
                  )}
                </FieldArray>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default CreatePoll;
