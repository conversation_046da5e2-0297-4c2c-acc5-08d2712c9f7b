import { FieldArray, Form, Formik } from 'formik';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';
import { toast } from 'react-toastify';

import { userAPI } from '@/APIs';
import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { enableEdit, showModal } from '@/toolkit/modalSlice';
import { IPollOption, Poll } from 'models';
import Link from 'next/link';
import { createPollSchema } from './schemas';

interface Props {
  poll: Poll;
}

const EditPoll: FC<Props> = (props) => {
  const router = useRouter();
  const [poll, setPoll] = useState(props.poll);
  const dispatch = useAppDispatch();
  const edit = useAppSelector((state) => state.persistedReducer.modal.edit);
  const [updateData, setUpdateData] = useState<Poll>();

  const handleSubmit = async (data: Poll) => {
    try {
      const res = await userAPI.updatePoll(poll.id, data);
      if (!res.data) {
        if ('error' in res) {
          toast.error(`Can't Update Poll`);
        }
      } else {
        router.push('/poll');
        toast.success('Poll Updated Successfully');
      }
    } catch (error: any) {
      toast.error(error);
    }
  };

  const handleOptionSelect = async (
    pollId: string,
    pollOptionId: string | undefined,
    winnerCount: number
  ) => {
    console.log(pollId, pollOptionId, winnerCount);
    try {
      const res = await userAPI.selectPollWinner({
        pollId,
        pollOptionId,
        winnerCount,
      });
      console.log(res);
      if (!res.data) {
        if ('error' in res) {
          toast.error(`Can't Select Winner for Poll`);
        }
      } else {
        router.push('/poll');
        toast.success('Poll Winner Selected Successfully');
      }
    } catch (error: any) {
      toast.error(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  if (edit === true) {
    handleSubmit(updateData!);
    dispatch(enableEdit(false));
  }

  return (
    <>
      {poll ? (
        <Formik
          initialValues={{
            title: poll?.title || '',
            subTitle: poll?.subTitle || '',
            description: poll?.description || '',
            winnerCount: poll?.winnerCount || 0,
            image: poll?.image || '',
            expirationDate: poll?.expirationDate || '',
            status: poll?.status || '',
            isPinned: poll?.isPinned,
            pollType: poll?.pollType || '',
            options: poll?.options || [],
          }}
          onSubmit={(values, actions) => {
            const data = {
              ...poll,
              title: values.title,
              subTitle: values.subTitle,
              description: values.description,
              image: values.image,
              expirationDate: new Date(values.expirationDate),
              status: values.status,
              isPinned: values.isPinned,
              pollType: values.pollType,
              winnerCount: values.winnerCount,
              options: values.options.map((option: IPollOption) => ({
                id: option.id,
                title: option.title,
                description: option.description,
                image: option.image,
                voteCount: option.voteCount,
              })),
            };
            dispatch(showModal(true));
            setUpdateData(data);
            actions.setSubmitting(false);
          }}
          validationSchema={createPollSchema}
        >
          {(formikprops) => {
            return (
              <Form
                onSubmit={formikprops.handleSubmit}
                onKeyDown={onKeyDown}
                className="min-vh-100"
              >
                <div className="content-header clearfix pt-4">
                  <h1 className="float-start">
                    Edit Poll Details
                    <span className="fs-5 p-3">
                      <Link href="/poll" className="text-decoration-none">
                        <i className="bi bi-arrow-left-circle-fill p-2" />
                        Back to poll list
                      </Link>
                    </span>
                  </h1>
                  <div className="float-end">
                    <button
                      type="submit"
                      name="save"
                      className="btn btn-primary m-1"
                    >
                      <i className="bi bi-save me-2" />
                      Save
                    </button>
                  </div>
                </div>

                <div className="mt-4 pb-5 card">
                  <div className="card-body">
                    <div className="row">
                      <div className="col-md-6 mb-3">
                        <label className="form-label">
                          Title <span style={{ color: 'red' }}>*</span>
                        </label>
                        <input
                          type="text"
                          name="title"
                          className="form-control"
                          onChange={formikprops.handleChange}
                          value={formikprops.values.title}
                        />
                        {formikprops.errors.title &&
                          formikprops.touched.title && (
                            <div className="text-danger">
                              {formikprops.errors.title}
                            </div>
                          )}
                      </div>

                      <div className="col-md-6 mb-3">
                        <label className="form-label">Sub Title</label>
                        <input
                          type="text"
                          name="subTitle"
                          className="form-control"
                          onChange={formikprops.handleChange}
                          value={formikprops.values.subTitle}
                        />
                      </div>

                      <div className="col-md-6 mb-3">
                        <label className="form-label">Winner Count</label>
                        <input
                          type="number"
                          name="winnerCount"
                          className="form-control"
                          onChange={formikprops.handleChange}
                          value={formikprops.values.winnerCount}
                        />
                      </div>
                      <div className="col-12 mb-3">
                        <label className="form-label">Description</label>
                        <textarea
                          name="description"
                          className="form-control"
                          rows={4}
                          onChange={formikprops.handleChange}
                          value={formikprops.values.description}
                        ></textarea>
                      </div>

                      <div className="col-md-6 mb-3">
                        <label className="form-label">Image URL</label>
                        <input
                          type="text"
                          name="image"
                          className="form-control"
                          onChange={formikprops.handleChange}
                          value={formikprops.values.image}
                        />
                      </div>

                      <div className="col-md-6 mb-3">
                        <label className="form-label">
                          Expiration Date and Time{' '}
                          <span style={{ color: 'red' }}>*</span>
                        </label>
                        <input
                          type="datetime-local"
                          name="expirationDate"
                          className="form-control"
                          onChange={formikprops.handleChange}
                          value={new Date(formikprops.values.expirationDate)
                            .toISOString()
                            .slice(0, 16)}
                        />
                        {formikprops.errors.expirationDate &&
                          formikprops.touched.expirationDate && (
                            <div className="text-danger">
                              {formikprops.errors.expirationDate}
                            </div>
                          )}
                      </div>

                      <div className="col-md-6 mb-3">
                        <label className="form-label">Status</label>
                        <select
                          name="status"
                          className="form-control"
                          onChange={formikprops.handleChange}
                          value={formikprops.values.status}
                        >
                          <option value="">Select Status</option>
                          <option value="ACTIVE">Active</option>
                          <option value="INACTIVE">Inactive</option>
                          <option value="CLOSED">Closed</option>
                          <option value="DELETED">Deleted</option>
                        </select>
                      </div>

                      <div className="col-md-6 mb-3">
                        <label className="form-label">Is pinned?</label>
                        <input
                          type="checkbox"
                          name="isPinned"
                          className="form-check-input"
                          onChange={formikprops.handleChange}
                          checked={formikprops.values.isPinned}
                        />
                      </div>

                      <div className="col-md-6 mb-3">
                        <label className="form-label">Poll Type</label>
                        <select
                          name="pollType"
                          className="form-control"
                          onChange={formikprops.handleChange}
                          value={formikprops.values.pollType}
                        >
                          <option value="">Select Poll Type</option>
                          <option value="GENERAL">General</option>
                          <option value="TEAM">Team</option>
                          <option value="PLAYER">Player</option>
                        </select>
                      </div>
                    </div>

                    <div className="mt-4">
                      <label className="form-label mb-0">Options</label>

                      <FieldArray name="options">
                        {({ push, remove }) => (
                          <>
                            {formikprops.values.options.map((option, index) => (
                              <div key={index} className="card mb-3 bg-light">
                                <div className="card-body">
                                  <div className="row">
                                    <div className="col-md-4 mb-2">
                                      <input
                                        type="text"
                                        name={`options.${index}.title`}
                                        placeholder="Title"
                                        className="form-control"
                                        onChange={formikprops.handleChange}
                                        value={option.title}
                                      />
                                    </div>
                                    <div className="col-md-4 mb-2">
                                      <input
                                        type="text"
                                        name={`options.${index}.description`}
                                        placeholder="Description"
                                        className="form-control"
                                        onChange={formikprops.handleChange}
                                        value={option.description}
                                      />
                                    </div>
                                    <div className="col-md-3 mb-2">
                                      <input
                                        type="text"
                                        name={`options.${index}.image`}
                                        placeholder="Image URL"
                                        className="form-control"
                                        onChange={formikprops.handleChange}
                                        value={option.image}
                                      />
                                    </div>
                                    <div className="col-md-1 d-flex align-items-center">
                                      <button
                                        type="button"
                                        className="btn btn-danger btn-sm"
                                        onClick={() => remove(index)}
                                      >
                                        <i className="bi bi-trash"></i>
                                      </button>
                                    </div>
                                    {poll.status === 'INACTIVE' && (
                                      <div className="col-md-4 mb-2">
                                        <button
                                          type="button"
                                          className="btn btn-info btn-sm"
                                          onClick={() =>
                                            handleOptionSelect(
                                              poll.id,
                                              option.id,
                                              formikprops.values.winnerCount
                                            )
                                          }
                                        >
                                          Select Option
                                        </button>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            ))}
                          </>
                        )}
                      </FieldArray>
                      <div className="d-flex justify-content-between align-items-center mb-3">
                        <FieldArray name="options">
                          {({ push, remove }) => (
                            <button
                              type="button"
                              className="btn btn-success btn-sm"
                              onClick={() =>
                                push({ title: '', description: '', image: '' })
                              }
                            >
                              <i className="bi bi-plus-circle me-2"></i>
                              Add New Option
                            </button>
                          )}
                        </FieldArray>
                      </div>
                    </div>
                  </div>
                </div>
              </Form>
            );
          }}
        </Formik>
      ) : (
        'Something went wrong!'
      )}
    </>
  );
};

export default EditPoll;
