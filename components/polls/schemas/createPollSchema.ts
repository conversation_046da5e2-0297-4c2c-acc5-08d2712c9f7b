import { PollStatus, PollType } from 'models/poll/enums.poll.interface';
import { array, date, number, object, string } from 'yup';

export const createPollSchema = object().shape({
  pollType: string()
    .oneOf(Object.values(PollType))
    .required('This field must not be empty'),
  title: string().required('This field must not be empty'),
  subTitle: string().optional(),
  status: string()
    .oneOf(Object.values(PollStatus))
    .required('This field must not be empty'),
  options: array().of(
    object().shape({
      title: string().required('This field must not be empty'),
      description: string().optional(),
      image: string().required('This field must not be empty'),
    })
  ),
  description: string().optional(),
  image: string().optional(),
  expirationDate: date().required('This field must not be empty'),
  winnerCount: number().optional(),
});
