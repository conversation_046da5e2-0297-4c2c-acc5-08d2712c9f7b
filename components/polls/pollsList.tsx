import Link from 'next/link';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import Table from '@/components/global/table/table';
import { config } from 'config';
import { Poll } from 'models';
import moment from 'moment';
import { toast } from 'react-toastify';

interface Props {
  pollList: Poll[];
  setPolls: Function;
  setSkip: Function;
  skip: number;
}

const PollList: FC<Props> = ({ pollList, setPolls, setSkip, skip }) => {
  const [pollId, setPollId] = useState('');
  const [modal, setModal] = useState({
    delete: false,
  });

  const onChangeForList = async (skip: number) => {
    try {
      const pollList = await userAPI.getPolls(skip, config.limit);
      if ('data' in pollList) {
        setPolls(pollList.data);
      } else {
        toast.error(pollList.error.message);
      }
    } catch (error) {}
    setSkip(0);
  };

  const deletePoll = async () => {
    try {
      const res = await userAPI.deletePoll(pollId);
      if ('data' in res) {
        toast.success(res.data.message);
        onChangeForList(0);
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
    setModal({
      ...modal,
      delete: false,
    });
  };

  const onClickForDelete = (id: string) => {
    setPollId(id);
    setModal({ ...modal, delete: true });
  };

  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const columns = [
    {
      label: 'Title',
      path: 'title',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.title || ''}</td>
      ),
    },
    {
      label: 'Sub Title',
      path: 'subTitle',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.subTitle || ''}</td>
      ),
    },
    {
      label: 'Status',
      path: 'status',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.status || ''}</td>
      ),
    },
    {
      label: 'Expiration Date',
      path: 'expirationDate',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {moment(data[key]).utc().local().format('llll')}
        </td>
      ),
    },
    {
      label: 'Edit',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/poll/edit/[id]`,
              query: { id: data?.[key] },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-info">
              <span>
                <i className="bi bi-pencil me-2 align-middle"></i>
              </span>
              Edit
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Delete',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <button
            className="btn btn-default btn-outline-danger"
            onClick={() => onClickForDelete(data.id)}
          >
            <i className="bi bi-trash3-fill me-2 align-middle"></i>
            Delete
          </button>
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={pollList}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>
      {modal.delete ? (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this item?</p>
                <br />
                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{ marginRight: '10px' }}
                    onClick={() => setModal({ ...modal, delete: false })}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => deletePoll()}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default PollList;
