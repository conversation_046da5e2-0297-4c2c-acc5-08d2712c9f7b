import { ErrorMessage, Field } from 'formik';
import { FC, useState } from 'react';
import { SketchPicker } from 'react-color';

interface Props {
  setColors?: Function;
  colors?: string[];
  setFieldValue?: Function;
}

const PickColorField: FC<Props> = ({ setColors, colors, setFieldValue }) => {
  const [color, setColor] = useState('lightblue');

  const pickerStyle = {
    default: {
      picker: {
        width: '170px',
      },
    },
  };

  const addColor = () => {
    const colorList = (document.getElementById('color') as HTMLInputElement)
      .value;
    if (colorList.length > 0) {
      setFieldValue!('colors', '');
      setColors!([...colors!, colorList]);
    }
  };

  const removeColor = (colorToRemove: string) => {
    const colorList = colors?.filter(
      (singleitem) => singleitem != colorToRemove
    );
    setColors!(colorList);
  };

  return (
    <>
      <div className="form-group row my-2 mb-3">
        <div className="col-md-3">
          <div className="label-wrapper row row-cols-auto float-md-end me-3">
            <label className="col-form-label col fs-5 px-1" htmlFor="color">
              Colors
            </label>
          </div>
        </div>
        <div className="col-md-9">
          <div className="input-group mb-2">
            <Field
              // disabled
              placeholder="Available colors here..."
              className="form-control rounded-start border-black"
              id="color"
              name="color"
            />
            <span className="btn btn-secondary rounded-end" onClick={addColor}>
              +
            </span>
          </div>
          <div className="errMsg text-danger text-red-600">
            <ErrorMessage name="color" />
          </div>
          <div className="d-flex flex-lg-row flex-column gap-5">
            <div className="d-flex flex-column">
              <p>Selected Color:</p>
              <button
                type="button"
                className="p-3 border border-none"
                style={{ backgroundColor: color }}
              ></button>
            </div>

            <div className="d-flex flex-column">
              <p>Move the dot to select color: </p>
              <div>
                {' '}
                <SketchPicker
                  styles={pickerStyle}
                  color={color}
                  onChange={(updatedColor: any) => {
                    setFieldValue!('color', updatedColor.hex);
                    setColor(updatedColor.hex);
                  }}
                />
              </div>
            </div>

            <div className="d-flex flex-column">
              <p>Selected colors:</p>

              <div>
                <ul>
                  {colors?.map((item) => (
                    <li
                      key={item}
                      className="border btn me-2 my-2 pe-2 py-2 pe-none"
                    >
                      <button
                        className="p-3 border border-none"
                        style={{ backgroundColor: item }}
                      ></button>
                      <span
                        onClick={() => removeColor(item)}
                        className="pe-auto ms-4 text-danger"
                      >
                        x
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PickColorField;
