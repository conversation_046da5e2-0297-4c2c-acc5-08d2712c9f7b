import myImageLoader from 'image/loader';
import Image from 'next/image';
import { FC, useState, useEffect } from 'react';

import FieldTemplate from '@/components/common/fieldTemplate';
import { useAppSelector } from '@/redux-hooks';
import { Product } from 'models';
import { handleMediaUpload } from 'utils/handleMediaUpload';

interface Props {
  setFieldValue: Function;
  setPhotosUrls: Function;
  photosUrls: string[];
  product?: Product;
  edit?: boolean;
}

const PhotosForm: FC<Props> = ({
  setFieldValue,
  product,
  edit,
  setPhotosUrls,
  photosUrls,
}) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');
  const [file, setFile] = useState<string[]>([]);
  const token = useAppSelector((state) => state.persistedReducer.auth.token);

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  useEffect(() => {
    let list = [] as string[];
    edit &&
      product?.photos!.map((photo) => {
        list.push(photo?.url!);
      });
    setFile(list);
  }, []);

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="photos"
        id="photos"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#photosTab"
            aria-expanded="true"
            aria-controls="photosTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <div className="fs-5 col px-3 text-start">
                <i
                  className="bi bi-image-fill col-1  px-1"
                  style={{ fontSize: '25px' }}
                />
                Photos
              </div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="photosTab">
          <div className="card-body">
            <div className="row justify-content-center">
              <div className="form-group row my-2 mb-3">
                <div className="col-md-3">
                  <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                    <label
                      className="col-form-label col fs-5 px-1 text-center"
                      htmlFor="photosUrl"
                    >
                      Upload Images
                      <span className="required text-danger ">*</span>
                    </label>
                  </div>
                </div>
                <div className="col-md-9">
                  <div className={`input-group mt-2`}>
                    <input
                      id="photosUrl"
                      name="photosUrl"
                      type="file"
                      multiple={true}
                      accept="image/*"
                      onChange={async (event: any) => {
                        let list = new Set(photosUrls);

                        for (let i = 0; i < event?.target?.files.length; i++) {
                          const fileData = {
                            featureName: 'product',
                            filenames: [event?.target?.files![i].name],
                          };
                          const fileInfo = (await handleMediaUpload(
                            fileData,
                            event?.target?.files![i],
                            token,
                            true
                          )) as string;
                          list.add(fileInfo);
                          // let list = file;
                          // list.push({
                          //   src: URL.createObjectURL(event?.target?.files![i]),
                          //   type: event?.target?.files![i].type,
                          // });
                          // setFile(list);
                        }
                        setPhotosUrls(Array.from(list));
                        setFile(Array.from(list) as string[]);
                      }}
                    />
                  </div>
                  <br />
                  {file && (
                    <>
                      <div className=" d-flex justified-content-start gap-5 flex-wrap">
                        {file.map((url: string) => (
                          <div className="position-relative" key={url}>
                            <Image
                              loader={myImageLoader}
                              className="mt-3 "
                              src={url}
                              height={100}
                              width={100}
                              alt="photo of product"
                            />
                            <button
                              onClick={() => {
                                let list = file.filter((f) => f !== url);
                                setFile(list);
                                setPhotosUrls(list);
                              }}
                              className="position-absolute start-100 top-0 btn btn-secondary px-1 py-0 rounded-circle"
                            >
                              X
                            </button>
                          </div>
                        ))}
                      </div>
                    </>
                  )}

                  {/* { product && edit && (
                    <>
                      <div className=" d-flex justified-content-start gap-5 flex-wrap">
                        {product?.photos!.map((photo: any) => (
                          <div className="position-relative" key={photo.url}>
                            <Image
                              loader={myImageLoader}
                              className="mt-3 "
                              src={photo.url}
                              height={100}
                              width={100}
                              alt="photo of product"
                            />
                            <button
                              onClick={() => {
                                let list = file.filter((f) => f !== photo.url);
                                setFile(list);
                                setPhotosUrls(list);
                              }}
                              className="position-absolute start-100 top-0 btn btn-secondary px-1 py-0 rounded-circle"
                            >
                              X
                            </button>
                          </div>
                        ))}
                      </div>
                    </>
                  )} */}
                </div>
              </div>
            </div>
            {/* <FieldTemplate
              label="URL"
              isRequired={false}
              fieldID="photosUrl"
              fieldType=""
              fieldAs="textarea"
              extraClass=""
              fieldClass=""
            /> */}

            <FieldTemplate
              label="Title"
              isRequired={false}
              fieldID="photosTitle"
              fieldType="text"
              fieldAs=""
              extraClass=""
              fieldClass=""
            />
            <FieldTemplate
              label="Display Order"
              isRequired={false}
              fieldID="displayOrderPhotos"
              fieldType="number"
              fieldAs=""
              extraClass=""
              fieldClass=""
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default PhotosForm;
