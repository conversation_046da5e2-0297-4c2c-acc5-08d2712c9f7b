import { FC, useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import CustomSelect from '@/components/common/customSelectInput';
import FieldTemplate from '@/components/common/fieldTemplate';
import { tagsOption } from '@/components/products/models/index';
import { ErrorMessage, Field } from 'formik';
import { Brand, Tag } from 'models';
import { SketchPicker } from 'react-color';
import Pick<PERSON>olor<PERSON>ield from './colorPickingField';
import { toast } from 'react-toastify';

interface Options {
  label: string;
  value: string;
}

interface Props {
  setColors?: Function;
  colors?: string[];
  setFieldValue?: Function;
}

const ProductInfoForm: FC<Props> = ({ setColors, colors, setFieldValue }) => {
  const [tags, setTags] = useState<Tag[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [btnToggler, setBtnToggler] = useState('bi-dash');
  const [tagsOptions, setTagsOption] = useState<tagsOption[]>([]);
  const [brandsOptions, setBrandsOption] = useState<tagsOption[]>([]);
  const [sizeOptions, setSizeOptions] = useState<Options[]>([
    {
      label: 'XS (Extra Small)',
      value: 'XS',
    },
    {
      label: 'S (Small)',
      value: 'S',
    },
    {
      label: 'M (Medium)',
      value: 'M',
    },
    {
      label: 'L (Large)',
      value: 'L',
    },
    {
      label: 'XL (Extra Large)',
      value: 'XL',
    },
    {
      label: 'XXL (Double Extra Large)',
      value: 'XXL',
    },
  ]);

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };
  const getTags = async () => {
    const res = await userAPI.getTags();
    if ('data' in res!) {
      const data = res?.data;
      data ? setTags(data) : '';
    } else {
      toast.error(res?.error.message);
    }
  };
  const setTagsOptions = () => {
    const temp: tagsOption[] = [];
    tags?.map((tag) => {
      temp.push({
        label: tag?.name,
        value: tag?.name,
      });
      setTagsOption(temp);
    });
  };

  const getBrands = async () => {
    const res = await userAPI.getBrands(0, 10000);
    if ('data' in res!) setBrands(res?.data.brands);
    else toast.error(res.error.message);
  };

  const setBrandsOptions = () => {
    const temp: tagsOption[] = [];
    brands?.map((brnad) => {
      temp.push({
        label: brnad?.info?.name,
        value: brnad?.info?.name,
      });
      setBrandsOption(temp);
    });
  };
  useEffect(() => {
    tags[0] ? '' : getTags();
    tagsOptions[0] ? '' : setTagsOptions();
  }, [tagsOptions, tags]);
  useEffect(() => {
    brands[0] ? '' : getBrands();
    brandsOptions[0] ? '' : setBrandsOptions();
  }, [brandsOptions, brands]);

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="product-info"
        data-hideattribute="ProductPage.HideInfoBlock"
        id="product-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#prouctInfoTab"
            aria-expanded="true"
            aria-controls="prouctInfoTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <div className="fs-5 col text-start px-3">
                <i
                  className="bi bi-info-lg col-1 px-1"
                  style={{ fontSize: '25px' }}
                />
                Product info
              </div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="prouctInfoTab">
          <div className="card-body">
            <FieldTemplate
              label="Product name"
              isRequired={true}
              fieldID="productName"
              fieldType="text"
            />
            <FieldTemplate
              label="Short description"
              isRequired={false}
              fieldID="ShortDescription"
              fieldAs="textarea"
            />
            <FieldTemplate
              label="Full description"
              isRequired={false}
              fieldID="FullDescription"
              fieldAs="textarea"
            />
            <FieldTemplate
              label="SKU"
              isRequired={true}
              fieldID="Sku"
              fieldType="text"
            />
            <FieldTemplate
              label="Price"
              isRequired={true}
              fieldID="Price"
              fieldType="number"
            />
            <FieldTemplate
              label="Old price"
              isRequired={true}
              fieldID="OldPrice"
              fieldType="number"
            />
            {sizeOptions[0] ? (
              <FieldTemplate
                // disabled
                label="Size"
                // isRequired={true}
                fieldID="size"
                fieldType="none"
                fieldClass="custom-select w-100"
                options={sizeOptions}
                component={CustomSelect}
                placeholder="Select available sizes..."
                ismulti={true}
              />
            ) : (
              'Size list is empty'
            )}

            {/* {colorOptions[0] ? (
              <FieldTemplate
                // disabled
                label="Colors"
                // isRequired={true}
                fieldID="color"
                fieldType="none"
                fieldClass="custom-select w-100"
                options={colorOptions}
                component={CustomSelect}
                placeholder="Select available colors..."
                ismulti={true}
              />
            ) : (
              'Color list is empty'
            )} */}

            <FieldTemplate
              label="Weight in Pound (lb)"
              isRequired={true}
              fieldID="weight"
              fieldType="number"
            />
            {/* <FieldTemplate
              label="Weight Unit"
              //isRequired={true}
              fieldID="weightUnit"
              placeholder="Default is Ounces (OZ)"
              fieldType="text"
            /> */}
            <FieldTemplate
              label="Height"
              //isRequired={true}
              fieldID="height"
              placeholder="Height in cm"
              fieldType="number"
            />
            <FieldTemplate
              label="Width"
              //isRequired={true}
              fieldID="width"
              placeholder="Width in cm"
              fieldType="number"
            />
            <FieldTemplate
              label="Length"
              //isRequired={true}
              fieldID="length"
              placeholder="length in cm"
              fieldType="number"
            />

            <FieldTemplate
              label="Stock"
              //isRequired={true}
              fieldID="stock"
              fieldType="number"
            />

            {/* <FieldTemplate
              label="Product cost"
              isRequired={true}
              fieldID="ProductCost"
              fieldType="number"
            /> */}

            {/* here color picker */}
            <PickColorField
              colors={colors}
              setColors={setColors}
              setFieldValue={setFieldValue}
            />

            <FieldTemplate
              label="Show on HomePage"
              isRequired={false}
              fieldID="showOnHomePage"
              fieldType="checkbox"
              fieldClass="check-box mt-2  "
            />
            <FieldTemplate
              label="Include on top menu"
              isRequired={false}
              fieldID="includeInTopMenu"
              fieldType="checkbox"
              fieldClass="check-box mt-2  "
            />
            <FieldTemplate
              label="Allow to select page size"
              isRequired={false}
              fieldID="allowToSelectPageSize"
              fieldType="checkbox"
              fieldClass="check-box mt-2  "
            />
            <FieldTemplate
              label="Published"
              isRequired={false}
              fieldID="published"
              fieldType="checkbox"
              fieldClass="check-box mt-2  "
            />
            <FieldTemplate
              label="Display Order"
              isRequired={true}
              fieldID="displayOrder"
              fieldType="number"
            />
            <FieldTemplate
              label="Featured"
              isRequired={false}
              fieldID="isFeatured"
              fieldType="checkbox"
              fieldClass="check-box mt-2  "
            />

            {/* <div className="form-group row">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end py-2">
                  <label
                    className="col-form-label col px-1"
                    htmlFor="publishDate"
                  >
                    Publlish Date
                  </label>
                </div>
              </div>
              <div className="col-md-9 mt-md-3 mx-2 mx-md-0">
                <div className="input-group ">
                  <Field
                    type="date"
                    id="publishDate"
                    name="publishDate"
                    aria-disabled="false"
                    className="form-control"
                  />
                </div>
                <div className="errMsg text-red-600 text-danger">
                  <ErrorMessage name="publishDate" />
                </div>
              </div>
            </div> */}
            {tagsOptions[0] ? (
              <FieldTemplate
                label="Tags"
                isRequired={true}
                fieldID="tags"
                fieldType="none"
                fieldClass="custom-select w-100"
                options={tagsOptions}
                component={CustomSelect}
                placeholder="Select tags..."
                ismulti={true}
              />
            ) : (
              'tags empty'
            )}

            <FieldTemplate
              label="Brands"
              isRequired={true}
              fieldID="brands"
              fieldType="none"
              fieldClass="custom-select w-100"
              options={brandsOptions}
              component={CustomSelect}
              placeholder="Select brands..."
              ismulti={true}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default ProductInfoForm;
