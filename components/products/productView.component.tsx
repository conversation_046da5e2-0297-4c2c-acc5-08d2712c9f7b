import { ViewProductInterface } from '@/components/products/models/index';
import CaegoryCard from '@/components/products/viewCards/categoryCard.component';
import ManufacturerCard from '@/components/products/viewCards/manufacturerCard.comonent';
import MetaCard from '@/components/products/viewCards/metaCard.Component';
import PhotosCard from '@/components/products/viewCards/photosCard';
import ProductInfoCard from '@/components/products/viewCards/productInfo.card';
import Link from 'next/link';

const ViewProduct: React.FC<ViewProductInterface> = (
  props: ViewProductInterface
) => {
  const { product } = props;

  return (
    <>
      {product ? (
        <div>
          <div className="content-header clearfix mt-3">
            <h1 className="float-start fs-2">
              View product details
              <span className="fs-5 p-3">
                <Link href="/Product" className="text-decoration-none ">
                  <i className="bi bi-arrow-left-circle-fill p-2" />
                  Back to product list
                </Link>
              </span>
            </h1>
          </div>

          <div className="mt-4">
            <ProductInfoCard product={product} />
            <MetaCard product={product} />
            <PhotosCard product={product} />
            <ManufacturerCard product={product} />
            <CaegoryCard categories={product.categories} />
          </div>
        </div>
      ) : (
        ''
      )}
    </>
  );
};

export default ViewProduct;
