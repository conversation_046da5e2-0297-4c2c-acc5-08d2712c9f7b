import { ErrorMessage, Field, Form, Formik } from 'formik';
import { FC } from 'react';

import { userAPI } from '@/APIs';
import { SearchWindowProps } from '@/components/products/models/index';
import { searchProductSchema } from '@/components/products/schema/productSchema/index';
import { config } from 'config';
import { useRouter } from 'next/router';
import { toast } from 'react-toastify';

const SearchWindow: FC<SearchWindowProps> = ({
  setProducts,
  setShowSeeMore,
}: SearchWindowProps) => {
  const router = useRouter();
  const handleSearchSubmit = async (data: string) => {
    if (data == '') {
      const productsList = await userAPI.getProducts(0, config.limit);
      if (productsList) setProducts(productsList.data);
    } else {
      const searchProduct: any = await userAPI.searchProduct(data);
      if (searchProduct) {
        setProducts([searchProduct]);
        setShowSeeMore(false);
      }
    }
  };

  const getAllProducts = async () => {
    const productsList = await userAPI.getProducts(0, config.limit);
    if ('data' in productsList) {
      setShowSeeMore(true);
      setProducts(productsList.data);
    } else {
      toast.error(productsList.error.message);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          // SearchProductName: "",
          // SearchCategoryId: "",
          // SearchIncludeSubCategories: false,
          // SearchManufacturerId: 0,
          // SearchVendorId: 0,
          // SearchWarehouseId: 0,
          // SearchProductTypeId: 0,
          // SearchPublishedId: 0,
          GoDirectlyToSku: '',
        }}
        onSubmit={(values, actions) => {
          // const data = {
          //   SearchProductName: values.SearchProductName,
          //   SearchCategoryId: values.SearchCategoryId,
          //   SearchIncludeSubCategories: values.SearchIncludeSubCategories,
          //   SearchManufacturerId: values.SearchManufacturerId,
          //   SearchVendorId: values.SearchVendorId,
          //   SearchWarehouseId: values.SearchWarehouseId,
          //   SearchProductTypeId: values.SearchProductTypeId,
          //   SearchPublishedId: values.SearchPublishedId,
          //   GoDirectlyToSku: values.GoDirectlyToSku,
          // };
          handleSearchSubmit(values.GoDirectlyToSku);
          actions.setSubmitting(false);
        }}
        validationSchema={searchProductSchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div className="card border-1 mt-3 rounded">
                <div className="card-header">
                  <span className="ms-2 fs-4">Search</span>
                </div>
                <div className="card-body">
                  <div className="form-group row py-2">
                    <div className="col-md-3">
                      <div className="label-wrapper row row-cols-auto float-md-end px-2">
                        <label
                          className="col-form-label"
                          htmlFor="GoDirectlyToSku"
                        >
                          Go directly to product SKU
                        </label>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="Field-group Field-group-short ">
                        <div className="row">
                          <Field
                            className="form-control col rounded-start rounded-0 my-3 mt-0 "
                            id="GoDirectlyToSku"
                            name="GoDirectlyToSku"
                            type="text"
                          />
                        </div>
                        <div className="errMsg text-danger text-red-600">
                          <ErrorMessage name="GoDirectlyToSku" />
                        </div>
                      </div>
                    </div>
                    <div className="col-md-3">
                      <span className="Field-group-append">
                        <button
                          type="submit"
                          id="goToProductBySku"
                          name="goToProductBySku"
                          className="btn btn-primary btn-flat rounded-end  rounded-0 my-0 "
                        >
                          Go
                        </button>
                      </span>
                      <button
                        className="ms-3 btn btn-secondary btn-flat rounded-2 rounded-0 my-0"
                        onClick={() => {
                          getAllProducts();
                          formikprops.setFieldValue('GoDirectlyToSku', '');
                        }}
                      >
                        Reset
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default SearchWindow;
