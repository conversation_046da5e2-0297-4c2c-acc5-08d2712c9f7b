import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import Table from '@/components/global/table/table';
import { config } from 'config';
import myImageLoader from 'image/loader';
import { ReviewListWithUserInfo } from 'models/order/review.interface';
import Image from 'next/image';
import { handlePagination } from 'utils/handlePagination';
import { toast } from 'react-toastify';

interface Props {
  productId: string;
  reviewList: ReviewListWithUserInfo[];
  setReviews: Function;
  setSkip: Function;
  skip: number;
}

const ReviewList: FC<Props> = ({
  productId,
  reviewList,
  setReviews,
  setSkip,
  skip,
}) => {
  // const [currentPage, setCurrentPage] = useState(1);
  // const [PageSize, setPageSize] = useState(7);
  const [reviewId, setReviewID] = useState('');
  const [userId, setUserID] = useState('');

  const onChangeForList = async (skip: number) => {
    const reviewList = await userAPI.getProductReviews(
      productId,
      skip,
      config.limit
    );
    if ('data' in reviewList!) {
      setReviews(reviewList.data);
      setSkip(0);
    } else {
      toast.error(reviewList.error.message);
    }
  };

  const deleteReviewFunction = async () => {
    const res = await userAPI.deleteProductReview(reviewId, userId);
    if ('data' in res!) {
      onChangeForList(0);
    } else {
      toast.error(res.error.message);
    }
    setModal({
      ...modal,
      delete: false,
    });
  };

  const onClickForDelete = (reviewId: string, userId: string) => {
    setReviewID(reviewId);
    setUserID(userId);
    setModal({ ...modal, delete: true });
  };

  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const [modal, setModal] = useState({
    delete: false,
  });

  const columns = [
    {
      label: 'Picture',
      path: 'url',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.image![0] ? (
            <Image
              loader={myImageLoader}
              src={`${data?.image![0][key]!}`}
              height={75}
              width={75}
              alt="attached photo"
            />
          ) : (
            'No photo attached'
          )}
        </td>
      ),
    },
    {
      label: 'Order ID',
      path: 'orderId',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.[key]}</td>
      ),
    },
    {
      label: 'Message',
      path: 'text',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.[key]}</td>
      ),
    },
    {
      label: 'Rating',
      path: 'rating',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.[key]}</td>
      ),
    },
    {
      label: 'User',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.userInfo[key]}</td>
      ),
    },
    {
      label: 'User Image',
      path: 'profile',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.userInfo.image[key] ? (
            <Image
              loader={myImageLoader}
              src={`${data?.userInfo.image[key]}`}
              height={75}
              width={75}
              alt="..."
            />
          ) : (
            <p>No Image Found</p>
          )}
        </td>
      ),
    },
    {
      label: 'Delete',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <button
            className="btn btn-default btn-outline-danger"
            onClick={() => onClickForDelete(data.id, data.userId)}
          >
            <i className="bi bi-trash3-fill me-2 align-middle"></i>
            Delete
          </button>
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          {reviewList?.length > 0 ? (
            <>
              {' '}
              <Table
                items={reviewList}
                columns={columns}
                onClickForSort={onClickForSort}
              />
            </>
          ) : (
            'No review to show'
          )}
        </div>
      </div>
      {modal.delete ? (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              // close modal when outside of modal is clicked
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                // do not close modal if anything inside modal content is clicked
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this item?</p>
                <br />

                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{
                      border: '1px solid gray',
                      backgroundColor: 'gray',
                      color: 'white',
                      marginRight: '10px',
                    }}
                    onClick={() =>
                      setModal({
                        ...modal,
                        delete: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => deleteReviewFunction()}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default ReviewList;
