import * as Yup from 'yup';
import { number, object, string, mixed } from 'yup';

const decimalPlaceRegex = /^\d+(\.\d{1})?$/;

export const editProductSchema = object().shape({
  productName: string()
    .min(2, 'This field must be at least 2 characters long')
    .max(50, 'This field must be at most 50 characters long'),
  ShortDescription: string()
    .min(2, 'This field must be at least 2 characters long')
    .max(200, 'This field must be at most 200 characters long'),
  FullDescription: string(),
  Sku: string()
    .min(1, 'This field must be at least 1 characters long')
    .max(400, 'This field must be at most 400 characters long'),

  OldPrice: number(),
  Price: number(),
  //ProductCost: number().required('This field must not be empty'),

  showOnHomePage: Yup.boolean(),
  includeInTopMenu: Yup.boolean(),
  allowToSelectPageSize: Yup.boolean(),
  published: Yup.boolean(),
  displayOrder: number(),
  isFeatured: Yup.boolean(),
  publishDate: string(),

  tags: Yup.array(),
  brands: Yup.array(),
  keywords: string(),
  metaTitle: string(),
  metaDescription: string(),
  metaFriendlyPageName: string(),

  photosUrl: mixed(),
  photosID: string(),
  photosTitle: string(),
  displayOrderPhotos: string(),
  manufacturerName: string(),
  height: number().max(22).test(
    'maxDigitsAfterDecimal',
    'number field must have 1 digits after decimal or less',
    (number: any) => decimalPlaceRegex.test(number)
  ),
  width: number().max(18).test(
    'maxDigitsAfterDecimal',
    'number field must have 1 digits after decimal or less',
    (number: any) => decimalPlaceRegex.test(number)
  ),
  weight: number().max(70).test(
    'maxDigitsAfterDecimal',
    'number field must have 1 digits after decimal or less',
    (number: any) => decimalPlaceRegex.test(number)
  ),
  length: number().max(15).test(
    'maxDigitsAfterDecimal',
    'number field must have 1 digits after decimal or less',
    (number: any) => decimalPlaceRegex.test(number)
  ),
  stock: number(),
  //weightUnit: string(),
  size: Yup.array(),
  colors: string(),
});
