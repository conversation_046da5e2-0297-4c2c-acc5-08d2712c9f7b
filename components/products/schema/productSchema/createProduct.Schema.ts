import * as Yup from 'yup';
import { number, object, string, array, mixed } from 'yup';

const decimalPlaceRegex = /^\d+(\.\d{1})?$/;

export const productSchema = object().shape({
  productName: string()
    .min(2, 'This field must be at least 2 characters long')
    .max(50, 'This field must be at most 50 characters long')
    .required('This field must not be empty'),
  ShortDescription: string()
    .min(2, 'This field must be at least 2 characters long')
    .max(200, 'This field must be at most 200 characters long'),
  FullDescription: string(),
  Sku: string()
    .required('This field must not be empty')
    .min(1, 'This field must be at least 1 characters long')
    .max(400, 'This field must be at most 400 characters long'),

  OldPrice: number().required(
    'This field should not be empty and should give a positive number'
  ),
  Price: number().required(
    'This field should not be empty and should give a positive number'
  ),
  //ProductCost: number().required('This field must not be empty'),

  showOnHomePage: Yup.boolean(),
  includeInTopMenu: Yup.boolean(),
  allowToSelectPageSize: Yup.boolean(),
  published: Yup.boolean(),
  displayOrder: number().required(
    'This field should not be empty and should give a positive number'
  ),
  isFeatured: Yup.boolean(),
  publishDate: string(),

  tags: Yup.array()
    .min(1, 'You must select one')
    .required('This field is required'),
  brands: Yup.array()
    .min(1, 'You must select one')
    .required('This field is required'),

  keywords: string().required('This field must not be empty'),
  metaTitle: string(),
  metaDescription: string(),
  metaFriendlyPageName: string().required('This field must not be empty'),

  photosUrl: mixed().required('File is required'),
  photosID: string(),
  photosTitle: string(),
  displayOrderPhotos: string(),
  manufacturerName: string().required('Select a manufacturer'),
  height: number().max(22).nullable().test(
    'maxDigitsAfterDecimal',
    'number field must have 2 digits after decimal or less',
    (number: any) => decimalPlaceRegex.test(number)
  ),
  width: number().max(18).nullable().test(
    'maxDigitsAfterDecimal',
    'number field must have 1 digits after decimal or less',
    (number: any) => decimalPlaceRegex.test(number)
  ),
  weight: number().max(70)
    .required('This field must not be empty')
    .test(
      'maxDigitsAfterDecimal',
      'number field must have 1 digits after decimal or less',
      (number: any) => decimalPlaceRegex.test(number)
    ),
  length: number().max(15).nullable().test(
    'maxDigitsAfterDecimal',
    'number field must have 1 digits after decimal or less',
    (number: any) => decimalPlaceRegex.test(number)
  ),
  stock: number(),
  //weightUnit: string(),
  size: Yup.array(),
  colors: string(),
});
