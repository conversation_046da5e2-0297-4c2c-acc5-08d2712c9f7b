import { FC } from 'react';

import SingleView from '@/components/products/singleView';
import { Product } from 'models';
import moment from 'moment';

const ProductInfoCard: FC<{
  product: Product;
}> = (props: { product: Product }) => {
  const { product } = props;

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="product-info"
        id="product-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <div className="card-title row align-items-center ps-2 pt-2">
            <i
              className="bi bi-info-lg col-1 align-text-top"
              style={{ fontSize: '25px' }}
            />
            <div className="fs-5 col text-start px-3">Product info</div>
          </div>
        </div>
        <div className="" id="prouctInfoTab">
          <div className="card-body">
            <SingleView label="Product name" value={product?.info?.name} />
            <SingleView
              label="Short description"
              value={product?.info?.shortDescription}
            />
            <SingleView
              label="Full description"
              value={product?.info?.fullDescription}
            />
            <SingleView label="SKU" value={product?.info?.sku} />
            <SingleView label="Price" value={product?.info?.price.toFixed(2)} />
            <SingleView
              label="Old price"
              value={product?.info?.oldPrice.toFixed(2)}
            />
            <SingleView label="Height" value={product?.info?.height} />
            <SingleView label="Width" value={product?.info?.width} />
            <SingleView label="Length" value={product?.info?.length} />
            <SingleView label="Weight" value={product?.info?.weight} />
            <SingleView label="Weight Unit" value={product?.info?.weightUnit} />
            <SingleView label="Available Sizes" value={product?.info?.size} />
            {/* <SingleView label="Available Colors" value={product?.info?.color} /> */}
            <div className="form-group row my-2">
              <div className="col-md-4">
                <div className="label-wrapper row row-cols-auto float-md-end">
                  <label
                    className="col-form-label col pe-2 fw-bold"
                    htmlFor="colors"
                  >
                    Available Colors
                  </label>
                </div>
              </div>
              <div className="col-md-8 ps-4 py-auto my-auto">
                <div className="text-break">
                  {Array.isArray(product?.info?.color) && (
                    <>
                      {product?.info?.color![0] ? (
                        <button
                          className="p-2 border border-none rounded-circle"
                          style={{ backgroundColor: product?.info?.color![0] }}
                        ></button>
                      ) : (
                        '---'
                      )}
                      {product?.info?.color!.map((data, index) =>
                        index > 0 ? (
                          <>
                            <button
                              className="p-2 m-2 border border-none rounded-circle"
                              style={{
                                backgroundColor: data,
                              }}
                            ></button>
                          </>
                        ) : (
                          ''
                        )
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
            <SingleView
              label="Availability"
              value={
                product?.info?.stock! > 0
                  ? product?.info?.stock! > 1
                    ? product?.info?.stock + ' ' + 'pieces'
                    : '1 piece'
                  : 'Stock Out'
              }
            />

            {/* <SingleView label="Product cost" value={product?.info?.cost} /> */}
            <SingleView
              label="Publish Date"
              value={moment(product?.info?.publishDate)
                .utc()
                .local()
                .format('llll')}
            />
            <SingleView label="Tags" value={product?.tags} />
            <SingleView label="Brands" value={product?.brands} />
          </div>
        </div>
      </div>
    </>
  );
};

export default ProductInfoCard;
