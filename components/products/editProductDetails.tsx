import { Form, Formik } from 'formik';
import { useRouter } from 'next/router';
import { FC, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import { userAPI } from '@/APIs';
import {
  Manufacturer,
  NestedCategoryList,
  ProductManufacturer,
  SubCategoryList,
  UpdateProductCategory,
  UpdateProductRequest,
} from 'models';

import CategorySection from '@/components/products/forms/categorySection';
import ProductManufacturers from '@/components/products/forms/manufacturerForm';
import MetaForm from '@/components/products/forms/metaForm';
import PhotosForm from '@/components/products/forms/photosForm';
import ProductInfoForm from '@/components/products/forms/productInfoForm';
import {
  CategoryInterface,
  EditProductInterface,
} from '@/components/products/models/index';
import Link from 'next/link';
import { editProductSchema } from './schema/productSchema/editProduct.schema';
import { useDispatch } from 'react-redux';
import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { enableEdit, showModal } from '@/toolkit/modalSlice';

const EditProduct: FC<EditProductInterface> = (props: EditProductInterface) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const edit = useAppSelector((state) => state.persistedReducer.modal.edit);
  const [product, setProduct] = useState(props.product);
  const [colors, setColors] = useState<string[]>(
    props.product?.info?.color ? props.product?.info?.color : []
  );
  const [photosUrls, setPhotosUrls] = useState<string[]>([]);

  const [customCategoryData, setCustomCategoryData] = useState<
    CategoryInterface[]
  >([]);

  const [categogiesFullList, setCategoryFullList] =
    useState<NestedCategoryList[]>(); // what we get from get category api
  const [manufacturerData, setManufacturerData] = useState<
    ProductManufacturer[]
  >([]);

  const [updateData, setUpdateData] = useState<UpdateProductRequest>();

  const handleSubmit = async (data: UpdateProductRequest) => {
    try {
      const id = product.id!;

      //if (data.photos![0].url === product.photos![0].url) delete data.photos;

      if (data?.categories![0]) {
        if (data.photos![0]) {
          const response = await userAPI.updateProduct(data, id, router);
          if ('data' in response!) {
            router.push('/Product');
            toast.success('Edit Successful');
          } else {
            toast.error(response?.error.message);
          }
        } else toast.error('Upload at least one photo');
        // if (!('data' in response!)) {
        //   console.log(response);
        // }
      } else toast.error('You must select at least one category');
    } catch (error) {}
  };

  async function loadAllManufacturers() {
    const response = await userAPI.getAllManufacturers();
    const allManufacturers: ProductManufacturer[] = [];
    if (response?.data.manufacturers.length! > 0) {
      response?.data.manufacturers.forEach((manufacturer: Manufacturer) => {
        allManufacturers.push({
          id: manufacturer.id!,
          name: manufacturer.name,
        });
      });
      setManufacturerData(allManufacturers);
    }
  }
  const checkCat = (
    cat: NestedCategoryList,
    tempCat: UpdateProductCategory[]
  ) => {
    customCategoryData.filter((cat2) => {
      cat.id === cat2.id && cat2.isSelected
        ? cat.subCategories?.map((subCat: NestedCategoryList) =>
            checkCat(subCat, tempCat)
          )
        : '';
      cat.id === cat2.id &&
        cat2.isSelected &&
        tempCat.push({ id: cat.id, name: cat.name });
    });
  };

  const printCat = () => {
    const tempCat: UpdateProductCategory[] = [];
    categogiesFullList?.map((cat) => checkCat(cat, tempCat));
    return tempCat;
  };

  const addSubCategories = (subCategories: SubCategoryList[]) => {
    const categoryList: CategoryInterface[] = [];
    subCategories.forEach((category) => {
      categoryList.push({
        id: category.id,
        name: category.name,
        isSelected: false,
      });
      if (category.subCategories && category.subCategories.length > 0) {
        const subCategoryList = addSubCategories(category.subCategories);
        categoryList.push(...subCategoryList);
      }
    });

    return categoryList;
  };

  async function loadCategories() {
    const response = await userAPI.getCategoryList();
    if (response?.data.categories.length! > 0) {
      setCategoryFullList(
        response?.data?.categories ? response.data.categories : []
      );
      const categories: CategoryInterface[] = [];
      response?.data.categories.forEach((category: NestedCategoryList) => {
        categories.push({
          id: category.id,
          name: category.name,
          isSelected: false,
        });
        if (category.subCategories && category.subCategories.length > 0) {
          const subCategoryList = addSubCategories(category.subCategories);
          categories.push(...subCategoryList);
        }
      });

      categories.map((category: CategoryInterface) => {
        product?.categories?.map((productCategory) => {
          category.id === productCategory.id
            ? (category.isSelected = true)
            : '';
        });
      });
      setCustomCategoryData(categories);
    }
  }

  const addCategory = (catID: string) => {
    customCategoryData.map((category: CategoryInterface) => {
      category.id == catID ? (category.isSelected = true) : '';
    });
    setCustomCategoryData([...customCategoryData]);
  };
  const removeCategory = (catID: string) => {
    customCategoryData.map((category: CategoryInterface) => {
      category.id == catID ? (category.isSelected = false) : '';
    });
    setCustomCategoryData([...customCategoryData]);
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  useEffect(() => {
    if (customCategoryData.length === 0) loadCategories();
    if (manufacturerData.length === 0) loadAllManufacturers();
  });

  useEffect(() => {
    let list = [] as string[];
    product?.photos!.map((photo) => {
      list.push(photo?.url!);
    });
    setPhotosUrls(list);
  }, []);

  if (edit === true) {
    handleSubmit(updateData!);
    dispatch(enableEdit(false));
  }

  return (
    <>
      {product ? (
        <Formik
          initialValues={{
            productName: product?.info?.name,
            ShortDescription: product?.info?.shortDescription,
            FullDescription: product?.info?.fullDescription,
            Sku: product?.info?.sku,
            OldPrice: product?.info?.oldPrice,
            Price: product?.info?.price,
            //ProductCost: product?.info?.cost,
            showOnHomePage: product?.info?.showOnHomePage,
            includeInTopMenu: product?.info?.includeInTopMenu,
            allowToSelectPageSize: product?.info?.allowToSelectPageSize,
            published: product?.info?.published,
            displayOrder: product?.info?.displayOrder,
            isFeatured: product?.info?.isFeatured,
            tags: product?.tags || [],
            brands: product?.brands,
            keywords: product?.meta?.keywords?.join(' '),
            metaTitle: product?.meta?.title,
            metaDescription: product?.meta?.description,
            metaFriendlyPageName: product?.meta?.friendlyPageName,
            photosUrl: product?.photos ? product?.photos[0]?.url! : '',
            photosID: product?.photos ? product?.photos[0]?.id : '',
            photosTitle: product?.photos ? product?.photos[0]?.title : '',
            displayOrderPhotos: product?.photos
              ? product?.photos[0]?.displayOrder
              : '',
            manufacturerId: product?.manufacturer?.id,
            manufacturerName: product?.manufacturer?.name,
            height: product?.info?.height,
            width: product?.info?.width,
            length: product?.info?.length,
            weight: product?.info?.weight,
            //weightUnit: product?.info?.weightUnit,
            stock: product?.info?.stock,
            size: product?.info?.size ? product?.info?.size : [],
            color: product?.info?.color ? product?.info?.color : [],
          }}
          onSubmit={(values, actions) => {
            const info = {
              name: values?.productName,
              shortDescription: values?.ShortDescription,
              fullDescription: values?.FullDescription,
              sku: values?.Sku,
              price: values?.Price,
              oldPrice: values?.OldPrice,
              //cost: values?.ProductCost,
              showOnHomePage: values?.showOnHomePage,
              includeInTopMenu: values?.includeInTopMenu,
              allowToSelectPageSize: values?.allowToSelectPageSize,
              published: values?.published,
              displayOrder: +values?.displayOrder!,
              isFeatured: values?.isFeatured,
              height: values?.height,
              width: values.width,
              length: values.length,
              weight: values.weight,
              //weightUnit: values.weightUnit,
              stock: values.stock,
              size: values.size,
              color: colors,
            };
            const meta = {
              keywords: values?.keywords?.split(' '),
              title: values?.metaTitle,
              description: values?.metaDescription,
              friendlyPageName: values?.metaFriendlyPageName,
            };
            let photos: any = [];
            photosUrls.map((url) => {
              photos.push({
                url: url,
                id: values?.photosID,
                title: values?.photosTitle,
                displayOrder: +`${values?.displayOrderPhotos}`,
                alt: 'product image',
              });
            });
            const manufacturer = {
              id: '',
              name: values.manufacturerName,
            };
            const newData = {
              info: info,
              meta: meta,
              tags: values.tags,
              photos,
              brands: values.brands,
              manufacturer: manufacturer,
              categories: printCat(),
            };
            dispatch(showModal(true));
            setUpdateData(newData);
            actions.setSubmitting(false);
          }}
          validationSchema={editProductSchema}
        >
          {(formikprops) => {
            return (
              <Form
                onSubmit={formikprops.handleSubmit}
                onKeyDown={onKeyDown}
                className="min-vh-100"
              >
                <div className="content-header clearfix pt-4">
                  <h1 className="float-start fs-2">
                    Edit product details
                    <span className="fs-5 p-3">
                      <Link href="/Product" className="text-decoration-none ">
                        <i className="bi bi-arrow-left-circle-fill p-2" />
                        Back to product list
                      </Link>
                    </span>
                  </h1>
                  <div className="float-end">
                    <button
                      type="submit"
                      name="save"
                      className="btn btn-primary m-1"
                    >
                      {/* <i className="bi bi-save" /> */}
                      <p className="float-end mx-1 my-0">Save </p>
                    </button>
                  </div>
                </div>
                <div className="mt-4 pb-5">
                  <ProductInfoForm
                    setColors={setColors}
                    colors={colors}
                    setFieldValue={formikprops.setFieldValue}
                  />
                  <MetaForm />
                  <PhotosForm
                    setPhotosUrls={setPhotosUrls}
                    photosUrls={photosUrls}
                    setFieldValue={formikprops.setFieldValue}
                    product={product}
                    edit={true}
                  />
                  <ProductManufacturers manufacturerData={manufacturerData} />
                  {/* <CategoryForm
                  setCustomCategoryData={setCustomCategoryData}
                  categoryData={customCategoryData}
                  setFieldValue={formikprops.setFieldValue}
                /> */}
                  <CategorySection
                    categoryData={customCategoryData}
                    categogiesFullList={categogiesFullList!}
                    removeCategory={removeCategory}
                    addCategory={addCategory}
                  />
                </div>
              </Form>
            );
          }}
        </Formik>
      ) : (
        'Something went wrong!'
      )}
    </>
  );
};

export default EditProduct;
