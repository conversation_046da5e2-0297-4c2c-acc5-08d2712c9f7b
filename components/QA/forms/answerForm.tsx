import { ErrorMessage, Field } from 'formik';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';

const Answer: FC = () => {
  const router = useRouter();
  const questionId = '' + `${router.query.questionId}`;
  const productId = '' + `${router.query.productId}`;
  const question = '' + `${router.query.question}`;
  const [btnToggler, setBtnToggler] = useState('bi-dash');

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="answers"
        data-hideattribute="ProductPage.HideInfoBlock"
        id="answers"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#answerTab"
            aria-expanded="true"
            aria-controls="answerTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-info-lg col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col px-3 text-start">Answer Tab</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="answerTab">
          <div className="card-body">
            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                  <p className="col-form-label col fs-5 px-1 text-center">
                    Question:
                  </p>
                </div>
              </div>
              <div className="col-md-9 border-bottom rounded-0 border border-0 border-2 p-2 shadow-none">
                {question}
              </div>
            </div>

            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                  <p className="col-form-label col fs-5 px-1 text-center">
                    Product Id:
                  </p>
                </div>
              </div>
              <div className="col-md-9 border-bottom rounded-0 border border-0 border-2 p-2 shadow-none">
                <Link
                  href={{
                    pathname: `/Product/View/[id]`,
                    query: { id: productId },
                  }}
                  passHref
                  legacyBehavior
                >
                  {productId}
                </Link>
              </div>
            </div>

            {/* <FieldTemplate
              label="Answer"
              isRequired={true}
              fieldID="answer"
              fieldType="textarea"
            /> */}
            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                  <label
                    className="col-form-label col fs-5 px-1 text-center"
                    htmlFor="answer"
                  >
                    Answer
                    <span className="required text-danger ">*</span>
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className={`input-group mt-2`}>
                  <Field
                    className="rounded-0 border border-2 p-2 shadow-none w-100"
                    as="textarea"
                    id="answer"
                    name="answer"
                  />
                  <div className="errMsg text-danger text-red-600">
                    <ErrorMessage name="answer" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Answer;
