import { userAPI } from '@/APIs';
import Answer from '@/components/QA/forms/answerForm';
import { answerSchema } from '@/components/QA/schema/answer.schema';
import { Form, Formik } from 'formik';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC } from 'react';
import { toast } from 'react-toastify';

const AnswerForm: FC = () => {
  const router = useRouter();
  const questionId = '' + `${router.query.questionId}`;
  const productId = '' + `${router.query.productId}`;

  const handleSubmit = async (data: string) => {
    try {
      const res = await userAPI.answerQuestion(productId, questionId, data);
      if ('data' in res) {
        router.push('/QA/unanswered-questions');
        toast.success(res.data.message);
      } else {
        toast.error(res.error.message);
      }
    } catch (error: any) {
      toast.error(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          answer: '',
        }}
        onSubmit={(values, actions) => {
          handleSubmit(values.answer);
          actions.setSubmitting(false);
        }}
        validationSchema={answerSchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix"
                style={{ paddingTop: '10px' }}
              >
                <h3 className="float-start">
                  Answer A Question
                  <span className="fs-5 p-3">
                    <Link
                      href="/QA/unanswered-questions"
                      className="text-decoration-none"
                    >
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      <span style={{ fontSize: '14px' }}>
                        Back to unanswered question list
                      </span>
                    </Link>
                  </span>
                </h3>
                <div className="float-end">
                  <button
                    type="submit"
                    name="save"
                    className="btn btn-primary m-1"
                  >
                    {/* <i className="bi-upload"></i> */}
                    <p className="float-end mx-1 my-0">Submit</p>
                  </button>
                </div>
              </div>
              <div className="mt-4">
                <Answer />
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default AnswerForm;
