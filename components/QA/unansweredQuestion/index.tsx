import { FC } from 'react';

import Table from '@/components/global/table/table';
import { config } from 'config';
import myImageLoader from 'image/loader';
import { ProductQuestionsForAdmin } from 'models';
import Image from 'next/image';
import Link from 'next/link';
import { handlePagination } from 'utils/handlePagination';

interface Props {
  questionList: ProductQuestionsForAdmin[];
  setSkip: Function;
  skip: number;
  // showSeeMore: boolean;
  // setLoadData: Function;
  // loadData: boolean;
  // setShowSeeMore: Function;
}

const UnansweredQuestionList: FC<Props> = ({
  questionList,
  setSkip,
  skip,
  // showSeeMore,
  // setLoadData,
  // loadData,
  // setShowSeeMore,
}) => {
  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const columns = [
    // {
    //   label: 'ID',
    //   path: 'id',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="text-center align-middle">{data?.[key]}</td>
    //   ),
    // },
    {
      label: 'Question',
      path: 'question',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.[key]}</td>
      ),
    },
    {
      label: 'Product Name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.productInfo?.info?.[key]}
        </td>
      ),
    },
    {
      label: 'Picture',
      path: 'url',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.productInfo.photos![0][key] ? (
            <Image
              loader={myImageLoader}
              src={`${data?.productInfo.photos![0][key]}`}
              height={75}
              width={75}
              alt={`${data?.productInfo.photos![0].alt}`}
            />
          ) : (
            'No Image Found'
          )}
        </td>
      ),
    },
    {
      label: 'Product name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.productInfo.info[key]}
        </td>
      ),
    },
    {
      label: 'Price',
      path: 'price',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.productInfo.info[key]}
        </td>
      ),
    },
    {
      label: 'User Asked',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.userInfo[key]}</td>
      ),
    },
    {
      label: 'Answer',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/QA/answer/[id]`,
              query: {
                id: data?.[key],
                questionId: data?.[key],
                productId: data?.productId,
                question: data?.question,
              },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              Answer
            </button>
          </Link>
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          {questionList?.length > 0 ? (
            <>
              <Table
                items={questionList}
                columns={columns}
                onClickForSort={onClickForSort}
              />
            </>
          ) : (
            'No question to show'
          )}
        </div>
      </div>
    </>
  );
};

export default UnansweredQuestionList;
