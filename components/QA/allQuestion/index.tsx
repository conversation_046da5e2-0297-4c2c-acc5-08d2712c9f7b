import { FC } from 'react';

import Table from '@/components/global/table/table';
import { config } from 'config';
import { ProductQuestionsWithAnswerForAdmin } from 'models';
import { handlePagination } from 'utils/handlePagination';

interface Props {
  questionList: ProductQuestionsWithAnswerForAdmin[];
  setSkip: Function;
  skip: number;
  // showSeeMore: boolean;
  // setLoadData: Function;
  // loadData: boolean;
  // setShowSeeMore: Function;
}

const AllQuestionList: FC<Props> = ({
  questionList,
  setSkip,
  skip,
  // showSeeMore,
  // setLoadData,
  // loadData,
  // setShowSeeMore,
}) => {
  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const columns = [
    // {
    //   label: 'ID',
    //   path: 'id',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="text-center align-middle">{data?.[key]}</td>
    //   ),
    // },
    {
      label: 'Question',
      path: 'question',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.[key]}</td>
      ),
    },
    // {
    //   label: 'Picture',
    //   path: 'url',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="text-center align-middle">
    //       {data?.productInfo.photos![0][key] && (
    //         <Image
    //           loader={myImageLoader}
    //           src={`${data?.productInfo.photos![0][key]}`}
    //           height={75}
    //           width={75}
    //           alt={`${data?.productInfo.photos![0].alt}`}
    //         />
    //       )}
    //     </td>
    //   ),
    // },
    {
      label: 'Product name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.productInfo.info[key]}
        </td>
      ),
    },

    {
      label: 'User Asked',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data?.userInfo[key]}</td>
      ),
    },

    {
      label: 'Answer',
      path: 'answer',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle w-lg-50 w-25 text-wrap">
          {data?.[key] ? data?.[key] : '--'}
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          {questionList?.length > 0 ? (
            <>
              <Table
                items={questionList}
                columns={columns}
                onClickForSort={onClickForSort}
              />
            </>
          ) : (
            'No question to show'
          )}
        </div>
      </div>
    </>
  );
};

export default AllQuestionList;
