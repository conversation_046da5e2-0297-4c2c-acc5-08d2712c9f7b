import { useAppDispatch } from '@/redux-hooks';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import { SignInRequest } from 'models';
import { useRouter } from 'next/router';

import { userAPI } from '@/APIs';
import { loginSchema } from '@/components/global/schemas/loginSchema';
import { removeToken, saveToken } from '@/toolkit/AuthSlice';
import { useEffect } from 'react';
import { toast } from 'react-toastify';

const Login: React.FC = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  async function handleLogin(data: SignInRequest) {
    try {
      const response = await userAPI.signin(data);
      if ('data' in response) {
        dispatch(saveToken(response?.data.token!));
        router.push('/');
        toast.success('Successfully signed in!');
      } else {
        toast.error(response.error.message);
      }
    } catch (error) {}
  }

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('persist:root');
    dispatch(removeToken);
  };

  useEffect(() => {
    handleLogout();
  }, []);

  return (
    <div className="d-flex flex-column align-items-center container">
      <span className="w-100 border-bottom border-1 flex-grow-1 h1 mb-3 pb-3 text-center mt-5">
        Fitsomnia Admin Panel
      </span>
      <span className="w-100 border-bottom border-1 flex-grow-1 h5 text-muted mb-0 pb-3 text-center mb-3">
        Welcome, please sign in!
      </span>
      {/* input form */}
      <Formik
        initialValues={{
          username: '',
          password: '',
        }}
        onSubmit={(values, actions) => {
          const data = {
            username: values.username,
            password: values.password,
          };
          handleLogin(data);
          actions.setSubmitting(false);
        }}
        validationSchema={loginSchema}
      >
        {(formikprops) => {
          return (
            <Form
              onSubmit={formikprops.handleSubmit}
              onKeyDown={onKeyDown}
              className="w-50 d-flex flex-column align-items-center"
            >
              <div
                className="w-100 d-flex flex-column align-items-center pt-4"
                style={{ backgroundColor: '#f9f9f9' }}
              >
                <div className="form-group row mb-lg-3 w-50">
                  <label
                    htmlFor="email"
                    className="col-lg-3 col-form-label text-lg-end text-center me-2"
                  >
                    Email:
                  </label>
                  <div className="col-lg-8">
                    <Field
                      type="email"
                      className="form-control "
                      id="email"
                      name="username"
                      placeholder="Email"
                    />
                    <div className="text-danger">
                      <ErrorMessage name="email" />
                    </div>
                  </div>
                </div>
                <div className="form-group row mb-lg-3 w-50">
                  <label
                    htmlFor="password"
                    className="col-lg-3 col-form-label text-lg-end text-center me-2"
                  >
                    Password:
                  </label>
                  <div className="col-lg-8">
                    <Field
                      type="password"
                      className="form-control "
                      id="password"
                      name="password"
                      placeholder="Password"
                    />
                    <div className="text-danger">
                      <ErrorMessage name="password" />
                    </div>
                  </div>
                </div>
                <div className="form-check mb-3">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    value=""
                    id="remember"
                  />
                  <label className="form-check-label" htmlFor="remember">
                    Remember me?
                  </label>
                </div>
                <button
                  type="submit"
                  className="btn btn-primary mb-4 px-5 py-1"
                >
                  {/* <Link href={""}> */}
                  <a className="text-decoration-none text-white">LOG IN</a>
                  {/* </Link> */}
                </button>
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};
export default Login;
