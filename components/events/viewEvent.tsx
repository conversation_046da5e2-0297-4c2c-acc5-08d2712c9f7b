import { saveAs } from 'file-saver';
import myImageLoader from 'image/loader';
import { Event, EventUserDetails } from 'models';
import Image from 'next/image';
import Link from 'next/link';
import { QRCodeCanvas } from 'qrcode.react';
import { FC } from 'react';
import * as XLSX from 'xlsx';

const ViewEvent: FC<{ event: Event; userDetails: EventUserDetails[] }> = ({
  event,
  userDetails,
}) => {
  const downloadQRCode = () => {
    const canvas = document.querySelector(
      '#qrcode-canvas'
    ) as HTMLCanvasElement;
    if (!canvas) throw new Error('<canvas> not found in the DOM');
    const pngUrl = canvas
      .toDataURL('image/png')
      .replace('image/png', 'image/octet-stream');
    const downloadLink = document.createElement('a');
    downloadLink.href = pngUrl;
    downloadLink.download = 'QR-code.png';
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
  };

  const exportToExcel = () => {
    // Format user details for export
    const exportData = userDetails.map((user) => ({
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      session: user.session ?? '',
      bkashNumber: user.bkashNumber ?? '',
      trxId: user.trxId ?? '',
      studentId: user.studentId ?? '',
      jerseySize: user.jerseySize ?? '',
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'User Details');

    // Generate Excel file
    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
    });
    const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });
    saveAs(blob, `${event.name}-user-details.xlsx`);
  };

  return (
    <>
      {event ? (
        <div>
          <div className="content-header clearfix mt-3">
            <h1 className="float-start fs-2">
              View event details
              <span className="fs-5 p-3">
                <Link href="/event" className="text-decoration-none">
                  <i className="bi bi-arrow-left-circle-fill p-2" />
                  Back to event list
                </Link>
              </span>
            </h1>
            {userDetails && userDetails.length > 0 && (
              <div className="float-end">
                <button className="btn btn-success" onClick={exportToExcel}>
                  <i className="bi bi-file-earmark-excel me-2"></i>
                  Export User Details
                </button>
              </div>
            )}
          </div>
          <div className="mt-4">
            <div className="card">
              <div className="card-body">
                <h5 className="card-title mb-4">{event.name}</h5>
                <div className="row">
                  <div className="col-md-6">
                    <p>
                      <strong>Description:</strong> {event.description}
                    </p>
                    <p>
                      <strong>Start Date:</strong>{' '}
                      {new Date(event.startDate).toLocaleString()}
                    </p>
                    <p>
                      <strong>End Date:</strong>{' '}
                      {new Date(event.endDate).toLocaleString()}
                    </p>
                    <p>
                      <strong>Registration Fee:</strong> {event.registrationFee}{' '}
                      {event.amountInCurrency}
                    </p>
                    <p>
                      <strong>Location:</strong> {event.locationTitle}
                    </p>
                    <p>
                      <strong>Actual Location:</strong> {event.actualLocation}
                    </p>
                    <p>
                      <strong>Registration Status:</strong>{' '}
                      {event.isRegistered ? 'Registered' : 'Not Registered'}
                    </p>
                    <p>
                      <strong>Registration Email Subject:</strong>{' '}
                      {event.registrationEmailSubject}
                    </p>
                    <p>
                      <strong>Registration Email Body:</strong>{' '}
                      <div className="border rounded p-2 bg-light">
                        {event.registrationEmailBody}
                      </div>
                    </p>
                    <p>
                      <strong>Unregistration Email Subject:</strong>{' '}
                      {event.unregistrationEmailSubject}
                    </p>
                    <p>
                      <strong>Unregistration Email Body:</strong>{' '}
                      <div className="border rounded p-2 bg-light">
                        {event.unregistrationEmailBody}
                      </div>
                    </p>
                  </div>
                  <div className="col-md-6">
                    <h6 className="mb-3">Event Media</h6>
                    <div className="row g-2">
                      {event.media && event.media.length > 0 ? (
                        event.media.map((mediaUrl, index) => (
                          <div key={index} className="col-6">
                            <Image
                              loader={myImageLoader}
                              src={mediaUrl}
                              alt={`Event media ${index + 1}`}
                              width={200}
                              height={200}
                              className="img-fluid rounded"
                              style={{ objectFit: 'cover' }}
                            />
                          </div>
                        ))
                      ) : (
                        <div className="col-12">No media available</div>
                      )}
                    </div>
                    <div className="mt-4">
                      <strong>Event QR Code:</strong>
                      <div className="row g-5">
                        <div className="col-12">
                          <div className="d-flex flex-column align-items-center">
                            <QRCodeCanvas
                              id="qrcode-canvas"
                              value={`https://fitsomnia.com/events/${event.id}`}
                              size={200}
                              level="H"
                            />
                            <button
                              className="btn btn-primary mt-3"
                              onClick={downloadQRCode}
                            >
                              <i className="bi bi-download me-2"></i>
                              Download QR Code
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        'No event data available'
      )}
    </>
  );
};

export default ViewEvent;
