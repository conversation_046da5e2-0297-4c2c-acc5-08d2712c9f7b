import Link from 'next/link';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import Table from '@/components/global/table/table';
import { config } from 'config';
import { Event } from 'models';
import moment from 'moment';
import { toast } from 'react-toastify';

interface Props {
  eventList: Event[];
  setEvents: Function;
  setSkip: Function;
  skip: number;
}

const EventList: FC<Props> = ({ eventList, setEvents, setSkip, skip }) => {
  const [eventId, setEventId] = useState('');
  const [modal, setModal] = useState({
    delete: false,
  });

  const onChangeForList = async (skip: number) => {
    try {
      const eventList = await userAPI.getMultipleEvents(skip, config.limit);
      if ('data' in eventList) {
        setEvents(eventList.data);
      } else {
        toast.error(eventList.error.message);
      }
    } catch (error) {}
    setSkip(0);
  };

  const deleteEvent = async () => {
    try {
      const res = await userAPI.deleteEvent(eventId);
      if ('data' in res) {
        toast.success(res.data.message);
        onChangeForList(0);
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
    setModal({
      ...modal,
      delete: false,
    });
  };

  const onClickForDelete = (id: string) => {
    setEventId(id);
    setModal({ ...modal, delete: true });
  };

  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const columns = [
    {
      label: 'Name',
      path: 'name',
      content: (data: Event) => (
        <td className="text-center align-middle">{data?.name || ''}</td>
      ),
    },
    {
      label: 'Description',
      path: 'description',
      type: 'textarea',
      content: (data: Event) => (
        <td className="text-center align-middle">{data?.description || ''}</td>
      ),
    },
    {
      label: 'Location',
      path: 'locationTitle',
      content: (data: Event) => (
        <td className="text-center align-middle">
          {data?.locationTitle || ''}
        </td>
      ),
    },
    {
      label: 'Start Date',
      path: 'startDate',
      content: (data: Event, key: any) => (
        <td className="text-center align-middle">
          {moment(data.startDate).utc().local().format('llll')}
        </td>
      ),
    },
    {
      label: 'Edit',
      path: 'id',
      content: (data: Event) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/event/edit/[id]`,
              query: { id: data.id },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-info">
              <span>
                <i className="bi bi-pencil me-2 align-middle"></i>
              </span>
              Edit
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'View',
      path: 'id',
      content: (data: Event) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/event/view/[id]`,
              query: { id: data.id },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Delete',
      path: 'id',
      content: (data: Event) => (
        <td className="text-center align-middle">
          <button
            className="btn btn-default btn-outline-danger"
            onClick={() => onClickForDelete(data.id)}
          >
            <i className="bi bi-trash3-fill me-2 align-middle"></i>
            Delete
          </button>
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={eventList}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>
      {modal.delete ? (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this event?</p>
                <br />
                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{ marginRight: '10px' }}
                    onClick={() => setModal({ ...modal, delete: false })}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => deleteEvent()}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default EventList;
