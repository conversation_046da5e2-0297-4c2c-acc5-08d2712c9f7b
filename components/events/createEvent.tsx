import { user<PERSON><PERSON> } from '@/APIs';
import { useAppSelector } from '@/redux-hooks';
import { Form, Formik } from 'formik';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';
import { toast } from 'react-toastify';
import { handleMediaUpload } from 'utils/handleMediaUpload';
import EventInfo from './forms/EventInfo';
import { createEventSchema } from './schemas/createEventSchema';

export interface Media {
  url: string;
  name: string;
  file: File;
}

const CreateEvent: FC = () => {
  const router = useRouter();
  const token = useAppSelector((state) => state.persistedReducer.auth.token);
  const [media, setMedia] = useState<Media[]>([]);

  const handleSubmit = async (values: any) => {
    try {
      let mediaUrls: string[] = [];

      if (media && media.length > 0) {
        // Upload all files concurrently but wait for all to complete
        mediaUrls = await Promise.all(
          media.map(async (item) => {
            const fileData = {
              featureName: 'event',
              filenames: [item.name],
            };

            const uploadedUrl = await handleMediaUpload(
              fileData,
              item.file,
              token,
              true
            );
            return uploadedUrl?.toString() as string;
          })
        );
        // Filter out any undefined values
        mediaUrls = mediaUrls.filter((url) => url);
      }

      const eventData = {
        name: values.name,
        description: values.description,
        media: mediaUrls,
        startDate: values.startDate,
        endDate: values.endDate,
        registrationFee: values.registrationFee,
        amountInCurrency: 'BDT',
        locationTitle: values.locationTitle,
        actualLocation: values.actualLocation,
        registrationStartTime: values.registrationStartTime,
        registrationEndTime: values.registrationEndTime,
        registrationEmailSubject: values.registrationEmailSubject,
        registrationEmailBody: values.registrationEmailBody,
        unregistrationEmailSubject: values.unregistrationEmailSubject,
        unregistrationEmailBody: values.unregistrationEmailBody,
      };

      const res = await userAPI.createEvent(eventData);
      if (res.data) {
        toast.success('Event Created Successfully');
        router.push('/event');
      } else {
        toast.error('Failed to Create Event');
      }
    } catch (error: any) {
      toast.error(error.message || 'Error creating event');
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          name: '',
          description: '',
          files: [],
          startDate: new Date(),
          endDate: new Date(),
          registrationFee: '',
          amountInCurrency: 'BDT',
          locationTitle: '',
          actualLocation: '',
          registrationStartTime: new Date(),
          registrationEndTime: new Date(),
          registrationEmailSubject: null,
          registrationEmailBody: null,
          unregistrationEmailSubject: null,
          unregistrationEmailBody: null,
        }}
        validationSchema={createEventSchema}
        onSubmit={(values, actions) => {
          handleSubmit(values);
          actions.setSubmitting(false);
        }}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix"
                style={{ paddingTop: '10px' }}
              >
                <h3 className="float-start">
                  Create An Event
                  <span className="fs-5 p-3">
                    <Link href="/event" className="text-decoration-none">
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      <span style={{ fontSize: '14px' }}>
                        Back to Event List
                      </span>
                    </Link>
                  </span>
                </h3>
                <div className="float-end">
                  <button
                    type="submit"
                    name="save"
                    className="btn btn-primary m-1"
                  >
                    {/* <i className="bi bi-save" /> */}
                    <p className="float-end mx-1 my-0">Save</p>
                  </button>
                </div>
              </div>
              <div className="mt-4">
                <EventInfo
                  setFieldValue={formikprops.setFieldValue}
                  setMedia={setMedia}
                  media={media}
                />
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default CreateEvent;
