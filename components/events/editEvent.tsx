import { userAPI } from '@/APIs';
import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { enableEdit, showModal } from '@/toolkit/modalSlice';
import { Form, Formik } from 'formik';
import { Event } from 'models';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';
import { toast } from 'react-toastify';
import { handleMediaUpload } from 'utils/handleMediaUpload';
import { Media } from './createEvent';
import EventInfo from './forms/EventInfo';
import { createEventSchema } from './schemas';

interface Props {
  eventToEdit: Event;
}

const EditEvent: FC<Props> = ({ eventToEdit }) => {
  const router = useRouter();
  const token = useAppSelector((state) => state.persistedReducer.auth.token);
  const dispatch = useAppDispatch();
  const edit = useAppSelector((state) => state.persistedReducer.modal.edit);
  const [updateData, setUpdateData] = useState<any>();
  const [event, setEvent] = useState(eventToEdit);
  const [media, setMedia] = useState<Media[]>([]);

  const handleSubmit = async (data: Event) => {
    try {
      let mediaUrls = [...(event.media || [])];
      // Handle new media uploads
      if (media && media.length > 0) {
        // Upload all files concurrently but wait for all to complete
        let newMediaUrls = await Promise.all(
          media.map(async (item) => {
            const fileData = {
              featureName: 'event',
              filenames: [item.name],
            };
            const uploadedUrl = await handleMediaUpload(
              fileData,
              item.file,
              token,
              true
            );
            return uploadedUrl?.toString() as string;
          })
        );
        newMediaUrls = newMediaUrls.filter((url) => url);
        mediaUrls = [...mediaUrls, ...newMediaUrls];
      }
      data = {
        ...data,
        media: mediaUrls,
      };

      const res = await userAPI.updateEvent(eventToEdit.id, data);

      if (!res.data) {
        toast.error("Can't Update Event");
      } else {
        router.push('/event');
        toast.success('Event Updated Successfully');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to update event');
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  if (edit === true) {
    handleSubmit(updateData!);
    dispatch(enableEdit(false));
  }

  return (
    <>
      {event ? (
        <Formik
          initialValues={{
            name: event.name || '',
            description: event.description || '',
            startDate: event?.startDate
              ? new Date(event.startDate).toISOString().slice(0, 16)
              : '',
            endDate: event?.endDate
              ? new Date(event.endDate).toISOString().slice(0, 16)
              : '',
            registrationFee: event.registrationFee || 0,
            amountInCurrency: event.amountInCurrency || '',
            locationTitle: event.locationTitle || '',
            actualLocation: event.actualLocation || '',
            registrationStartTime: event?.registrationStartTime
              ? new Date(event.registrationStartTime).toISOString().slice(0, 16)
              : '',
            registrationEndTime: event?.registrationEndTime
              ? new Date(event.registrationEndTime).toISOString().slice(0, 16)
              : '',
            registrationEmailSubject: event.registrationEmailSubject || '',
            registrationEmailBody: event.registrationEmailBody || '',
            unregistrationEmailSubject: event.unregistrationEmailSubject || '',
            unregistrationEmailBody: event.unregistrationEmailBody || '',
            files: event.media || [],
          }}
          onSubmit={(values, actions) => {
            const data = {
              ...event,
              name: values.name,
              description: values.description,
              startDate: values.startDate,
              endDate: values.endDate,
              registrationFee: values.registrationFee,
              amountInCurrency: values.amountInCurrency,
              locationTitle: values.locationTitle,
              actualLocation: values.actualLocation,
              registrationStartTime: values.registrationStartTime,
              registrationEndTime: values.registrationEndTime,
              registrationEmailSubject: values.registrationEmailSubject,
              registrationEmailBody: values.registrationEmailBody,
              unregistrationEmailSubject: values.unregistrationEmailSubject,
              unregistrationEmailBody: values.unregistrationEmailBody,
            };
            setUpdateData(data);
            actions.setSubmitting(false);
            dispatch(showModal(true));
          }}
          validationSchema={createEventSchema}
        >
          {(formikprops) => {
            return (
              <Form onKeyDown={onKeyDown} className="min-vh-100">
                <div className="content-header clearfix pt-4">
                  <h1 className="float-start fs-2">
                    Edit Event Details
                    <span className="fs-5 p-3">
                      <Link href="/event" className="text-decoration-none">
                        <i className="bi bi-arrow-left-circle-fill p-2" />
                        Back to event list
                      </Link>
                    </span>
                  </h1>
                  <div className="float-end">
                    <button type="submit" className="btn btn-primary m-1">
                      <p className="float-end mx-1 my-0">Save</p>
                    </button>
                  </div>
                </div>
                <div className="mt-4 pb-5">
                  <EventInfo
                    setFieldValue={formikprops.setFieldValue}
                    setMedia={setMedia}
                    media={media}
                    event={event}
                    edit={true}
                  />
                </div>
              </Form>
            );
          }}
        </Formik>
      ) : (
        'Something went wrong!'
      )}
    </>
  );
};

export default EditEvent;
