import * as Yup from 'yup';

export const createEventSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  description: Yup.string().optional(),
  files: Yup.array().of(Yup.mixed()).optional(),
  startDate: Yup.string().required('Start date is required'),
  endDate: Yup.string().required('End date is required'),
  registrationFee: Yup.number()
    .required('Registration fee is required')
    .min(0, 'Registration fee cannot be negative'),
  amountInCurrency: Yup.string().required('Currency is required'),
  locationTitle: Yup.string().required('Location title is required'),
  actualLocation: Yup.string().required('Actual location is required'),
  registrationStartDate: Yup.string().optional(),
  registrationEndDate: Yup.string().optional(),
  registrationEmailSubject: Yup.string().optional().nullable(),
  registrationEmailBody: Yup.string().optional().nullable(),
  unregistrationEmailSubject: Yup.string().optional().nullable(),
  unregistrationEmailBody: Yup.string().optional().nullable(),
});
