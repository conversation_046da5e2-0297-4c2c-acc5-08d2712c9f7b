import FieldTemplate from '@/components/common/fieldTemplate';
import DOMPurify from 'dompurify';
import { ErrorMessage } from 'formik';
import myImageLoader from 'image/loader';
import { Event } from 'models';
import Image from 'next/image';
import { FC, useState } from 'react';
import { Media } from '../createEvent';

interface Props {
  setFieldValue: Function;
  setMedia: Function;
  media: Media[];
  event?: Event;
  edit?: boolean;
}

const EventInfo: FC<Props> = ({
  setFieldValue,
  setMedia,
  media,
  event,
  edit,
}) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');
  const [file, setFile] = useState<any>({});
  const [registrationEmailPreview, setRegistrationEmailPreview] = useState(
    event?.registrationEmailBody || ''
  );
  const [unregistrationEmailPreview, setUnregistrationEmailPreview] = useState(
    event?.unregistrationEmailBody || ''
  );

  const toggleButton = () => {
    if (btnToggler === 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  const addMedia = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const currentFiles = event.target.form?.files || [];
      const newFiles = Array.from(files);

      newFiles.forEach((file) => {
        const fileUrl = URL.createObjectURL(file);
        setFile((prev: any) => ({
          ...prev,
          [fileUrl]: {
            src: fileUrl,
            type: file.type,
            file: file,
          },
        }));

        const mediaInfo = {
          url: fileUrl,
          name: file.name,
          file: file,
        };
        const updatedMedia = [...media, mediaInfo];
        setMedia(updatedMedia);
        setFieldValue('media', updatedMedia);
      });

      // Update the files in form values
      const existingFiles = event.target.form?.files || [];
      const updatedFiles = [...Array.from(existingFiles), ...newFiles];
      setFieldValue('files', updatedFiles);
    }
  };

  const removeMedia = (url: string) => {
    if (event?.media && event.media.includes(url)) {
      event.media = event.media.filter((item) => item !== url);
    }
    const updatedMedia = media.filter((item) => item.url !== url);
    setMedia(updatedMedia);

    setFieldValue('media', updatedMedia);

    // Remove the file from the files state
    if (file[url]?.file) {
      setFieldValue('files', (currentFiles: File[]) =>
        currentFiles.filter((f) => f !== file[url].file)
      );

      // Clean up the file state
      setFile((prev: any) => {
        const newState = { ...prev };
        delete newState[url];
        return newState;
      });
    }
  };

  const handleRegistrationEmailBodyChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const sanitizedHtml = DOMPurify.sanitize(e.target.value);
    setRegistrationEmailPreview(sanitizedHtml);
    setFieldValue('registrationEmailBody', e.target.value);
  };

  const handleUnregistrationEmailBodyChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const sanitizedHtml = DOMPurify.sanitize(e.target.value);
    setUnregistrationEmailPreview(sanitizedHtml);
    setFieldValue('unregistrationEmailBody', e.target.value);
  };

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="event-info"
        id="event-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#eventInfoTab"
            aria-expanded="true"
            aria-controls="eventInfoTab"
            onClick={toggleButton}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-calendar-event col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col px-3 text-start">Event Info</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="eventInfoTab">
          <div className="card-body">
            <FieldTemplate
              label="Event Name"
              isRequired={true}
              fieldID="name"
              fieldType="text"
            />

            <FieldTemplate
              label="Description"
              fieldID="description"
              fieldType="textarea"
              fieldAs="textarea"
            />

            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end me-3">
                  <label
                    className="col-form-label col fs-5 px-1"
                    htmlFor="media"
                  >
                    Media
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className="input-group mb-2">
                  <input
                    className="form-control rounded-start border-black"
                    type="file"
                    id="media"
                    name="media"
                    onChange={addMedia}
                    accept="image/*,video/*"
                    multiple
                  />
                </div>
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="media" />
                </div>
                <div className="media-preview d-flex flex-wrap gap-2">
                  {media.map((item, index) => (
                    <div
                      key={index}
                      className="position-relative"
                      style={{ width: '150px', height: '150px' }}
                    >
                      {file?.[item.url]?.type?.startsWith('image/') ? (
                        <Image
                          src={item.url}
                          alt="Preview"
                          width={150}
                          height={150}
                          style={{ objectFit: 'cover' }}
                          loader={myImageLoader}
                        />
                      ) : (
                        <video
                          src={item.url}
                          style={{
                            width: '150px',
                            height: '150px',
                            objectFit: 'cover',
                          }}
                          controls
                        />
                      )}
                      <button
                        type="button"
                        className="btn btn-danger btn-sm position-absolute top-0 end-0"
                        onClick={() => removeMedia(item.url)}
                      >
                        <i className="bi bi-x"></i>
                      </button>
                    </div>
                  ))}

                  {edit &&
                    event?.media?.map((item, index) => (
                      <div
                        key={index}
                        className="position-relative"
                        style={{ width: '150px', height: '150px' }}
                      >
                        {item ? (
                          <Image
                            src={item}
                            alt="Preview"
                            width={150}
                            height={150}
                            style={{ objectFit: 'cover' }}
                            loader={myImageLoader}
                          />
                        ) : (
                          <video
                            src={item}
                            style={{
                              width: '150px',
                              height: '150px',
                              objectFit: 'cover',
                            }}
                            controls
                          />
                        )}
                        <button
                          type="button"
                          className="btn btn-danger btn-sm position-absolute top-0 end-0"
                          onClick={() => removeMedia(item)}
                        >
                          <i className="bi bi-x"></i>
                        </button>
                      </div>
                    ))}
                </div>
              </div>
            </div>

            <FieldTemplate
              label="Start Date"
              isRequired={true}
              fieldID="startDate"
              fieldType="datetime-local"
            />
            <FieldTemplate
              label="End Date"
              isRequired={true}
              fieldID="endDate"
              fieldType="datetime-local"
            />

            <FieldTemplate
              label="Registration Start Time"
              isRequired={false}
              fieldID="registrationStartTime"
              fieldType="datetime-local"
            />
            <FieldTemplate
              label="Registration End Time"
              isRequired={false}
              fieldID="registrationEndTime"
              fieldType="datetime-local"
            />

            <FieldTemplate
              label="Registration Fee"
              isRequired={true}
              fieldID="registrationFee"
              fieldType="number"
            />
            <FieldTemplate
              label="Currency"
              isRequired={true}
              fieldID="amountInCurrency"
              fieldType="text"
              placeholder="e.g., BDT, USD, EUR"
            />

            <FieldTemplate
              label="Location Title"
              isRequired={true}
              fieldID="locationTitle"
              fieldType="text"
            />

            <FieldTemplate
              label="Actual Location"
              isRequired={true}
              fieldID="actualLocation"
              fieldType="text"
            />

            <FieldTemplate
              label="Registration Email Subject"
              isRequired={false}
              fieldID="registrationEmailSubject"
              fieldType="text"
              placeholder="Subject line for registration emails"
            />

            <div className="form-group row my-2">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end me-3">
                  <label
                    className="col-form-label col fs-5 px-1"
                    htmlFor="registrationEmailBody"
                  >
                    Registration Email Body
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <textarea
                  className="form-control mb-3"
                  id="emailBody"
                  name="emailBody"
                  rows={10}
                  placeholder="Content of the email to be sent to participants"
                  onChange={handleRegistrationEmailBodyChange}
                  value={event?.registrationEmailBody || ''}
                />
                <div className="errMsg text-danger text-red-600 mb-3">
                  <ErrorMessage name="emailBody" />
                </div>

                <h6 className="mb-2">Registration Email Preview</h6>
                <div className="border p-3">
                  <div
                    dangerouslySetInnerHTML={{
                      __html: registrationEmailPreview,
                    }}
                    className="email-preview"
                  />
                </div>
              </div>
            </div>

            <FieldTemplate
              label="Unregistration Email Subject"
              isRequired={false}
              fieldID="unregistrationEmailSubject"
              fieldType="text"
              placeholder="Subject line for unregistration emails"
            />

            <div className="form-group row my-2">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end me-3">
                  <label
                    className="col-form-label col fs-5 px-1"
                    htmlFor="unregistrationEmailBody"
                  >
                    Unregistration Email Body
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <textarea
                  className="form-control mb-3"
                  id="unregistrationEmailBody"
                  name="unregistrationEmailBody"
                  rows={10}
                  placeholder="Content of the unregistration email to be sent to participants"
                  onChange={handleUnregistrationEmailBodyChange}
                  value={event?.unregistrationEmailBody || ''}
                />
                <div className="errMsg text-danger text-red-600 mb-3">
                  <ErrorMessage name="unregistrationEmailBody" />
                </div>

                <h6 className="mb-2">Unregistration Email Preview</h6>
                <div className="border p-3">
                  <div
                    dangerouslySetInnerHTML={{
                      __html: unregistrationEmailPreview,
                    }}
                    className="email-preview"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EventInfo;
