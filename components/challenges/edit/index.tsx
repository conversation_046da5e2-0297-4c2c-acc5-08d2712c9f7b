import { Form, Formik } from 'formik';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import AdditionalInfo from '@/components/challenges/forms/additionalInfo';
import ExerciseInfo from '@/components/challenges/forms/exerciseInfo';
import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { ICreateBaseChallengeRes } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';
import { handleMediaUpload } from 'utils/handleMediaUpload';
import { enableEdit, showModal } from '@/toolkit/modalSlice';

interface Props {
  challenge: ICreateBaseChallengeRes;
}

const EditChallenge: FC<Props> = ({ challenge }) => {
  const router = useRouter();
  const [equipments, setEquipments] = useState<string[]>(challenge.equipments);
  const token = useAppSelector((state) => state.persistedReducer.auth.token);
  const dispatch = useAppDispatch();
  const edit = useAppSelector((state) => state.persistedReducer.modal.edit);
  const [updateData, setUpdateData] = useState<any>();

  const handleSubmit = async (data: any) => {
    try {
      let attachment;
      if (data.preview.name) {
        const fileData = {
          featureName: 'challenge',
          filenames: [data.preview.name],
        };
        const fileInfo = await handleMediaUpload(
          fileData,
          data.preview,
          token,
          true
        );
        attachment = fileInfo;
      } else {
        attachment = data.preview;
      }

      // if (attachment === challenge.preview) {
      //   attachment = null;
      // }

      let newData;
      if (attachment !== null) {
        newData = {
          ...data,
          preview: attachment,
        };
      } else {
        newData = {
          name: data.name,
          description: data.description,
          mechanics: data.mechanics,
          type: data.type,
          category: data.category,
          forceType: data.forceType.toString(),
          primaryMuscles: data.primaryMuscles,
          secondaryMuscles: data.secondaryMuscles,
          equipments: data.equipments,
          duration: data.duration,
          points: data.points,
          difficulty: data.difficulty,
          loop: data.loop,
        };
      }

      const res = await userAPI.updateChallenge(challenge.id, newData);

      if (!res.data) {
        toast.error("Can't Update Challenge");
      } else {
        router.push('/challenges');
        toast.success('Challenge Updated Successfully');
      }
    } catch (error: any) {
      toast.error(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  if (edit === true) {
    handleSubmit(updateData!);
    dispatch(enableEdit(false));
  }

  return (
    <>
      {challenge ? (
        <Formik
          initialValues={{
            name: challenge.name,
            description: challenge.description,
            mechanics: challenge.mechanics,
            type: challenge.type,
            category: challenge.category || [],
            forceType: challenge.forceType.toString(),
            primaryMuscles: challenge.primaryMuscles || [],
            secondaryMuscles: challenge.secondaryMuscles || [],
            equipments: challenge.equipments || [],
            preview: challenge.preview || null,
            duration: challenge.duration || 30,
            points: challenge.points || 1,
            difficulty: challenge.difficulty,
            loop: challenge.loop || 0,
          }}
          onSubmit={(values, actions) => {
            dispatch(showModal(true));
            setUpdateData(values);
            actions.setSubmitting(false);
          }}
          //validationSchema={updateChallengeSchema}
        >
          {(formikprops) => {
            return (
              <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
                <div
                  className="content-header clearfix"
                  style={{ paddingTop: '10px' }}
                >
                  <h3 className="float-start fs-2">
                    Edit A Challenge
                    <span className="fs-5 p-3">
                      <Link href="/challenges" className="text-decoration-none">
                        <i className="bi bi-arrow-left-circle-fill p-2" />
                        <span style={{ fontSize: '14px' }}>
                          Back to challenge list
                        </span>
                      </Link>
                    </span>
                  </h3>
                  <div className="float-end">
                    <button
                      type="submit"
                      name="save"
                      className="btn btn-primary m-1"
                    >
                      {/* <i className="bi bi-save" /> */}
                      <p className="float-end mx-1 my-0">Save</p>
                    </button>
                  </div>
                </div>
                <div className="mt-4">
                  <ExerciseInfo
                    edit={true}
                    setEquipments={setEquipments}
                    setFieldValue={formikprops.setFieldValue}
                    equipments={equipments}
                    challenge={challenge}
                  />
                  <AdditionalInfo edit={true} />
                </div>
              </Form>
            );
          }}
        </Formik>
      ) : (
        'Something went wrong!'
      )}
    </>
  );
};

export default EditChallenge;
