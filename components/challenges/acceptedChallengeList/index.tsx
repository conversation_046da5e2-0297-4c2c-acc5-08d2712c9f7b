import { FC, useState } from 'react';

import TableBody from '@/components/challenges/acceptedChallengeList/tableBody';
import { config } from 'config';
import { IUserChallengeHistoryRes } from 'models';
import React from 'react';
import { handlePagination } from 'utils/handlePagination';

interface Props {
  acceptedChallenges: IUserChallengeHistoryRes[];
  setAcceptedChallenges: Function;
  getAcceptedChallengeList: Function;
  acceptedChallengeStatus: string;
  setSkip: Function;
  skip: number;
}

const AcceptedChallengeList: FC<Props> = ({
  acceptedChallenges,
  setAcceptedChallenges,
  getAcceptedChallengeList,
  acceptedChallengeStatus,
  setSkip,
  skip,
}) => {
  const [ChallengeId, setChallengeId] = useState('');

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <div
            className="d-flex flex-wrap justify-content-between"
            style={{ overflow: 'auto' }}
          >
            <table className="table table-bordered table-striped">
              <thead className="border">
                <tr>
                  {' '}
                  {/* <th className="text-center p-2">ID</th> */}
                  <th className="text-center p-2">Video</th>
                  <th className="text-center p-2">Exercise</th>
                  <th className="text-center p-2">Duration</th>
                  <th className="text-center p-2">Points</th>
                  <th className="text-center p-2">Current Status</th>
                  <th className="text-center p-2">End Date</th>
                  {acceptedChallengeStatus === 'Pending' && (
                    <th className="text-center p-2">Edit</th>
                  )}
                </tr>
              </thead>
              <tbody>
                {acceptedChallenges.map((data: IUserChallengeHistoryRes) => {
                  return (
                    <React.Fragment key={data.id}>
                      <TableBody
                        challenge={data}
                        getAcceptedChallengeList={getAcceptedChallengeList}
                      />
                    </React.Fragment>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </>
  );
};

export default AcceptedChallengeList;
