import { userAPI } from '@/APIs';
import {
  IUpdateChallengeStatusEnum,
  IUserChallengeHistoryRes,
  IUserChallengeStatusEnum,
} from 'models';
import moment from 'moment';
import { FC, useState } from 'react';
import { toast } from 'react-toastify';

interface Props {
  challenge: IUserChallengeHistoryRes;
  getAcceptedChallengeList: Function;
}

const TableBody: FC<Props> = ({ challenge, getAcceptedChallengeList }) => {
  const [editState, setEditState] = useState<string>('edit');

  const handleUpdate = async () => {
    try {
      const status = (document.getElementById(challenge.id) as HTMLInputElement)
        .value;
      const id = challenge.id;
      const res = await userAPI.editAcceptedChallenge(status, id);
      toast.success('Status Updated');
      setEditState('edit');
      getAcceptedChallengeList(IUserChallengeStatusEnum.PENDING);
    } catch (error) {}
  };
  return (
    <>
      <tr>
        {/* <td className="text-center align-middle">{challenge.id}</td> */}
        <td className="text-center align-middle">
          <div className="embed-responsive embed-responsive-16by9">
            {challenge.videoUrl ? (
              <video
                width="240"
                height="200"
                controls={true}
                className="embed-responsive-item"
              >
                <source src={challenge.videoUrl} type="video/mp4" />
              </video>
            ) : (
              <p>No Video Found</p>
            )}
          </div>
        </td>
        <td className="text-center align-middle">
          {challenge.challengeDetails.name
            ? challenge.challengeDetails.name
            : '--'}
        </td>
        <td className="text-center align-middle">
          {challenge.challengeDetails.duration
            ? challenge.challengeDetails.duration
            : '--'}
        </td>
        <td className="text-center align-middle">
          {challenge.challengeDetails.points
            ? challenge.challengeDetails.points
            : '--'}
        </td>
        <td className="text-center align-middle">
          {editState === 'edit' ? (
            <p
              className={`btn mb-0 pe-none user-select-auto ${
                challenge.lastStatus === IUserChallengeStatusEnum.COMPLETED
                  ? 'btn-outline-success'
                  : challenge.lastStatus ===
                    IUserChallengeStatusEnum.IN_PROGRESS
                  ? 'btn-outline-primary'
                  : challenge.lastStatus === IUserChallengeStatusEnum.EXPIRED
                  ? 'btn-outline-secondary'
                  : challenge.lastStatus === IUserChallengeStatusEnum.DECLINED
                  ? 'btn-outline-danger'
                  : 'btn-outline-warning'
              }`}
            >
              {challenge.lastStatus}
            </p>
          ) : (
            <>
              <select className="form-select text-center" id={challenge.id}>
                <option selected>Select One</option>
                <option value={IUpdateChallengeStatusEnum.COMPLETED}>
                  {IUpdateChallengeStatusEnum.COMPLETED}
                </option>
                <option value={IUpdateChallengeStatusEnum.DECLINED}>
                  {IUpdateChallengeStatusEnum.DECLINED}
                </option>
              </select>
            </>
          )}
        </td>
        <td className="text-center align-middle">
          {moment(challenge.expireAt).utc().local().local().format('llll')}
        </td>
        {challenge.lastStatus === IUserChallengeStatusEnum.PENDING && (
          <td className="text-center align-middle">
            {editState === 'edit' ? (
              <button
                className={`btn btn-default btn-outline-info`}
                onClick={() => {
                  setEditState('update');
                }}
              >
                <>
                  <span>
                    <i className="bi bi-pencil m-2 align-middle"></i>
                  </span>
                  Edit
                </>
              </button>
            ) : (
              <>
                <button
                  className="btn btn-default btn-outline-success m-3"
                  onClick={handleUpdate}
                >
                  <>
                    <span>
                      <i className="bi bi-pencil m-2 align-middle"></i>
                    </span>
                    Update
                  </>
                </button>

                <button
                  className="btn btn-default btn-outline-secondary"
                  onClick={() => {
                    setEditState('edit');
                  }}
                >
                  <>
                    <span>
                      <i className="bi bi-x m-2 align-middle"></i>
                    </span>
                    Cancel
                  </>
                </button>
              </>
            )}
          </td>
        )}
      </tr>
    </>
  );
};

export default TableBody;
