import { userAPI } from '@/APIs';
import AdditionalInfo from '@/components/challenges/forms/additionalInfo';
import ExerciseInfo from '@/components/challenges/forms/exerciseInfo';
import { challengeSchema } from '@/components/challenges/schema';
import { Form, Formik } from 'formik';
import { IChallengeDifficultyEnum, ICreateBaseChallengeReq } from 'models';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';
import { toast } from 'react-toastify';

const CreateChallenge: FC = () => {
  const router = useRouter();
  const [equipments, setEquipments] = useState<string[]>([]);

  const handleSubmit = async (data: ICreateBaseChallengeReq) => {
    try {
      const res = await userAPI.createChallenge(data);
      if (!res.data) {
        toast.error("Can't Create Challenge");
      } else {
        router.push('/challenges');
        toast.success('Challenge Created Successfully');
      }
    } catch (error: any) {
      toast.error(error);
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          exerciseID: '',
          duration: 30,
          points: 1,
          difficulty: '',
          loop: 0,
        }}
        onSubmit={(values, actions) => {
          const data = {
            exerciseId: values.exerciseID,
            additionalInfo: {
              duration: values.duration,
              points: values.points,
              difficulty:
                values.difficulty === IChallengeDifficultyEnum.ADVANCE
                  ? IChallengeDifficultyEnum.ADVANCE
                  : values.difficulty === IChallengeDifficultyEnum.BEGINNER
                  ? IChallengeDifficultyEnum.BEGINNER
                  : IChallengeDifficultyEnum.INTERMEDIATE,
              loop: values.loop,
            },
          };
          handleSubmit(data);
          actions.setSubmitting(false);
        }}
        validationSchema={challengeSchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit} onKeyDown={onKeyDown}>
              <div
                className="content-header clearfix"
                style={{ paddingTop: '10px' }}
              >
                <h3 className="float-start">
                  Create A Challenge
                  <span className="fs-5 p-3">
                    <Link href="/challenges" className="text-decoration-none">
                      <i className="bi bi-arrow-left-circle-fill p-2" />
                      <span style={{ fontSize: '14px' }}>
                        Back to challenge list
                      </span>
                    </Link>
                  </span>
                </h3>
                <div className="float-end">
                  <button
                    type="submit"
                    name="save"
                    className="btn btn-primary m-1"
                  >
                    {/* <i className="bi bi-save" /> */}
                    <p className="float-end mx-1 my-0">Save</p>
                  </button>
                </div>
              </div>
              <div className="mt-4">
                <ExerciseInfo
                  edit={false}
                  setEquipments={setEquipments}
                  setFieldValue={formikprops.setFieldValue}
                  equipments={equipments}
                />
                <AdditionalInfo />
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default CreateChallenge;
