import CustomSelect from '@/components/common/customSelectInput';
import FieldTemplate from '@/components/common/fieldTemplate';
import { IChallengeDifficultyEnum } from 'models';
import { FC, useState } from 'react';

interface Props {
  edit?: boolean;
}

const AdditionalInfo: FC<Props> = ({ edit }) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');
  const [difficultyOptions, setDifficultyOptions] = useState([
    {
      label: IChallengeDifficultyEnum.BEGINNER,
      value: IChallengeDifficultyEnum.BEGINNER,
    },
    {
      label: IChallengeDifficultyEnum.INTERMEDIATE,
      value: IChallengeDifficultyEnum.INTERMEDIATE,
    },
    {
      label: IChallengeDifficultyEnum.ADVANCE,
      value: IChallengeDifficultyEnum.ADVANCE,
    },
  ]);

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };
  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="challenge-additional-info"
        data-hideattribute="ProductPage.HideInfoBlock"
        id="challenge-additional-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#challengeAdditionalInfoTab"
            aria-expanded="false"
            aria-controls="challengeAdditionalInfoTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-info-lg col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col px-3 text-start">Addtional Info</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="challengeAdditionalInfoTab">
          <div className="card-body">
            <FieldTemplate
              label="Difficulty"
              isRequired={!edit ? true : false}
              fieldID="difficulty"
              fieldType="none"
              fieldClass="custom-select w-100"
              options={difficultyOptions}
              component={CustomSelect}
              placeholder="Select Difficulty..."
              ismulti={false}
            />
            <FieldTemplate
              label="Duration"
              isRequired={!edit ? true : false}
              fieldID="duration"
              fieldType="number"
            />
            <FieldTemplate
              label="Points"
              isRequired={!edit ? true : false}
              fieldID="points"
              fieldType="number"
            />
            <FieldTemplate label="Loop" fieldID="loop" fieldType="text" />
          </div>
        </div>
      </div>
    </>
  );
};

export default AdditionalInfo;
