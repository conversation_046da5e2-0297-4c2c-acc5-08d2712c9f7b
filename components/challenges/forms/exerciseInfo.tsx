import { userAPI } from '@/APIs';
import ExerciseList from '@/components/challenges/forms/exerciseTable';
import CustomSelect from '@/components/common/customSelectInput';
import FieldTemplate from '@/components/common/fieldTemplate';
import PrevNextPagination from '@/components/common/newPagination';
import { config } from 'config';
import { ErrorMessage, Field } from 'formik';
import myImageLoader from 'image/loader';
import {
  ICreateBaseChallengeRes,
  ICreateBaseExcercise,
  MuscleGroup,
  TrainingCategory,
} from 'models';
import Image from 'next/image';
import { FC, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

interface Props {
  setFieldValue?: Function;
  setEquipments?: Function;
  equipments?: string[];
  edit?: boolean;
  challenge?: ICreateBaseChallengeRes;
}

interface Options {
  label: string;
  value: string;
}

const ExerciseInfo: FC<Props> = ({
  setFieldValue,
  setEquipments,
  equipments,
  edit,
  challenge,
}) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');
  const [exercises, setExercises] = useState<ICreateBaseExcercise[]>([]);
  const [file, setFile] = useState<any>();
  const [muscleOptions, setMuscleOptions] = useState<Options[]>([]);
  const [categoryOptions, setCategoryOptions] = useState<Options[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);

  const [disableNext, setDisableNext] = useState(false);

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  const addItem = () => {
    const equipmentsList = (
      document.getElementById('equipments') as HTMLInputElement
    ).value;
    if (equipmentsList.length > 0) {
      setFieldValue!('equipments', '');
      setEquipments!([...equipments!, equipmentsList]);
    }
  };

  const removeItem = (equipment: string) => {
    const equipmentsList = equipments?.filter(
      (singleitem) => singleitem != equipment
    );
    setEquipments!(equipmentsList);
  };

  const getAllExercises = async () => {
    try {
      const res = await userAPI.getBaseExercise(
        undefined,
        undefined,
        skip,
        limit
      );
      if (res.data) {
        if (res.data.length === 0) {
          setDisableNext(true);
        } else {
          setDisableNext(false);
          setExercises(res.data);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const getMuscleGroupList = async () => {
    try {
      const res = await userAPI.getMuscleGroup(undefined, 0, 10000);
      const list = res.data as MuscleGroup[];
      const muscleList = list.map((item) => {
        return {
          label: item.name,
          value: item.name,
        };
      });
      setMuscleOptions(muscleList);
    } catch (error) {
      console.log(error);
    }
  };

  const getCategoryList = async () => {
    try {
      const res = await userAPI.getTrainingCategory(undefined, 0, 10000);
      const list = res.data as TrainingCategory[];
      const categories = list.map((item) => {
        return {
          label: item.name,
          value: item.name,
        };
      });
      setCategoryOptions(categories);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getCategoryList();
    getMuscleGroupList();
  }, []);

  useEffect(() => {
    getAllExercises();
  }, [skip]);

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="challenge-exercise-info"
        data-hideattribute="ProductPage.HideInfoBlock"
        id="challenge-exercise-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#challengeExerciseInfoTab"
            aria-expanded="true"
            aria-controls="challengeExerciseInfoTab"
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-info-lg col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col px-3 text-start">Exercise Info</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id="challengeExerciseInfoTab">
          <div className="card-body">
            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                  <label
                    className="col-form-label col fs-5 px-1 text-center"
                    htmlFor="exerciseID"
                  >
                    Select One Exercise
                    {!edit && <span className="required text-danger ">*</span>}
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                {' '}
                <ExerciseList
                  edit={edit}
                  setFieldValue={setFieldValue!}
                  exerciseList={exercises!}
                  setEquipments={setEquipments!}
                  setSkip={setSkip}
                  skip={skip}
                />
                <PrevNextPagination
                  skip={skip}
                  setSkip={setSkip}
                  limit={limit}
                  disableNext={disableNext}
                />
                <div className="errMsg text-danger text-red-600">
                  <ErrorMessage name="exerciseID" />
                </div>
              </div>
            </div>
            {edit && (
              <>
                {' '}
                <FieldTemplate label="Name" fieldID="name" fieldType="text" />
                <FieldTemplate
                  label="Description/Instruction"
                  fieldID="description"
                  fieldType="text"
                />
                <FieldTemplate
                  label="Mechanics"
                  fieldID="mechanics"
                  fieldType="text"
                />
                {/* <FieldTemplate
                  label="Type"
                  isRequired={true}
                  fieldID="type"
                  fieldType="text"
                /> */}
                <FieldTemplate
                  label="Force Type"
                  fieldID="forceType"
                  fieldType="text"
                />
                <FieldTemplate
                  label="Category"
                  fieldID="category"
                  fieldType="none"
                  fieldClass="custom-select w-100"
                  options={categoryOptions}
                  component={CustomSelect}
                  placeholder="Select Category..."
                  ismulti={true}
                />
                <FieldTemplate
                  label="Muscle Group"
                  fieldID="primaryMuscles"
                  fieldType="none"
                  fieldClass="custom-select w-100"
                  options={muscleOptions}
                  component={CustomSelect}
                  placeholder="Select Muscle Group..."
                  ismulti={true}
                />
                <FieldTemplate
                  label="Secondary Muscle Group"
                  fieldID="secondaryMuscles"
                  fieldType="none"
                  fieldClass="custom-select w-100"
                  options={muscleOptions}
                  component={CustomSelect}
                  placeholder="Select Secondary Muscle Group..."
                  ismulti={true}
                />
                <div className="form-group row my-2 mb-3">
                  <div className="col-md-3">
                    <div className="label-wrapper row row-cols-auto float-md-end me-3">
                      <label
                        className="col-form-label col fs-5 px-1"
                        htmlFor="equipments"
                      >
                        Equipments
                      </label>
                    </div>
                  </div>
                  <div className="col-md-9">
                    <div className="input-group mb-2">
                      <Field
                        className="form-control rounded-start border-black"
                        id="equipments"
                        name="equipments"
                      />
                      <span
                        className="btn btn-secondary rounded-end"
                        onClick={addItem}
                      >
                        +
                      </span>
                    </div>
                    <div className="errMsg text-danger text-red-600">
                      <ErrorMessage name="equipments" />
                    </div>
                    <div>
                      <ul>
                        {equipments?.map((item) => (
                          <li
                            key={item}
                            className="border btn m-2 px-3 pe-none"
                          >
                            {item}
                            <span
                              onClick={() => removeItem(item)}
                              className="pe-auto ms-4 text-danger"
                            >
                              x
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
                <div className="form-group row my-2 mb-3">
                  <div className="col-md-3">
                    <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                      <label
                        className="col-form-label col fs-5 px-1 text-center"
                        htmlFor="file"
                      >
                        Upload Attachment
                      </label>
                    </div>
                  </div>
                  <div className="col-md-9">
                    <div className={`input-group mt-2`}>
                      <input
                        id="preview"
                        name="preview"
                        type="file"
                        onChange={(
                          event: React.ChangeEvent<HTMLInputElement>
                        ) => {
                          console.log(event?.target?.files![0]);
                          setFieldValue!('preview', event?.target?.files![0]);
                          setFile({
                            src: URL.createObjectURL(event?.target?.files![0]),
                            type: event?.target?.files![0].type,
                          });
                        }}
                      />
                    </div>
                    <br />
                    {file && (
                      <>
                        {file.type.includes('image') ? (
                          <Image
                            loader={myImageLoader}
                            className="mt-3"
                            src={file.src}
                            height={200}
                            width={200}
                            alt="animation"
                          />
                        ) : (
                          <div className="embed-responsive embed-responsive-16by9">
                            <video
                              width="240"
                              height="200"
                              controls={true}
                              className="embed-responsive-item"
                            >
                              <source src={file.src} type="video/mp4" />
                            </video>
                          </div>
                        )}
                      </>
                    )}

                    {!file &&
                      challenge &&
                      (challenge.preview ? (
                        <div className="embed-responsive embed-responsive-16by9">
                          {/* <video
                            width="240"
                            height="200"
                            controls={true}
                            className="embed-responsive-item"
                          >
                            <source src={challenge.preview} type="video/mp4" />
                          </video> */}
                          <Image
                            loader={myImageLoader}
                            height={100}
                            width={100}
                            src={challenge?.preview!}
                            alt="Exercise Preview"
                          />
                        </div>
                      ) : (
                        'No attachment found'
                      ))}
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ExerciseInfo;
