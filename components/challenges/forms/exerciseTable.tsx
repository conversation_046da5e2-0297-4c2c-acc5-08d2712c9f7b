import { FC } from 'react';

import Table from '@/components/global/table/table';
import { config } from 'config';
import { Field } from 'formik';
import myImageLoader from 'image/loader';
import { ICreateBaseExcercise } from 'models';
import Image from 'next/image';
import { handlePagination } from 'utils/handlePagination';

interface Props {
  exerciseList: ICreateBaseExcercise[];
  setFieldValue: Function;
  setEquipments: Function;
  edit?: boolean;
  setSkip: Function;
  skip: number;
}

const ExerciseList: FC<Props> = ({
  exerciseList,
  setFieldValue,
  setEquipments,
  edit,
  setSkip,
  skip,
}) => {
  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const columns = [
    {
      label: 'Select One',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Field
            type="radio"
            name="exerciseID"
            value={data[key]}
            onClick={() => {
              if (edit) {
                setFieldValue('name', data.name);
                setFieldValue('description', data.description);
                setFieldValue('mechanics', data.mechanics);
                setFieldValue('type', data.type);
                setFieldValue('category', data.category);
                setFieldValue('forceType', data.forceType.toString());
                setFieldValue('primaryMuscles', data.primaryMuscles);
                setFieldValue('equipments', data.equipments);
                setFieldValue('preview', data.previewKey);
                setEquipments(data.equipments);
              }
            }}
          />
        </td>
      ),
    },
    {
      label: 'Attachment',
      path: 'preview',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Image
            loader={myImageLoader}
            height={70}
            width={70}
            src={data[key]}
            alt="Exercise Preview"
          />
        </td>
      ),
    },
    {
      label: 'Name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Categories',
      path: 'category',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.category ? data?.category[0] : '---'}
          {data?.category?.map((category: any, index: any) =>
            index > 0 ? ` , ${category}` : ''
          )}
        </td>
      ),
    },
    {
      label: 'Muscle',
      path: 'primaryMuscles',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.primaryMuscles ? data?.primaryMuscles[0] : '---'}
          {data?.primaryMuscles?.map((muscle: any, index: any) =>
            index > 0 ? ` , ${muscle}` : ''
          )}
        </td>
      ),
    },

    // {
    //   label: 'View',
    //   path: 'id',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="text-center align-middle">
    //       <Link
    //         href={{
    //           pathname: `/exercise-module/exercises/view/[id]`,
    //           query: { id: data?.[key] },
    //         }}
    //         passHref
    //         legacyBehavior
    //       >
    //         <button className="btn btn-default btn-outline-primary">
    //           <span>
    //             <i className="bi bi-eye me-2 align-middle"></i>
    //           </span>
    //           View
    //         </button>
    //       </Link>
    //     </td>
    //   ),
    // },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={exerciseList}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>
    </>
  );
};

export default ExerciseList;
