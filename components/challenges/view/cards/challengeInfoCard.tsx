import { FC } from 'react';

import SingleView from '@/components/common/singleView';
import { ICreateBaseChallenge } from 'models';
import moment from 'moment';

interface Props {
  challenge: ICreateBaseChallenge;
}

const ChallengeInfoCard: FC<Props> = ({ challenge }) => {
  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name="challenge-info"
        id="challenge-info"
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <div className="card-title row align-items-center ps-2 pt-2">
            <i
              className="bi bi-info-lg col-1 align-text-top"
              style={{ fontSize: '25px' }}
            />
            <div className="fs-5 col text-start px-3">Challenge Info</div>
          </div>
        </div>
        <div className="" id="challengeInfoTab">
          <div className="card-body">
            <SingleView label="Name" value={challenge.name} />
            <SingleView label="Difficulty" value={challenge.difficulty} />
            <SingleView label="Duration" value={challenge.duration} />
            <SingleView label="Description" value={challenge.description} />
            <SingleView label="Mechanics" value={challenge.mechanics} />
            {/* <SingleView label="Type" value={challenge.type} /> */}
            <SingleView label="Force Type" value={challenge.forceType} />
            <SingleView label="Category" value={challenge.category} />
            <SingleView label="Muscle Group" value={challenge.primaryMuscles} />
            <SingleView
              label="Secondary Muscle Group"
              value={challenge.secondaryMuscles}
            />
            <SingleView label="Equipments" value={challenge.equipments} />
            <SingleView
              label="Start Date"
              value={moment(challenge.startDate).utc().local().format('lll')}
            />
            <SingleView
              label="End Date"
              value={moment(challenge.endDate).utc().local().format('lll')}
            />

            <SingleView
              label="Attachment"
              attachment={challenge.preview}
              isImage={true}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default ChallengeInfoCard;
