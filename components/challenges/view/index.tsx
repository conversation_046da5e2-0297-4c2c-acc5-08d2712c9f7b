import { ICreateBaseChallenge } from 'models';
import Link from 'next/link';
import ChallengeInfoCard from './cards/challengeInfoCard';

interface Props {
  challenge: ICreateBaseChallenge;
}

const ViewChallenge: React.FC<Props> = ({ challenge }) => {
  return (
    <>
      {challenge ? (
        <div>
          <div className="content-header clearfix mt-3">
            <h1 className="float-start fs-2">
              View challenge details
              <span className="fs-5 p-3">
                <Link href="/challenges" className="text-decoration-none ">
                  <i className="bi bi-arrow-left-circle-fill p-2" />
                  Back to challenge list
                </Link>
              </span>
            </h1>
          </div>

          <div className="mt-4">
            <ChallengeInfoCard challenge={challenge} />
          </div>
        </div>
      ) : (
        ''
      )}
    </>
  );
};

export default ViewChallenge;
