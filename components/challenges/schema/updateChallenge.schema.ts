import { array, number, object, string } from 'yup';

export const updateChallengeSchema = object().shape({
  name: string()
    .min(2, 'This field must be at least 2 characters long')
    .required('This field must not be empty'),
  description: string()
    .min(2, 'This field must be at least 2 characters long')
    .required('This field must not be empty'),
  mechanics: string(),
  category: array()
    .min(1, 'You must select one')
    .required('This field is required'),
  //type: string().required('This field must not be empty'),
  forceType: string(),
  primaryMuscles: array()
    .min(1, 'You must select one')
    .required('This field is required'),
  secondaryMuscles: array(),
  equipments: array().nullable(),
  duration: number().min(30).required('This field must not be empty'),
  points: number().min(1).required('This field must not be empty'),
  difficulty: string().required('This field must not be empty'),
  loop: number(),
});
