import Link from 'next/link';
import { FC, useState } from 'react';

import { userAPI } from '@/APIs';
import Table from '@/components/global/table/table';
import { config } from 'config';
import myImageLoader from 'image/loader';
import { ICreateBaseChallengeRes } from 'models';
import moment from 'moment';
import Image from 'next/image';
import { handlePagination } from 'utils/handlePagination';
import { toast } from 'react-toastify';

interface Props {
  challengeList: ICreateBaseChallengeRes[];
  setChallenges: Function;
  setSkip: Function;
  skip: number;
}

const ChallengeList: FC<Props> = ({
  challengeList,
  setChallenges,
  setSkip,
  skip,
}) => {
  const [ChallengeId, setChallengeId] = useState('');
  const [modal, setModal] = useState({
    delete: false,
  });

  const onChangeForList = async (skip: number) => {
    try {
      const challengeList = await userAPI.getChallenges(skip, config.limit);
      if ('data' in challengeList) setChallenges(challengeList.data);
      else {
        toast.error(challengeList.error.message);
      }
    } catch (error) {}
    setSkip(0);
  };

  const deleteExercise = async () => {
    try {
      const res = await userAPI.deleteChallenge(ChallengeId);
      if ('data' in res) {
        onChangeForList(0);
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
    setModal({
      ...modal,
      delete: false,
    });
  };

  const onClickForDelete = (id: string) => {
    setChallengeId(id);
    setModal({ ...modal, delete: true });
  };

  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const columns = [
    // {
    //   label: 'ID',
    //   path: 'id',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="text-center align-middle">{data[key]}</td>
    //   ),
    // },
    {
      label: 'Attachment',
      path: 'preview',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Image
            src={data[key]}
            height={100}
            width={100}
            alt="Challenge Attachment"
            loader={myImageLoader}
          />
        </td>
      ),
    },
    {
      label: 'Difficulty',
      path: 'difficulty',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Name',
      path: 'name',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Duration',
      path: 'duration',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    // {
    //   label: 'Loop',
    //   path: 'loop',
    //   content: (data: any, key: any, index: any) => (
    //     <td className="text-center align-middle">{data[key]}</td>
    //   ),
    // },
    {
      label: 'Points',
      path: 'points',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">{data[key]}</td>
      ),
    },
    {
      label: 'Start Date',
      path: 'startDate',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {moment(data[key]).utc().local().format('llll')}
        </td>
      ),
    },
    {
      label: 'End Date',
      path: 'endDate',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {moment(data[key]).utc().local().format('llll')}
        </td>
      ),
    },
    {
      label: 'View',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/challenges/view/[id]`,
              query: { id: data?.[key] },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary">
              <span>
                <i className="bi bi-eye me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Edit',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/challenges/edit/[id]`,
              query: { id: data?.[key] },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-info">
              <span>
                <i className="bi bi-pencil me-2 align-middle"></i>
              </span>
              Edit
            </button>
          </Link>
        </td>
      ),
    },
    {
      label: 'Delete',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <button
            className="btn btn-default btn-outline-danger"
            onClick={() => onClickForDelete(data.id)}
          >
            <i className="bi bi-trash3-fill me-2 align-middle"></i>
            Delete
          </button>
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={challengeList}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>
      {modal.delete ? (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              // close modal when outside of modal is clicked
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                // do not close modal if anything inside modal content is clicked
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this item?</p>
                <br />

                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{
                      border: '1px solid gray',
                      backgroundColor: 'gray',
                      color: 'white',
                      marginRight: '10px',
                    }}
                    onClick={() =>
                      setModal({
                        ...modal,
                        delete: false,
                      })
                    }
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => deleteExercise()}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </>
  );
};

export default ChallengeList;
