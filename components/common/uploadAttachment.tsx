import myImageLoader from 'image/loader';
import Image from 'next/image';
import { FC, useState } from 'react';

interface Props {
  setFieldValue: Function;
  label: string;
  header: string;
  collapseControlId: string;
  cardName: string;
}

const Attachment: FC<Props> = ({
  setFieldValue,
  label,
  header,
  collapseControlId,
  cardName,
}) => {
  const [btnToggler, setBtnToggler] = useState('bi-dash');
  const [file, setFile] = useState<any>();

  const toggleButton = () => {
    if (btnToggler == 'bi-plus-lg') setBtnToggler('bi-dash');
    else setBtnToggler('bi-plus-lg');
  };

  return (
    <>
      <div
        className="card card-secondary card-outline my-4"
        data-card-name={cardName}
        data-hideattribute="ProductPage.HideInfoBlock"
        id={cardName}
      >
        <div className="card-header with-border d-flex justify-content-between align-items-center">
          <button
            className="btn w-100 text-top invisible m-0 h-auto p-0"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target={`#${collapseControlId}`}
            aria-expanded="true"
            aria-controls={collapseControlId}
            onClick={() => toggleButton()}
          >
            <div className="card-title row align-items-center visible">
              <i
                className="bi bi-paperclip col-1 align-text-top"
                style={{ fontSize: '25px' }}
              />
              <div className="fs-5 col px-3 text-start">{header}</div>
              <div className="col-1">
                <i className={`bi ${btnToggler}`} />
              </div>
            </div>
          </button>
        </div>
        <div className="collapse show" id={collapseControlId}>
          <div className="card-body">
            <div className="form-group row my-2 mb-3">
              <div className="col-md-3">
                <div className="label-wrapper row row-cols-auto float-md-end pe-3">
                  <label
                    className="col-form-label col fs-5 px-1 text-center"
                    htmlFor="file"
                  >
                    {label}
                    <span className="required text-danger ">*</span>
                  </label>
                </div>
              </div>
              <div className="col-md-9">
                <div className={`input-group mt-2`}>
                  <input
                    id="file"
                    name="file"
                    type="file"
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFieldValue('file', event?.target?.files![0]);
                      setFile({
                        src: URL.createObjectURL(event?.target?.files![0]),
                        type: event?.target?.files![0].type,
                      });
                    }}
                  />
                </div>
                {file && (
                  <>
                    {file.type.includes('image') ? (
                      <Image
                        loader={myImageLoader}
                        className="mt-3"
                        src={file.src}
                        height={200}
                        width={200}
                        alt="animation"
                      />
                    ) : (
                      <div className="embed-responsive embed-responsive-16by9">
                        <video
                          width="240"
                          height="200"
                          controls={true}
                          className="embed-responsive-item"
                        >
                          <source src={file.src} type="video/mp4" />
                        </video>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Attachment;
