import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { enableEdit, showModal } from '@/toolkit/modalSlice';
import { FC } from 'react';
interface Props {
  show: boolean;
}

const ConfirmationModal: FC<Props> = ({ show }) => {
  const dispatch = useAppDispatch();
  return (
    <>
      {' '}
      <div className="modal" style={{ display: show ? 'block' : 'none' }}>
        <div
          className="modal-backdrop"
          style={{
            backgroundColor: 'rgba(0, 0, 0, 0.1)',
          }}
          onClick={() => {
            // close modal when outside of modal is clicked
            dispatch(showModal(false));
            dispatch(enableEdit(false));
          }}
        >
          <div
            className="modal-content"
            onClick={(e) => {
              // do not close modal if anything inside modal content is clicked
              e.stopPropagation();
            }}
            style={{
              textAlign: 'left',
              width: '25%',
              marginLeft: '40%',
              marginTop: '5%',
              border: '1px solid gray',
              boxShadow: '1px 1px 10px gray',
              borderRadius: '10px',
              padding: '20px',
            }}
          >
            <div className="container">
              <h1>Are you sure?</h1>
              <hr />
              <p>Are you sure you want to update this item?</p>
              <br />

              <div className="clearfix float-end">
                <button
                  type="button"
                  className="btn btn-light"
                  style={{
                    border: '1px solid gray',
                    backgroundColor: 'gray',
                    color: 'white',
                    marginRight: '10px',
                  }}
                  onClick={() => {
                    dispatch(showModal(false));
                    dispatch(enableEdit(false));
                  }}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={() => {
                    dispatch(enableEdit(true));
                    dispatch(showModal(false));
                  }}
                >
                  Update
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ConfirmationModal;
