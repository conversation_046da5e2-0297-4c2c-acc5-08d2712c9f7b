import { apiFunction } from '../utils/types';
import {
  addClubMemberRest,
  answerQuestionRest,
  approvePendingCoachProfileRest,
  blockClubMemberRest,
  buyShipmentRest,
  changePasswordRest,
  createAdminRest,
  createAdvertisingProductRest,
  createBaseExerciseRest,
  createBBProgramRest,
  createBlogRest,
  createBrandRest,
  createCategoryRest,
  createChallengeRest,
  createClubRest,
  createCoachCategoryRest,
  createDealProductRest,
  createEventRest,
  createIntermediateTrainingRest,
  createManufacturerRest,
  createMuscleGroupRest,
  createPackageRest,
  createPollRest,
  createProductRest,
  createShipmentRest,
  createTagsRest,
  createTrainingCategoryRest,
  createTrainingRest,
  deleteAdvertisingProductRest,
  deleteBaseExerciseRest,
  deleteBBProgramRest,
  deleteBlogsRest,
  deleteBrandRest,
  deleteChallengeRest,
  deleteClubMemberRest,
  deleteClubRest,
  deleteCoachCategoryRest,
  deleteDealProductRest,
  deleteEventRest,
  deleteIntermediateTrainingRest,
  deleteManufacturerRest,
  deleteMuscleGroupRest,
  deletePollRest,
  deleteProductRest,
  deleteProductReviewRest,
  deleteTrainingCategoryRest,
  deleteTrainingRest,
  deleteUserRest,
  editAcceptedChallengeRest,
  getAcceptedChallengeRest,
  getAdminsRest,
  getAdvertisingProductsRest,
  getAllManufacturersRest,
  getAllOrderListRest,
  getAllTagsRest,
  getAnsweredQuestionListRest,
  getBaseExerciseByIdRest,
  getBaseExerciseRest,
  getBBProgramRest,
  getBlogByIdRest,
  getBlogsRest,
  getBrandRest,
  getBrandsRest,
  getCategoryListRest,
  getCategoryRest,
  getChallengeByIdRest,
  getChallengesRest,
  getClubDetailsFromGoogleMapRest,
  getClubMembersByStatusRest,
  getClubsFromGoogleMapRest,
  getClubsRest,
  getCoachCategoriesRest,
  getDashboardDataRest,
  getDealProductsRest,
  getIntermediateTrainingListRest,
  getManufacturerRest,
  getMultipleCoachProfilesRest,
  getMultipleEventsRest,
  getMuscleGroupRest,
  getOrderEnumRest,
  getPackageByIdRest,
  getPackagesRest,
  getPaymentMethodRest,
  getPointRest,
  getPollsRest,
  getProductRest,
  getProductReviewsRest,
  getProductSearchRest,
  getProductsRest,
  getShipmentDetailsRest,
  getSingleCoachCategoryRest,
  getSingleCoachProfileRest,
  getSingleEventRest,
  getSingleIntermediateTrainingRest,
  getSingleManufacturerRest,
  getSingleOrderByIdRest,
  getSingleTagRest,
  getSubscriberListRest,
  getTagsRest,
  getTrainingByIdRest,
  getTrainingCategoryRest,
  getTrainingListRest,
  getUnansweredQuestionListRest,
  getUserListRest,
  getUserProfileRest,
  getWaitListRest,
  getWpBlogsRest,
  getWpSingleBlogRest,
  rejectPendingCoachProfileRest,
  selectPollWinnerRest,
  setPaymentMethodRest,
  setPointRest,
  signinRest,
  updateAdminRest,
  updateBaseExerciseRest,
  updateBBProgramRest,
  updateBlogRest,
  updateBrandRest,
  updateChallengeRest,
  updateClubMemberRest,
  updateClubRest,
  updateCoachCategoryRest,
  updateEventRest,
  updateIntermediateTrainingRest,
  updateManufacturerRest,
  updateMuscleGroupRest,
  updateOrderStatusRest,
  updatePackageRest,
  updatePaymentStatusRest,
  updatePollRest,
  updateProductRest,
  updateShippingStatusRest,
  updateTrainingCategoryRest,
  updateTrainingRest,
  uploadMediaRest,
  uploadPublicMediaRest,
} from './restApi';

const restApi: apiFunction = {
  getProducts: getProductsRest,
  searchProduct: getProductSearchRest,
  getCategoryList: getCategoryListRest,
  getCategory: getCategoryRest,
  createCategory: createCategoryRest,
  createProduct: createProductRest,
  updateProduct: updateProductRest,
  getProduct: getProductRest,
  deleteProduct: deleteProductRest,
  signin: signinRest,
  createAdmin: createAdminRest,
  getAdmins: getAdminsRest,
  updateAdmin: updateAdminRest,
  changePassword: changePasswordRest,
  createManufacturer: createManufacturerRest,
  getManufacturer: getManufacturerRest,
  deleteManufacturer: deleteManufacturerRest,
  getSingleManufacturer: getSingleManufacturerRest,
  updateManufacturer: updateManufacturerRest,
  getOrderEnum: getOrderEnumRest,
  updateOrderStatus: updateOrderStatusRest,
  updatePaymentStatus: updatePaymentStatusRest,
  updateShippingStatus: updateShippingStatusRest,
  getUserProfile: getUserProfileRest,
  getBrands: getBrandsRest,
  getBrand: getBrandRest,
  getTags: getTagsRest,
  getAllManufacturers: getAllManufacturersRest,
  createBrand: createBrandRest,
  getAllOrderList: getAllOrderListRest,
  getSingleOrderById: getSingleOrderByIdRest,
  deleteBrand: deleteBrandRest,
  updateBrand: updateBrandRest,
  getAllTags: getAllTagsRest,
  createTags: createTagsRest,
  getSingleTag: getSingleTagRest,
  createClub: createClubRest,
  getClubs: getClubsRest,
  deleteClub: deleteClubRest,
  updateClub: updateClubRest,
  getClubMembersByStatus: getClubMembersByStatusRest,
  updateClubMember: updateClubMemberRest,
  deleteClubMember: deleteClubMemberRest,
  blockClubMember: blockClubMemberRest,
  addClubMember: addClubMemberRest,
  uploadMedia: uploadMediaRest,
  createBaseExercise: createBaseExerciseRest,
  getBaseExercise: getBaseExerciseRest,
  getChallenges: getChallengesRest,
  createChallenge: createChallengeRest,
  getAcceptedChallenge: getAcceptedChallengeRest,
  editAcceptedChallenge: editAcceptedChallengeRest,
  updateChallenge: updateChallengeRest,
  deleteChallenge: deleteChallengeRest,
  updateBaseExercise: updateBaseExerciseRest,
  deleteBaseExercise: deleteBaseExerciseRest,
  createTrainingCategory: createTrainingCategoryRest,
  getTrainingCategory: getTrainingCategoryRest,
  updateTrainingCategory: updateTrainingCategoryRest,
  deleteTrainingCategory: deleteTrainingCategoryRest,
  createMuscleGroup: createMuscleGroupRest,
  getMuscleGroup: getMuscleGroupRest,
  updateMuscleGroup: updateMuscleGroupRest,
  deleteMuscleGroup: deleteMuscleGroupRest,
  createTraining: createTrainingRest,
  getTrainingList: getTrainingListRest,
  getTrainingById: getTrainingByIdRest,
  updateTraining: updateTrainingRest,
  deleteTraining: deleteTrainingRest,
  getAdvertisingProducts: getAdvertisingProductsRest,
  createAdvertisingProduct: createAdvertisingProductRest,
  deleteAdvertisingProduct: deleteAdvertisingProductRest,
  getDealProducts: getDealProductsRest,
  createDealProduct: createDealProductRest,
  deleteDealProduct: deleteDealProductRest,
  getUnansweredQuestionList: getUnansweredQuestionListRest,
  answerQuestion: answerQuestionRest,
  getAnsweredQuestionList: getAnsweredQuestionListRest,
  createBBProgram: createBBProgramRest,
  getBBProgram: getBBProgramRest,
  updateBBProgram: updateBBProgramRest,
  deleteBBProgram: deleteBBProgramRest,
  createIntermediateTraining: createIntermediateTrainingRest,
  getIntermediateTrainingList: getIntermediateTrainingListRest,
  getSingleIntermediateTraining: getSingleIntermediateTrainingRest,
  deleteIntermediateTraining: deleteIntermediateTrainingRest,
  updateIntermediateTraining: updateIntermediateTrainingRest,
  uploadPublicMedia: uploadPublicMediaRest,
  getChallengeById: getChallengeByIdRest,
  getBaseExerciseById: getBaseExerciseByIdRest,
  getClubsFromGoogleMap: getClubsFromGoogleMapRest,
  getClubDetailsFromGoogleMap: getClubDetailsFromGoogleMapRest,
  setPoint: setPointRest,
  getPoint: getPointRest,
  setPaymentMethod: setPaymentMethodRest,
  getPaymentMethod: getPaymentMethodRest,
  getProductReviews: getProductReviewsRest,
  deleteProductReview: deleteProductReviewRest,
  createShipment: createShipmentRest,
  buyShipment: buyShipmentRest,
  getShipmentDetails: getShipmentDetailsRest,
  getWaitList: getWaitListRest,
  createBlog: createBlogRest,
  getBlogs: getBlogsRest,
  deleteBlogs: deleteBlogsRest,
  getBlogById: getBlogByIdRest,
  updateBlog: updateBlogRest,
  createPackage: createPackageRest,
  getPackages: getPackagesRest,
  getPackageById: getPackageByIdRest,
  updatePackage: updatePackageRest,
  getDashboardData: getDashboardDataRest,
  getUserList: getUserListRest,
  getSubscriberList: getSubscriberListRest,
  deleteUser: deleteUserRest,
  getWpBlogs: getWpBlogsRest,
  getWpSingleBlog: getWpSingleBlogRest,
  createPoll: createPollRest,
  getPolls: getPollsRest,
  updatePoll: updatePollRest,
  deletePoll: deletePollRest,
  selectPollWinner: selectPollWinnerRest,
  createEvent: createEventRest,
  getMultipleEvents: getMultipleEventsRest,
  getSingleEvent: getSingleEventRest,
  deleteEvent: deleteEventRest,
  updateEvent: updateEventRest,
  getMultipleCoachProfiles: getMultipleCoachProfilesRest,
  getSingleCoachProfile: getSingleCoachProfileRest,
  approvePendingCoachProfile: approvePendingCoachProfileRest,
  rejectPendingCoachProfile: rejectPendingCoachProfileRest,
  createCoachCategory: createCoachCategoryRest,
  deleteCoachCategory: deleteCoachCategoryRest,
  updateCoachCategory: updateCoachCategoryRest,
  getCoachCategories: getCoachCategoriesRest,
  getSingleCoachCategory: getSingleCoachCategoryRest,
};

export const userAPI = restApi;
