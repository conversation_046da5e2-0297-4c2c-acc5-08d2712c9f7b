import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import BodyBuildingProgramList from '@/components/exerciseModule/bodyBuildingProgram/list';
import { config } from 'config';
import { BodyBuildingProgram } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';
import PrevNextPagination from '@/components/common/newPagination';

const BodyBuildingProgramPage: NextPage = () => {
  const [bbPrograms, setBBPrograms] = useState<BodyBuildingProgram[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getAllBBPrograms = async () => {
    try {
      const res = await userAPI.getBBProgram(undefined, skip, limit);
      if ('data' in res) {
        if ((res.data as BodyBuildingProgram[]).length === 0) {
          setDisableNext(true);
          if (skip === 0) setBBPrograms(res.data as BodyBuildingProgram[]);
        } else {
          setDisableNext(false);
          setBBPrograms(res.data as BodyBuildingProgram[]);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getAllBBPrograms();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Body Building Programs</div>
          <Link
            href="/exercise-module/body-building-program/add-new"
            className="btn btn-primary"
          >
            Add new
          </Link>
        </div>
        <div>
          {setBBPrograms?.length! > 0 ? (
            <>
              <BodyBuildingProgramList
                bbProgramList={bbPrograms!}
                setBBProgram={setBBPrograms}
                setSkip={setSkip}
                skip={skip}
                // showSeeMore={showSeeMore}
                // setLoadData={setLoadData}
                // loadData={loadData}
                // setShowSeeMore={setShowSeeMore}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'No body building program found'
          )}
        </div>
      </main>
    </>
  );
};
export default BodyBuildingProgramPage;
