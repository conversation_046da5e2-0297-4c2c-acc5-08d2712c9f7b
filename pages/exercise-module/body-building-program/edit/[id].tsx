import { userAPI } from '@/APIs';
import EditBodyBuildingProgram from '@/components/exerciseModule/bodyBuildingProgram/edit';
import { BodyBuildingProgram } from 'models';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

const EditBodyBuildingPage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [bbProgram, setBBProgram] = useState<BodyBuildingProgram>();

  const getBBProgram = async () => {
    try {
      const res = await userAPI.getBBProgram(id);
      setBBProgram(res.data as BodyBuildingProgram);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getBBProgram();
    }
  }, [router.isReady]);
  return (
    <div className="bg-light px-5">
      <main>
        {bbProgram ? (
          <>
            <EditBodyBuildingProgram bbProgram={bbProgram} />
          </>
        ) : (
          'Nothing Found'
        )}
      </main>
    </div>
  );
};
export default EditBodyBuildingPage;
