import { userAPI } from '@/APIs';
import PrevNextPagination from '@/components/common/newPagination';
import MuscleGroupList from '@/components/exerciseModule/muscleGroup/list';
import { config } from 'config';
import { MuscleGroup } from 'models';
import type { NextPage } from 'next';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const MuscleGroupPage: NextPage = () => {
  const [muscleGroup, setMuscleGroup] = useState<MuscleGroup[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getMuscleGroup = async () => {
    try {
      const res = await userAPI.getMuscleGroup(undefined, skip, limit);
      if (res.data) {
        if (res.data.length === 0) {
          setDisableNext(true);
          if (skip === 0) setMuscleGroup(res.data);
        } else {
          setDisableNext(false);
          setMuscleGroup(res.data);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getMuscleGroup();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Muscle Group</div>
          <Link
            href="/exercise-module/muscle-group/add-new"
            className="btn btn-primary"
          >
            Add new
          </Link>
        </div>
        <div>
          {muscleGroup?.length! > 0 ? (
            <>
              <MuscleGroupList
                setMuscleGroup={setMuscleGroup}
                muscleGroupList={muscleGroup!}
                setSkip={setSkip}
                skip={skip}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'No muscle group found'
          )}
        </div>
      </main>
    </>
  );
};
export default MuscleGroupPage;
