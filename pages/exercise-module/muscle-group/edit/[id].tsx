import { userAPI } from '@/APIs';
import EditMuscleGroup from '@/components/exerciseModule/muscleGroup/edit';
import { MuscleGroup } from 'models';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

const EditMuscleGRoupPage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [muscle, setMuscle] = useState<MuscleGroup>();

  const getMuscle = async () => {
    try {
      const res = await userAPI.getMuscleGroup(id);
      setMuscle(res.data as MuscleGroup);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getMuscle();
    }
  }, [router.isReady]);
  return (
    <div className="bg-light px-5">
      <main>
        {muscle ? <EditMuscleGroup muscle={muscle} /> : 'Nothing Found'}
      </main>
    </div>
  );
};
export default EditMuscleGRoupPage;
