import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import ExerciseList from '@/components/exerciseModule/exercises/list';
import { config } from 'config';
import { ICreateBaseExcercise } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';
import PrevNextPagination from '@/components/common/newPagination';

const Exercises: NextPage = () => {
  const [exercises, setExercises] = useState<ICreateBaseExcercise[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getAllExercises = async () => {
    try {
      const res = await userAPI.getBaseExercise(
        undefined,
        undefined,
        skip,
        limit
      );
      if (res.data) {
        if (res.data.length === 0) {
          setDisableNext(true);
          if (skip === 0) {
            setExercises(res.data);
          }
        } else {
          setDisableNext(false);
          setExercises(res.data);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getAllExercises();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Exercises</div>
          <Link
            href="/exercise-module/exercises/add-new"
            className="btn btn-primary"
          >
            Add new
          </Link>
        </div>
        <div>
          {exercises?.length! > 0 ? (
            <>
              <ExerciseList
                exerciseList={exercises!}
                setExercises={setExercises}
                setSkip={setSkip}
                skip={skip}
                // showSeeMore={showSeeMore}
                // setLoadData={setLoadData}
                // loadData={loadData}
                // setShowSeeMore={setShowSeeMore}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'There is no exercise'
          )}
        </div>
      </main>
    </>
  );
};
export default Exercises;
