import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import EditExercise from '@/components/exerciseModule/exercises/edit';
import { ICreateBaseExcercise } from 'models';
import { toast } from 'react-toastify';

const EditExercisePage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [exercise, setExercise] = useState<ICreateBaseExcercise>();

  const getExercise = async () => {
    try {
      const res = await userAPI.getBaseExerciseById(id);
      if ('data' in res) {
        setExercise(res.data);
      }
    } catch (error) {
      toast.error("Can't Get Selected Exercise");
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getExercise();
    }
  }, [router.isReady]);

  return (
    <div className="bg-light px-5">
      <main>
        {exercise ? (
          <EditExercise exerciseToEdit={exercise} />
        ) : (
          'Nothing Found'
        )}
      </main>
    </div>
  );
};
export default EditExercisePage;
