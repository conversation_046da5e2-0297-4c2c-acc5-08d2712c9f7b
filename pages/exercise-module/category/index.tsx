import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import ExerciseCategoryList from '@/components/exerciseModule/category/list';
import { config } from 'config';
import { TrainingCategory } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';
import PrevNextPagination from '@/components/common/newPagination';

const Category: NextPage = () => {
  const [categories, setCategories] = useState<TrainingCategory[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getAllCategories = async () => {
    try {
      const res = await userAPI.getTrainingCategory(undefined, skip, limit);
      if ('data' in res) {
        if ((res.data as TrainingCategory[]).length === 0) {
          setDisableNext(true);
          if (skip === 0) setCategories(res.data as TrainingCategory[]);
        } else {
          setDisableNext(false);
          setCategories(res.data as TrainingCategory[]);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getAllCategories();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Categories</div>
          <Link
            href="/exercise-module/category/add-new"
            className="btn btn-primary"
          >
            Add new
          </Link>
        </div>
        <div>
          {categories?.length! > 0 ? (
            <>
              <ExerciseCategoryList
                setCategories={setCategories}
                categoryList={categories}
                setSkip={setSkip}
                skip={skip}
                // showSeeMore={showSeeMore}
                // setLoadData={setLoadData}
                // loadData={loadData}
                // setShowSeeMore={setShowSeeMore}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'No category found'
          )}
        </div>
      </main>
    </>
  );
};
export default Category;
