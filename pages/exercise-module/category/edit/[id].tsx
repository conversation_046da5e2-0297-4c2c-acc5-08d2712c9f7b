import { userAPI } from '@/APIs';
import EditExerciseCategory from '@/components/exerciseModule/category/edit';
import { TrainingCategory } from 'models';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

const EditExerciseCategoryPage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [category, setCategory] = useState<TrainingCategory>();

  const getExerciseCategory = async () => {
    try {
      const res = await userAPI.getTrainingCategory(id);
      setCategory(res.data as TrainingCategory);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getExerciseCategory();
    }
  }, [router.isReady]);
  return (
    <div className="bg-light px-5">
      <main>
        {category ? (
          <EditExerciseCategory category={category} />
        ) : (
          'Nothing Found'
        )}
      </main>
    </div>
  );
};
export default EditExerciseCategoryPage;
