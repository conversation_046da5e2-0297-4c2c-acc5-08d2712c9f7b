import { userAPI } from '@/APIs';
import ViewTraining from '@/components/training/view';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const ViewTrainingPage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [training, setTraining] = useState<any>();

  const getTraining = async () => {
    try {
      let res;
      res = await userAPI.getTrainingById(id);
      if ('data' in res) {
        setTraining(res.data);
      } else {
        res = await userAPI.getSingleIntermediateTraining(id);
        if ('data' in res) {
          setTraining(res.data);
        } else {
          toast.error("Can't Get Selected Training");
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getTraining();
    }
  }, [router.isReady]);
  return (
    <div className="bg-light px-5">
      <main>
        {training ? <ViewTraining training={training} /> : 'Nothing Found'}
      </main>
    </div>
  );
};
export default ViewTrainingPage;
