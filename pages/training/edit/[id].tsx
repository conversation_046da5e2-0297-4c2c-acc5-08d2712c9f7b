import { userAPI } from '@/APIs';
import EditTraining from '@/components/training/edit';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const EditTrainingPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [training, setTraining] = useState<any>();
  const [level, setLevel] = useState<string>();

  const getTraining = async () => {
    try {
      let res;
      res = await userAPI.getTrainingById(id);
      if ('data' in res) {
        setTraining(res.data);
        setLevel(res.data.expertiseLevel);
      } else {
        res = await userAPI.getSingleIntermediateTraining(id);
        if ('data' in res) {
          setTraining(res.data);
          setLevel('INTERMEDIATE');
        } else {
          toast.error("Can't Get Selected Training");
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getTraining();
    }
  }, [router.isReady]);
  return (
    <>
      <div className="bg-light px-5">
        <main>
          {training ? (
            <>
              <EditTraining trainingToEdit={training} level={level!} />
            </>
          ) : (
            'Nothing Found'
          )}
        </main>
      </div>
    </>
  );
};

export default EditTrainingPage;
