import ListBlock from '@/components/training/listBlock';
import { NextPage } from 'next';

const QAPage: NextPage = () => {
  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2 me-2">Trainings</div>
        </div>
        <br />
        <div className="d-flex flex-wrap flex-column">
          <ListBlock
            label="View Beginner Training List"
            extraClass="btn-outline-warning"
            level="beginner"
          />

          <ListBlock
            label="View Intermediate Training List"
            extraClass="btn-outline-success"
            level="intermediate"
          />

          <ListBlock
            label="View Advance Training List"
            extraClass="btn-outline-primary"
            level="advance"
          />
        </div>
      </main>
    </>
  );
};

export default QAPage;
