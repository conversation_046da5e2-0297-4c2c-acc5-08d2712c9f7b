import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import TrainingList from '@/components/training/list';
import { config } from 'config';
import { IIntermediateTraining } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';
import PrevNextPagination from '@/components/common/newPagination';

interface Options {
  label: string;
  value: string;
}

const IntermediateTrainings: NextPage = () => {
  const [trainings, setTrainings] = useState<IIntermediateTraining[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  const [disableNext, setDisableNext] = useState(false);

  const getAllTrainings = async () => {
    try {
      let res;
      res = await userAPI.getIntermediateTrainingList(skip, limit);
      if ('data' in res) {
        if (res.data.length === 0) {
          setDisableNext(true);
          if (skip === 0) setTrainings(res.data);
        } else {
          setDisableNext(false);
          setTrainings(res.data);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getAllTrainings();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Intermediate Trainings</div>
          <Link
            href={{
              pathname: `/training/add-new`,
              query: { level: 'INTERMEDIATE' },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-primary">Add New</button>
          </Link>
        </div>

        <div>
          {trainings?.length! > 0 ? (
            <>
              <TrainingList
                trainingList={trainings}
                setTrainings={setTrainings}
                setSkip={setSkip}
                skip={skip}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'There is no training'
          )}
        </div>
      </main>
    </>
  );
};
export default IntermediateTrainings;
