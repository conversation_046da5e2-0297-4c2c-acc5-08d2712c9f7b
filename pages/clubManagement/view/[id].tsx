import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import ViewClub from '@/components/clubs/view/clubView';
import { Club } from 'models';
import { toast } from 'react-toastify';

const LogDetailPage: NextPage = () => {
  const router = useRouter();
  const [club, setClub] = useState<Club>();
  const [ready, setReady] = useState(false);
  const id = '' + `${router.query.id}`;

  const getClub = async () => {
    try {
      const clubs = await userAPI.getClubs(0, 10000);
      if ('data' in clubs) {
        const clubToUpdate = clubs.data.find((club: Club) => club.id === id);
        setClub(clubToUpdate!);
      }
    } catch (error) {
      toast.error("Can't Get Selected Club");
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getClub();
    }
  }, [router.isReady]);

  if (!ready) {
    return null;
  }

  return (
    <div className="bg-light px-5">
      <main>{club ? <ViewClub club={club} /> : 'Nothing Found'}</main>
    </div>
  );
};
export default LogDetailPage;
