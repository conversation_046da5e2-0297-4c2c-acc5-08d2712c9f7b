import EditMemberDetails from '@/components/clubs/members/edit';
import { NextPage } from 'next';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Members from '../../../../../allData/clubMember.json';
import { ClubMember } from 'models';
import { userAPI } from '@/APIs';

const SingleActiveMember: NextPage = () => {
  const router = useRouter();
  const [ready, setReady] = useState(false);
  const [member, setMember] = useState<ClubMember>();
  const id = router.query.memberid as string;
  const clubId = router.query.id! as string;

  const getMember = async () => {
    try {
      const members = await userAPI.getClubMembersByStatus(
        clubId,
        'ACTIVE'
      );
      //console.log(members);
      const membetToUpdate = members.data.find((member:ClubMember) => member.id === id)
      setMember(membetToUpdate);
    } catch(error) {
      console.log(error);
    }
  }

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getMember();
    }
  }, [router.isReady]);

  if (!ready) {
    return null;
  }
  
  return (
    <>
      <main className='px-5'>
        {member && <EditMemberDetails member={member!}/>}
      </main>
    </>
  );
};

export default SingleActiveMember;
