import { userAPI } from '@/APIs';
import EditMemberDetails from '@/components/clubs/members/edit';
import { ClubMember } from 'models';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';

const SingleExpiredMember: NextPage = () => {
  const router = useRouter();
  const [ready, setReady] = useState(false);
  const [member, setMember] = useState<ClubMember>();
  const id = router.query.memberid! as string;
  const clubId = router.query.id! as string;

  const getMember = async () => {
    try {
      const members = await userAPI.getClubMembersByStatus(
        clubId,
        'EXPIRED'
      );
      //console.log(members);
      const membetToUpdate = members.data.find((member:ClubMember) => member.id === id)
      setMember(membetToUpdate);
    } catch(error) {
      console.log(error);
    }
  }

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getMember();
    }
  }, [router.isReady]);

  if (!ready) {
    return null;
  }
  return (
    <>
      <main className="px-5">
        <EditMemberDetails member={member!} />
      </main>
    </>
  );
};

export default SingleExpiredMember;
