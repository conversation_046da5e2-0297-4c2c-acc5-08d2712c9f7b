import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import ClubList from '@/components/clubs/list/clubList';
import { config } from 'config';
import { Club } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';
import PrevNextPagination from '@/components/common/newPagination';

const Clubs: NextPage = () => {
  const [clubs, setClubs] = useState<Club[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  const [showSeeMore, setShowSeeMore] = useState(true);
  const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getAllClubs = async () => {
    try {
      const res = await userAPI.getClubs(skip, limit);
      if ('data' in res) {
        if (res.data.length === 0) {
          setDisableNext(true);
        } else {
          setDisableNext(false);
          setClubs(res.data);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getAllClubs();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Clubs</div>
          <Link href={'/clubManagement/add-new'} className="btn btn-primary">
            Add new
          </Link>
        </div>
        <div>
          {/* <SearchWindow setProducts={setProducts} /> */}
          {clubs?.length > 0 ? (
            <>
              <ClubList
                clubs={clubs}
                setClubs={setClubs}
                setSkip={setSkip}
                skip={skip}
                // showSeeMore={showSeeMore}
                // setLoadData={setLoadData}
                // loadData={loadData}
                // setShowSeeMore={setShowSeeMore}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'There is no club'
          )}
        </div>
      </main>
    </>
  );
};
export default Clubs;
