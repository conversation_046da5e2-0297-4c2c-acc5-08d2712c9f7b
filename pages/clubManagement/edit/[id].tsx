import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import EditClub from '@/components/clubs/edit/editClubDetails';
import { userAPI } from 'APIs';
import { Club } from 'models';
import { toast } from 'react-toastify';

const EditClubPage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [club, setClub] = useState<Club>();

  const getClub = async () => {
    try {
      const clubs = await userAPI.getClubs(0, 10000);
      if ('data' in clubs) {
        const clubToUpdate = clubs.data.find((club: Club) => club.id === id);
        setClub(clubToUpdate);
      }
    } catch (error) {
      toast.error("Can't Get Selected Club");
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getClub();
    }
  }, [router.isReady]);
  return (
    <div className="bg-light px-5">
      <main>{club ? <EditClub club={club} /> : 'Nothing Found'}</main>
    </div>
  );
};
export default EditClubPage;
