import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { config } from 'config';
import FitmarketUserList from '@/components/fitmarketUsers';
import PrevNextPagination from '@/components/common/newPagination';
import { userAPI } from '@/APIs';
import { useRouter } from 'next/router';
import { toast } from 'react-toastify';
import { useSearchParams } from 'next/navigation';

const FitmarketUsersList: NextPage = () => {
  const router = useRouter();

  const [fitmarketUsers, setFitmarketUsers] = useState<any>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(20);
  const [disableNext, setDisableNext] = useState(false);

  const getAllFitmarketUsers = async () => {
    try {
      const res = await userAPI.getUserList(skip, limit);
      if ('data' in res) {
        if (res.data.length === 0) {
          setDisableNext(true);
        } else {
          setDisableNext(false);
          setFitmarketUsers(res.data);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getAllFitmarketUsers();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Fitmarket User List</div>
        </div>
        <div>
          <div className="d-flex justify-content-center flex-wrap align-items-center gap-2">
            <input
              className="border rounded w-50 p-2"
              id="search"
              onChange={(e) => {
                if (e.target.value.length === 0) {
                  getAllFitmarketUsers();
                } else {
                  const filteredUser = fitmarketUsers.filter((user: any) => {
                    if (
                      user.name
                        .toLowerCase()
                        .includes(e.target.value.toLowerCase())
                    )
                      return user;
                  });
                  if (filteredUser) setFitmarketUsers(filteredUser);
                }
              }}
              type="text"
            />
            <button
              className="btn btn-secondary"
              onClick={() => {
                (document.getElementById('search') as HTMLInputElement).value =
                  '';
                getAllFitmarketUsers();
              }}
            >
              Clear
            </button>
          </div>
          {fitmarketUsers?.length! > 0 ? (
            <>
              <FitmarketUserList
                userList={fitmarketUsers}
                setFitmarketUser={setFitmarketUsers}
                setSkip={setSkip}
                skip={skip}
                getAllFitmarketUsers={getAllFitmarketUsers}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'No user found'
          )}
        </div>
      </main>
    </>
  );
};
export default FitmarketUsersList;
