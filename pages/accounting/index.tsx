import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import PrevNextPagination from '@/components/common/newPagination';
import AccountingList from '@/components/accounting/list/accountingList';
import { config } from 'config';
import { IAccountingEntry } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';

const Accounting: NextPage = () => {
  const [accountingEntries, setAccountingEntries] = useState<IAccountingEntry[]>([]);
  const [total, setTotal] = useState<number>();

  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  const [disableNext, setDisableNext] = useState(false);

  useEffect(() => {
    const getAccountingList = async () => {
      try {
        const res = await userAPI.getAccountingEntries(skip, limit);
        if (res.data) {
          if (res.data.length === 0) {
            setDisableNext(true);
            if (skip === 0) setAccountingEntries(res.data);
          } else {
            setDisableNext(false);
            setAccountingEntries(res.data);
          }
        } else {
          toast.error(res.error.message);
        }
      } catch (error) {}
    };

    getAccountingList();
  }, [skip, limit]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Accounting</div>
          <div className="d-flex gap-2">
            <Link href="/accounting/refunds" className="btn btn-outline-primary">
              Manage Refunds
            </Link>
          </div>
        </div>
        <div>
          {accountingEntries?.length! > 0 ? (
            <>
              <AccountingList
                accountingList={accountingEntries!}
                setAccountingEntries={setAccountingEntries}
                setSkip={setSkip}
                skip={skip}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            <div className="text-center mt-5">
              <p>No accounting entries found</p>
            </div>
          )}
        </div>
      </main>
    </>
  );
};

export default Accounting;
