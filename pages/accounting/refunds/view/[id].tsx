import type { NextPage } from 'next';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

import { userAPI } from '@/APIs';
import RefundDetailsCard from '@/components/accounting/forms/refundDetailsCard';
import { IRefundEntry, ReviewStatus } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';

const ViewRefundPage: NextPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const [refund, setRefund] = useState<IRefundEntry | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      const getRefundDetails = async () => {
        try {
          const res = await userAPI.getRefundEntry(id as string);
          if (res.data) {
            setRefund(res.data);
          } else {
            toast.error(res.error.message);
          }
        } catch (error) {
          toast.error('Failed to load refund details');
        } finally {
          setLoading(false);
        }
      };

      getRefundDetails();
    }
  }, [id]);

  const handleApprove = async () => {
    if (!refund) return;
    
    const reviewComment = prompt('Please enter a review comment for approval:');
    if (!reviewComment) {
      toast.error('Review comment is required');
      return;
    }

    try {
      const res = await userAPI.approveRefundEntry(refund.id, { reviewComment });
      if (res.data) {
        toast.success('Refund approved successfully');
        setRefund({ ...refund, reviewStatus: ReviewStatus.APPROVED, reviewComment });
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {
      toast.error('Failed to approve refund');
    }
  };

  const handleReject = async () => {
    if (!refund) return;
    
    const reviewComment = prompt('Please enter a review comment for rejection:');
    if (!reviewComment) {
      toast.error('Review comment is required');
      return;
    }

    try {
      const res = await userAPI.rejectRefundEntry(refund.id, { reviewComment });
      if (res.data) {
        toast.success('Refund rejected successfully');
        setRefund({ ...refund, reviewStatus: ReviewStatus.REJECTED, reviewComment });
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {
      toast.error('Failed to reject refund');
    }
  };

  if (loading) {
    return (
      <main className="px-5">
        <div className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
          <div className="spinner-border" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </main>
    );
  }

  if (!refund) {
    return (
      <main className="px-5">
        <div className="text-center mt-5">
          <h3>Refund not found</h3>
          <Link href="/accounting/refunds" className="btn btn-primary">
            Back to Refunds
          </Link>
        </div>
      </main>
    );
  }

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Refund Details</div>
          <div className="d-flex gap-2">
            <Link href="/accounting/refunds" className="btn btn-outline-secondary">
              <i className="bi bi-arrow-left"></i> Back to Refunds
            </Link>
            {refund.reviewStatus === ReviewStatus.PENDING && (
              <>
                <button
                  className="btn btn-success"
                  onClick={handleApprove}
                >
                  <i className="bi bi-check-circle"></i> Approve
                </button>
                <button
                  className="btn btn-danger"
                  onClick={handleReject}
                >
                  <i className="bi bi-x-circle"></i> Reject
                </button>
              </>
            )}
          </div>
        </div>
        
        <div className="mt-4">
          <RefundDetailsCard refund={refund} />
        </div>
      </main>
    </>
  );
};

export default ViewRefundPage;
