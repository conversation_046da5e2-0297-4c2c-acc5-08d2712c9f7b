import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import PrevNextPagination from '@/components/common/newPagination';
import RefundList from '@/components/accounting/list/refundList';
import { config } from 'config';
import { IRefundEntry } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';

const RefundsPage: NextPage = () => {
  const [refundEntries, setRefundEntries] = useState<IRefundEntry[]>([]);
  const [total, setTotal] = useState<number>();

  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  const [disableNext, setDisableNext] = useState(false);

  useEffect(() => {
    const getRefundList = async () => {
      try {
        const res = await userAPI.getRefundEntries(skip, limit);
        if (res.data) {
          if (res.data.length === 0) {
            setDisableNext(true);
            if (skip === 0) setRefundEntries(res.data);
          } else {
            setDisableNext(false);
            setRefundEntries(res.data);
          }
        } else {
          toast.error(res.error.message);
        }
      } catch (error) {}
    };

    getRefundList();
  }, [skip, limit]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Refund Management</div>
          <div className="d-flex gap-2">
            <Link href="/accounting" className="btn btn-outline-secondary">
              <i className="bi bi-arrow-left"></i> Back to Accounting
            </Link>
          </div>
        </div>
        <div>
          {refundEntries?.length! > 0 ? (
            <>
              <RefundList
                refundList={refundEntries!}
                setRefundEntries={setRefundEntries}
                setSkip={setSkip}
                skip={skip}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            <div className="text-center mt-5">
              <p>No refund requests found</p>
            </div>
          )}
        </div>
      </main>
    </>
  );
};

export default RefundsPage;
