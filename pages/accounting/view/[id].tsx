import type { NextPage } from 'next';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

import { userAPI } from '@/APIs';
import { IAccountingEntry } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';
import moment from 'moment';

const ViewAccountingEntryPage: NextPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const [accountingEntry, setAccountingEntry] = useState<IAccountingEntry | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      const getAccountingEntryDetails = async () => {
        try {
          const res = await userAPI.getAccountingEntry(id as string);
          if (res.data) {
            setAccountingEntry(res.data);
          } else {
            toast.error(res.error.message);
          }
        } catch (error) {
          toast.error('Failed to load accounting entry details');
        } finally {
          setLoading(false);
        }
      };

      getAccountingEntryDetails();
    }
  }, [id]);

  if (loading) {
    return (
      <main className="px-5">
        <div className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
          <div className="spinner-border" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </main>
    );
  }

  if (!accountingEntry) {
    return (
      <main className="px-5">
        <div className="text-center mt-5">
          <h3>Accounting entry not found</h3>
          <Link href="/accounting" className="btn btn-primary">
            Back to Accounting
          </Link>
        </div>
      </main>
    );
  }

  const getTransactionTypeBadge = (type: string) => {
    return type === 'credit' ? 'bg-success' : 'bg-danger';
  };

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Accounting Entry Details</div>
          <div className="d-flex gap-2">
            <Link href="/accounting" className="btn btn-outline-secondary">
              <i className="bi bi-arrow-left"></i> Back to Accounting
            </Link>
          </div>
        </div>
        
        <div className="mt-4">
          <div className="card">
            <div className="card-header">
              <h5 className="card-title mb-0">Transaction Details</h5>
            </div>
            <div className="card-body">
              <div className="row">
                <div className="col-md-6">
                  <div className="mb-3">
                    <label className="form-label fw-bold">Entry ID:</label>
                    <p className="mb-0">{accountingEntry.id}</p>
                  </div>
                  <div className="mb-3">
                    <label className="form-label fw-bold">Transaction Type:</label>
                    <p className="mb-0">
                      <span className={`badge ${getTransactionTypeBadge(accountingEntry.transactionType)}`}>
                        {accountingEntry.transactionType.toUpperCase()}
                      </span>
                    </p>
                  </div>
                  <div className="mb-3">
                    <label className="form-label fw-bold">Reference:</label>
                    <p className="mb-0">
                      <span className="badge bg-info">
                        {accountingEntry.reference.toUpperCase()}
                      </span>
                    </p>
                  </div>
                  <div className="mb-3">
                    <label className="form-label fw-bold">Reference Type:</label>
                    <p className="mb-0">
                      <span className="badge bg-secondary">
                        {accountingEntry.referenceType.toUpperCase()}
                      </span>
                    </p>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="mb-3">
                    <label className="form-label fw-bold">Amount:</label>
                    <p className="mb-0 fs-4 fw-bold">
                      ${accountingEntry.amount.toFixed(2)}
                    </p>
                  </div>
                  <div className="mb-3">
                    <label className="form-label fw-bold">Reference ID:</label>
                    <p className="mb-0">{accountingEntry.referenceId}</p>
                  </div>
                  {accountingEntry.createdAt && (
                    <div className="mb-3">
                      <label className="form-label fw-bold">Created At:</label>
                      <p className="mb-0">
                        {moment(accountingEntry.createdAt).format('MMMM DD, YYYY HH:mm')}
                      </p>
                    </div>
                  )}
                  {accountingEntry.updatedAt && (
                    <div className="mb-3">
                      <label className="form-label fw-bold">Updated At:</label>
                      <p className="mb-0">
                        {moment(accountingEntry.updatedAt).format('MMMM DD, YYYY HH:mm')}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
};

export default ViewAccountingEntryPage;
