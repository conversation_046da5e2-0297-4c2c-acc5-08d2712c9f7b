import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import ViewSubscription from '@/components/subscriptions/view';
import { toast } from 'react-toastify';
import { userAPI } from '@/APIs';
import { ISubscriptionPackage } from 'models';

const ViewSingleSubscriptionPage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [subscription, setSubscription] = useState<ISubscriptionPackage>();

  const getSubscription = async () => {
    try {
      const res = await userAPI.getPackageById(id);
      if ('data' in res) {
        setSubscription(res.data);
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getSubscription();
    }
  }, [router.isReady]);

  return (
    <div className="bg-light px-5">
      <main>
        {subscription ? (
          <>
            <ViewSubscription subscription={subscription} />
          </>
        ) : (
          'Nothing Found'
        )}
      </main>
    </div>
  );
};
export default ViewSingleSubscriptionPage;
