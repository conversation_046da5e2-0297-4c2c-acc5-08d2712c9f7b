import EditSubscriptionPackage from '@/components/subscriptions/edit';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { ISubscriptionPackage } from 'models';
import { userAPI } from '@/APIs';
import { toast } from 'react-toastify';

const EditSubscriptionPackagePage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [packageToEdit, setPackageToEdit] = useState<ISubscriptionPackage>();

  const getPackage = async () => {
    try {
      const res = await userAPI.getPackageById(id);
      if ('data' in res) {
        setPackageToEdit(res.data);
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getPackage();
    }
  }, [router.isReady]);
  return (
    <div className="bg-light px-5">
      <main>
        {packageToEdit ? (
          <EditSubscriptionPackage packageToEdit={packageToEdit} />
        ) : (
          'Nothing Found'
        )}
      </main>
    </div>
  );
};
export default EditSubscriptionPackagePage;
