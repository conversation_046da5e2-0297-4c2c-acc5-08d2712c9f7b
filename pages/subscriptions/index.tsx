import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import SubscriptionsList from '@/components/subscriptions/list';
import { config } from 'config';
import Link from 'next/link';
import { userAPI } from '@/APIs';
import { toast } from 'react-toastify';
import { ISubscriptionPackage } from 'models';
import PrevNextPagination from '@/components/common/newPagination';

const Subscriptions: NextPage = () => {
  const [subscriptions, setSubscriptions] = useState<ISubscriptionPackage[]>(
    []
  );
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getAllSubscriptions = async () => {
    try {
      const res = await userAPI.getPackages(skip, limit);
      if ('data' in res) {
        if (res.data.length === 0) {
          setDisableNext(true);
          if (skip === 0) setSubscriptions(res.data);
        } else {
          setDisableNext(false);
          setSubscriptions(res.data);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getAllSubscriptions();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Subscriptions</div>
          <Link href="/subscriptions/add-new" className="btn btn-primary">
            Add new
          </Link>
        </div>
        <div>
          {subscriptions?.length! > 0 ? (
            <>
              <SubscriptionsList
                subscriptionList={subscriptions}
                setSubscriptions={setSubscriptions}
                setSkip={setSkip}
                skip={skip}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'No package found'
          )}
        </div>
      </main>
    </>
  );
};
export default Subscriptions;
