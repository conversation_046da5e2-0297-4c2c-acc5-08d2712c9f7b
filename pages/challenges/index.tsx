import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import ChallengeList from '@/components/challenges/list';
import { config } from 'config';
import { ICreateBaseChallengeRes } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';
import PrevNextPagination from '@/components/common/newPagination';

const Challenges: NextPage = () => {
  const [challenges, setChallenges] = useState<ICreateBaseChallengeRes[]>([]);
  const [total, setTotal] = useState<number>();

  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getChallengeList = async () => {
    try {
      const res = await userAPI.getChallenges(skip, limit);
      if (res.data) {
        if (res.data.length === 0) {
          setDisableNext(true);
          if (skip === 0) setChallenges(res.data);
        } else {
          setDisableNext(false);
          setChallenges(res.data);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getChallengeList();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Challenges</div>
          <Link href="/challenges/add-new" className="btn btn-primary">
            Add new
          </Link>
        </div>
        <div>
          {challenges?.length! > 0 ? (
            <>
              <ChallengeList
                challengeList={challenges!}
                setChallenges={setChallenges}
                setSkip={setSkip}
                skip={skip}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'There is no challenge'
          )}
        </div>
      </main>
    </>
  );
};
export default Challenges;
