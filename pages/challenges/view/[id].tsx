import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import ViewChallenge from '@/components/challenges/view';
import { toast } from 'react-toastify';

const ViewChallengePage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [challenge, setChallenge] = useState<any>();

  const getChallenge = async () => {
    try {
      const res = await userAPI.getChallengeById(id);
      if ('data' in res) {
        setChallenge(res.data);
      }
    } catch (error) {
      toast.error("Can't Get Selected Challenge");
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getChallenge();
    }
  }, [router.isReady]);
  return (
    <div className="bg-light px-5">
      <main>
        {challenge ? (
          <>
            <ViewChallenge challenge={challenge!} />
          </>
        ) : (
          'Nothing Found'
        )}
      </main>
    </div>
  );
};
export default ViewChallengePage;
