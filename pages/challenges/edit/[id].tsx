import EditChallenge from '@/components/challenges/edit';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import { ICreateBaseChallengeRes } from 'models';
import { toast } from 'react-toastify';

const EditChallengePage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [challenge, setChallenge] = useState<ICreateBaseChallengeRes>();

  const getChallenge = async () => {
    try {
      const res = await userAPI.getChallengeById(id);
      if (res.data) {
        setChallenge(res.data);
      }
    } catch (error) {
      toast.error("Can't Get Selected Exercise");
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getChallenge();
    }
  }, [router.isReady]);

  return (
    <div className="bg-light px-5">
      <main>
        {challenge ? <EditChallenge challenge={challenge} /> : 'Nothing Found'}
      </main>
    </div>
  );
};
export default EditChallengePage;
