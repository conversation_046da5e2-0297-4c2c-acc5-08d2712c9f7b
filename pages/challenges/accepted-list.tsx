import { userAPI } from '@/APIs';
import AcceptedChallengeList from '@/components/challenges/acceptedChallengeList';
import PrevNextPagination from '@/components/common/newPagination';
import { config } from 'config';
import { IUserChallengeHistoryRes, IUserChallengeStatusEnum } from 'models';
import type { NextPage } from 'next';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const AcceptedChallengesPage: NextPage = () => {
  const [acceptedChallenges, setAcceptedChallenges] = useState<
    IUserChallengeHistoryRes[]
  >([]);

  const [acceptedChallengeStatus, setAcceptedChallengeStatus] =
    useState<string>(IUserChallengeStatusEnum.PENDING);

  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getAcceptedChallengeList = async (filterBy?: string) => {
    try {
      const status = filterBy ? filterBy : IUserChallengeStatusEnum.PENDING;
      const res = await userAPI.getAcceptedChallenge(status, skip, limit);
      if (res.data) {
        let newArray;
        if ('data' in res) {
          if (res.data.length === 0) {
            setDisableNext(true);
            if (skip === 0) setAcceptedChallenges(res.data);
          } else {
            setDisableNext(false);
            setAcceptedChallenges(res.data);
          }
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  const handleFilter = () => {
    const filterBy = (document.getElementById('status') as HTMLInputElement)
      .value;
    if (filterBy.length > 0) {
      setAcceptedChallengeStatus(filterBy);
      getAcceptedChallengeList(filterBy);
    }
  };

  useEffect(() => {
    getAcceptedChallengeList();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Review Accepted Challenges</div>
        </div>
        <div className="d-flex flex-wrap mt-2 justify-content-center align-items-center">
          <p className="fs-4">Status</p>
          <select
            className="form-select text-center w-25 mb-md-3 my-md-3 mx-3"
            id="status"
            onChange={handleFilter}
          >
            <option value="">Please Select One</option>
            <option value={IUserChallengeStatusEnum.PENDING}>
              {IUserChallengeStatusEnum.PENDING}
            </option>
            <option value={IUserChallengeStatusEnum.COMPLETED}>
              {IUserChallengeStatusEnum.COMPLETED}
            </option>
            <option value={IUserChallengeStatusEnum.DECLINED}>
              {IUserChallengeStatusEnum.DECLINED}
            </option>
            <option value={IUserChallengeStatusEnum.EXPIRED}>
              {IUserChallengeStatusEnum.EXPIRED}
            </option>
            <option value={IUserChallengeStatusEnum.IN_PROGRESS}>
              {IUserChallengeStatusEnum.IN_PROGRESS}
            </option>
          </select>
          {/* <button className="btn btn-primary" onClick={handleFilter}>
            Filter
          </button> */}
        </div>
        <div>
          {acceptedChallenges?.length! > 0 ? (
            <>
              <AcceptedChallengeList
                acceptedChallengeStatus={acceptedChallengeStatus}
                acceptedChallenges={acceptedChallenges!}
                setAcceptedChallenges={setAcceptedChallenges}
                getAcceptedChallengeList={getAcceptedChallengeList}
                setSkip={setSkip}
                skip={skip}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'There is no accepted challenge'
          )}
        </div>
      </main>
    </>
  );
};
export default AcceptedChallengesPage;
