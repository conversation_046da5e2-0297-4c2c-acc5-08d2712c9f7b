import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import AdvertisingProductsList from '@/components/advertisingProducts/list';
import { config } from 'config';
import Link from 'next/link';
import { toast } from 'react-toastify';
import PrevNextPagination from '@/components/common/newPagination';

const AdvertisingProductsPage: NextPage = () => {
  const [advertisingProducts, setAdvertisingProducts] = useState<any>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getAdvertisingProducts = async () => {
    try {
      const res = await userAPI.getAdvertisingProducts(skip, limit);
      if ('data' in res) {
        //const newArray = advertisingProducts.concat(res.data);
        if (res.data.length === 0) {
          setDisableNext(true);
          if (skip === 0) {
            setAdvertisingProducts(res.data);
          }
        } else {
          setDisableNext(false);
          setAdvertisingProducts(res.data);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getAdvertisingProducts();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Advertising Products</div>
          <Link
            href="/advertising-products/add-new"
            className="btn btn-primary"
          >
            Add new
          </Link>
        </div>
        <div>
          {advertisingProducts?.length! > 0 ? (
            <>
              <AdvertisingProductsList
                productsList={advertisingProducts}
                setProducts={setAdvertisingProducts}
                setSkip={setSkip}
                skip={skip}
                // showSeeMore={showSeeMore}
                // setLoadData={setLoadData}
                // loadData={loadData}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'No product found'
          )}
        </div>
      </main>
    </>
  );
};
export default AdvertisingProductsPage;
