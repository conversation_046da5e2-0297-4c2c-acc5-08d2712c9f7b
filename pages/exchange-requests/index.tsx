import PrevNextPagination from '@/components/common/newPagination';
import { config } from 'config';
import { useEffect, useState } from 'react';
import ExchangeRequestData from 'allData/exchangeRequests.json';
import List from '@/components/exchangeRequests/List';

const ExchangeRequestsList = () => {
  const [exchangeRequests, setExchangeRequests] = useState<any>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getAllExchangeRequests = async () => {
    try {
      //   const res = await userAPI.getWaitList(skip, limit);
      //   if ('data' in res) {
      //     if (res.data.length === 0) {
      //       setDisableNext(true);
      //     } else {
      //       setDisableNext(false);
      //       setMails(res.data);
      //     }
      //   } else {
      //     toast.error(res.error.message);
      //   }
      setExchangeRequests(ExchangeRequestData.orders);
    } catch (error) {}
  };

  useEffect(() => {
    getAllExchangeRequests();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Exchange Requests List</div>
        </div>
        <div>
          {exchangeRequests.length > 0 ? (
            <>
              <List
                setExchangeRequests={setExchangeRequests}
                exchangeRequests={exchangeRequests}
                skip={skip}
                setSkip={setSkip}
              />

              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'No record found'
          )}
        </div>
      </main>
    </>
  );
};

export default ExchangeRequestsList;
