import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import ReviewList from '@/components/products/reviewList';
import { config } from 'config';
import { ReviewListWithUserInfo } from 'models/order/review.interface';
import { useRouter } from 'next/router';
import PrevNextPagination from '@/components/common/newPagination';

const ProductReviews: NextPage = () => {
  const router = useRouter();
  const productId = router.query.id! as string;

  const [reviews, setReviews] = useState<ReviewListWithUserInfo[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [ready, setReady] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getAllReviews = async () => {
    try {
      const reviewList = await userAPI.getProductReviews(
        productId,
        skip,
        limit
      );
      if ('data' in reviewList) {
        if (reviewList.data.length === 0) {
          setDisableNext(true);
        } else {
          setDisableNext(false);
          setReviews(reviewList.data);
        }
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getAllReviews();
    }
  }, [router.isReady, skip]);

  if (!ready) {
    return null;
  }

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Reviews from Fitmarket</div>
        </div>
        <div>
          {reviews ? (
            <>
              {' '}
              <ReviewList
                productId={productId}
                reviewList={reviews}
                setReviews={setReviews}
                setSkip={setSkip}
                skip={skip}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'There is no review'
          )}
        </div>
      </main>
    </>
  );
};
export default ProductReviews;
