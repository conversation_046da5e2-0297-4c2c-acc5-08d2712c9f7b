import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import ViewProduct from '@/components/products/productView.component';
import { Product } from 'models';

const LogDetailPage: NextPage = () => {
  const router = useRouter();
  const [ready, setReady] = useState(false);

  const [product, setProduct] = useState<Product>();
  const id = '' + `${router.query.id}`;

  const getProduct = async () => {
    const res = await userAPI.getProduct({ productId: `${id}` });
    if ('data' in res!) res?.data ? setProduct(res.data) : '';
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getProduct();
    }
  }, [router.isReady]);

  if (!ready) {
    return null;
  }

  return (
    <div className="bg-light px-5">
      <main>
        {product ? <ViewProduct product={product} /> : 'Nothing Found'}
      </main>
    </div>
  );
};
export default LogDetailPage;
