import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import ProductsList from '@/components/products/productsList';
import SearchWindow from '@/components/products/searchWindow';
import { config } from 'config';
import { Product } from 'models';
import Link from 'next/link';
import PrevNextPagination from '@/components/common/newPagination';
import { toast } from 'react-toastify';

const Products: NextPage = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  const [showSeeMore, setShowSeeMore] = useState(true);
  const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getAllProducts = async () => {
    const productsList = await userAPI.getProducts(skip, limit);
    if ('data' in productsList) {
      //const newArray = products.concat(productsList.data);
      if (productsList.data.length === 0) {
        setDisableNext(true);
      } else {
        setDisableNext(false);
        setProducts(productsList.data);
      }
    } else {
      toast.error(productsList.error.message);
    }
  };

  useEffect(() => {
    getAllProducts();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Products</div>
          <Link href="/Product/Create" className="btn btn-primary">
            Add new
          </Link>
        </div>
        <div>
          <SearchWindow
            setProducts={setProducts}
            setShowSeeMore={setShowSeeMore}
          />
          {products ? (
            <ProductsList
              productsList={products}
              setProducts={setProducts}
              setSkip={setSkip}
              skip={skip}
              //showSeeMore={showSeeMore}
              //setLoadData={setLoadData}
              //loadData={loadData}
            />
          ) : (
            'There is no product'
          )}
          <PrevNextPagination
            skip={skip}
            setSkip={setSkip}
            limit={limit}
            disableNext={disableNext}
          />
        </div>
      </main>
    </>
  );
};
export default Products;
