import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import EditProduct from '@/components/products/editProductDetails';
import { userAPI } from 'APIs';

const EditProductPage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [product, setProduct] = useState<any>();
  const [ready, setReady] = useState(false);

  const getProduct = async () => {
    const res = await userAPI.getProduct({ productId: `${id}` });
    if ('data' in res!) res?.data ? setProduct(res.data) : '';
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getProduct();
    }
  }, [router.isReady]);

  if (!ready) {
    return null;
  }
  return (
    <div className="bg-light px-5">
      <main>
        {product ? <EditProduct product={product} id={id} /> : 'Nothing Found'}
      </main>
    </div>
  );
};
export default EditProductPage;
