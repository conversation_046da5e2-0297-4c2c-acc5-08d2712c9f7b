import { userAPI } from '@/APIs';
import Details from '@/components/sales/orders/shipmentDetails/details';
import LabelComponent from '@/components/sales/orders/shipmentDetails/label';
import type { NextPage } from 'next';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const Label: NextPage = () => {
  const router = useRouter();
  const id = router.query.id as string;

  const [ready, setReady] = useState(false);
  const [shipmentDetails, setShipmentDetails] = useState<any>();

  const getShipmentDetails = async () => {
    try {
      const res = await userAPI.getShipmentDetails(id);
      if ('data' in res) {
        setShipmentDetails(res.data);
      } else {
        toast.error('Failed to get shipment details');
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getShipmentDetails();
    }
  }, [router.isReady]);

  if (!ready) {
    return null;
  }

  console.log(shipmentDetails);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between flex-md-nowrap align-items-center flex-wrap pt-3">
          <div className="d-flex justify-content-between">
            <h1 className="h2 fs-2">Shipment Details</h1>
            <div style={{ marginLeft: '10px' }} className="mb-2 pt-1 pb-2">
              <Link href={`/Sales/Order/Edit/${id}`} passHref legacyBehavior>
                <p
                  style={{
                    cursor: 'pointer',
                    color: '#3c8dbc',
                  }}
                  className="text-xs"
                >
                  <i className="bi bi-arrow-left-circle-fill"></i> back to order
                  edit
                </p>
              </Link>
            </div>
          </div>
        </div>
      </main>
      {shipmentDetails ? (
        <>
          {' '}
          <div className="row px-3 px-md-5">
            <div className="col-12 col-lg-5">
              <Details shipmentDetails={shipmentDetails} />
            </div>
            <div className="d-lg-block d-none ms-5 col-lg-1 vr"></div>
            <div className="col-12 col-lg-6">
              <LabelComponent shipmentDetails={shipmentDetails} />
            </div>
          </div>
        </>
      ) : (
        <p className="main px-5">No Shipment Details Found</p>
      )}
    </>
  );
};

export default Label;
