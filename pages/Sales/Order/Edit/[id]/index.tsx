import EditOrder from '@/components/sales/orders/Edit/editOrder';
import type { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { userAPI } from '../../../../../APIs/index';
const Edit: NextPage = () => {
  const router = useRouter();
  const [ready, setReady] = useState(false);
  const id = router.query.id as string;
  const [singleOrderInfo, setSingleOrderInfo] = useState<any>();

  const getSingleOrderData = async () => {
    const res = await userAPI.getSingleOrderById(id);
    res ? setSingleOrderInfo(res) : '';
  };

  useEffect(() => {
    if (router.isReady) {
      getSingleOrderData();
      setReady(true);
    }
  }, [router.isReady]);

  if (!ready) {
    return null;
  }

  //   const filteredOrder = order?.filter((data: any) => data.id == id);
  //   const singleOrder = filteredOrder[0];

  return (
    <>
      <div>
        {singleOrderInfo ? (
          <EditOrder
            getSingleOrderData={getSingleOrderData}
            singleOrderInfo={singleOrderInfo}
          />
        ) : null}
      </div>
    </>
  );
};

export default Edit;
