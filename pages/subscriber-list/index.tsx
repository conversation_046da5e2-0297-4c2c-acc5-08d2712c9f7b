import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { config } from 'config';
import { userAPI } from '@/APIs';
import { toast } from 'react-toastify';
import List from '@/components/subscriberList';
import { ISubscriptionList } from 'models';
import PrevNextPagination from '@/components/common/newPagination';

const SubscriberList: NextPage = () => {
  const [subscribers, setSubscribers] = useState<ISubscriptionList[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getAllSubscriber = async () => {
    try {
      const res = await userAPI.getSubscriberList(skip, limit);
      if ('data' in res) {
        if (res.data.length === 0) {
          setDisableNext(true);
        } else {
          setDisableNext(false);
          setSubscribers(res.data);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getAllSubscriber();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Subscriber List</div>
        </div>
        <div>
          {subscribers?.length! > 0 ? (
            <>
              <List
                setSubscriber={setSubscribers}
                setSkip={setSkip}
                skip={skip}
                // showSeeMore={showSeeMore}
                // setLoadData={setLoadData}
                // loadData={loadData}
                subscriber={subscribers}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'No subscriber found'
          )}
        </div>
      </main>
    </>
  );
};
export default SubscriberList;
