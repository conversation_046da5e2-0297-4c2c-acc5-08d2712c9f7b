import { userAPI } from '@/APIs';
import ViewEvent from '@/components/events/viewEvent';
import { getEventUserDetailsRest } from 'APIs/restApi';
import { Event } from 'models';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const ViewEventPage: NextPage = () => {
  const router = useRouter();
  const id = String(router.query.id);
  const [ready, setReady] = useState(false);
  const [event, setEvent] = useState<Event>();
  const [userDetails, setUserDetails] = useState<any>();

  const getEvent = useCallback(async () => {
    try {
      const res = await userAPI.getSingleEvent(id);
      console.log(res);
      if ('data' in res) {
        setEvent(res.data);
      } else {
        toast.error('Event not found');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch event');
    }
  }, [id]);

  const getUserDetails = useCallback(async () => {
    try {
      const res = await getEventUserDetailsRest(id);
      console.log(res);
      if ('data' in res) {
        setUserDetails(res.data);
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch user details');
    }
  }, [id]);

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getEvent();
      getUserDetails();
    }
  }, [router.isReady, getEvent, getUserDetails]);

  return (
    <div className="bg-light px-5">
      <main>
        {event ? (
          <ViewEvent event={event} userDetails={userDetails} />
        ) : (
          'Nothing Found'
        )}
      </main>
    </div>
  );
};

export default ViewEventPage;
