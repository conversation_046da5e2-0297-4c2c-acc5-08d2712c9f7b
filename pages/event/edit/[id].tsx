import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import EditEvent from '@/components/events/editEvent';
import { Event } from 'models';
import { toast } from 'react-toastify';

const EditEventPage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [events, setEvent] = useState<Event>();

  useEffect(() => {
    if (router.isReady) {
      setReady(true);

      const getEvent = async () => {
        try {
          const res = await userAPI.getSingleEvent(id);
          if ('data' in res) {
            setEvent(res.data);
          }
        } catch (error) {
          toast.error("Can't Get Selected Event");
        }
      };

      getEvent();
    }
  }, [router.isReady, id]);

  return (
    <div className="bg-light px-5">
      <main>
        {events ? <EditEvent eventToEdit={events} /> : 'Nothing Found'}
      </main>
    </div>
  );
};
export default EditEventPage;
