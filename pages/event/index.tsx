import { userAPI } from '@/APIs';
import PrevNextPagination from '@/components/common/newPagination';
import EventList from '@/components/events/eventsList';
import { config } from 'config';
import { Event } from 'models';
import type { NextPage } from 'next';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const Events: NextPage = () => {
  const [events, setEvents] = useState<Event[]>([]);

  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  const [disableNext, setDisableNext] = useState(false);

  useEffect(() => {
    const getEventList = async () => {
      try {
        const res = await userAPI.getMultipleEvents(skip, limit);
        if (res.data) {
          if (res.data.length === 0) {
            setDisableNext(true);
            if (skip === 0) setEvents(res.data);
          } else {
            setDisableNext(false);
            setEvents(res.data);
          }
        } else {
          toast.error(res.error.message);
        }
      } catch (error) {}
    };

    getEventList();
  }, [skip, limit]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Events</div>
          <Link href="/event/create" className="btn btn-primary">
            Add new
          </Link>
        </div>
        <div>
          {events?.length! > 0 ? (
            <>
              <EventList
                eventList={events}
                setEvents={setEvents}
                setSkip={setSkip}
                skip={skip}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'There is no event'
          )}
        </div>
      </main>
    </>
  );
};
export default Events;
