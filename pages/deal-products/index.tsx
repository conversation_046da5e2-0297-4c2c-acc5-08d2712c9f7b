import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import DealProductsList from '@/components/dealProducts/list';
import { config } from 'config';
import Link from 'next/link';
import { toast } from 'react-toastify';
import PrevNextPagination from '@/components/common/newPagination';

const DealProductsPage: NextPage = () => {
  const [dealProducts, setDealProducts] = useState<any>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getDealProducts = async () => {
    try {
      const res = await userAPI.getDealProducts(skip, limit);
      if ('data' in res) {
        if (res.data.length === 0) {
          setDisableNext(true);
        } else {
          setDisableNext(false);
          setDealProducts(res.data);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getDealProducts();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Deal Products</div>
          <Link href="/deal-products/add-new" className="btn btn-primary">
            Add new
          </Link>
        </div>
        <div>
          {dealProducts?.length! > 0 ? (
            <>
              <DealProductsList
                productsList={dealProducts}
                setProducts={setDealProducts}
                setSkip={setSkip}
                skip={skip}
                // showSeeMore={showSeeMore}
                // setLoadData={setLoadData}
                // loadData={loadData}
                // setShowSeeMore={setShowSeeMore}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'No product found'
          )}
        </div>
      </main>
    </>
  );
};
export default DealProductsPage;
