import { userAPI } from '@/APIs';
import EditBlog from '@/components/blogs/edit';
import { Blog } from 'models';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const EditBlogPage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [blog, setBlog] = useState<Blog>();

  const getBlog = async () => {
    try {
      const res = await userAPI.getBlogById(id);
      if ('data' in res) {
        setBlog(res.data);
      } else {
        toast.error(res?.error.message);
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getBlog();
    }
  }, [router.isReady]);

  return (
    <div className="bg-light px-5">
      <main>
        {blog ? (
          <>
            <EditBlog blog={blog} />
          </>
        ) : (
          'Nothing Found'
        )}
      </main>
    </div>
  );
};
export default EditBlogPage;
