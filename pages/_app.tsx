import Axios from 'axios';
import type { AppProps } from 'next/app';
import { Provider } from 'react-redux';
import { persistStore } from 'redux-persist';
import { PersistGate } from 'redux-persist/integration/react';

import { config } from 'config';
import { store } from 'store';

import Layout from '@/components/layouts/index';

import BackToTopButton from '@/components/common/backToTop';
import '../styles/App.scss';

Axios.defaults.baseURL = config?.restPrefix;

Axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response && error.response.data) {
      if (error.response.status === 401) {
        window.location.href = '/account/login';
      }
      return Promise.reject(error.response.data);
    }
    return Promise.reject(error.message);
  }
);

let persistor = persistStore(store);

function MyApp({ Component, pageProps }: AppProps) {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <Layout>
          <Component {...pageProps} />
          <BackToTopButton />
        </Layout>
      </PersistGate>
    </Provider>
  );
}

export default MyApp;
