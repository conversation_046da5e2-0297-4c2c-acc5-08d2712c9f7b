import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import ViewWarehouse from '@/components/warehouses/view';
import { toast } from 'react-toastify';
import WarehouseList from '../../../allData/warehouse.json';

const ViewSingleWarehousPage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [warehouse, setWarehouse] = useState<any>();

  const getWarehouse = async () => {
    try {
      //   const res = await userAPI.getBaseExerciseById(id);
      //   if ('data' in res) {
      //     setSubscription(res.data);
      //   }
      const item = WarehouseList.warehouses.find((item) => item.id === id);
      console.log(item);
      setWarehouse(item);
    } catch (error) {
      toast.error("Can't Get Selected Warehouse Details");
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getWarehouse();
    }
  }, [router.isReady]);

  return (
    <div className="bg-light px-5">
      <main>
        {warehouse ? (
          <>
            <ViewWarehouse warehouse={warehouse} />
          </>
        ) : (
          'Nothing Found'
        )}
      </main>
    </div>
  );
};
export default ViewSingleWarehousPage;
