import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { config } from 'config';
import Link from 'next/link';

import WarehouseListComponent from '@/components/warehouses/list';
import WarehouseData from '../../allData/warehouse.json';

const Warehouses: NextPage = () => {
  const [warehouseList, setWarehouseList] = useState<any>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  const [showSeeMore, setShowSeeMore] = useState(true);
  const [loadData, setLoadData] = useState(false);

  const getAllWarehouses = async () => {
    try {
      //   const res = await userAPI.getTrainingCategory(undefined, skip, limit);
      //   if ('data' in res) {
      //     const newArray = categories.concat(res.data);
      //     setCategories(newArray);
      //     if ((res.data as TrainingCategory[]).length < limit) {
      //       setShowSeeMore(false);
      //     } else {
      //       setShowSeeMore(true);
      //     }
      //   } else {
      //     toast.error(`Can't get training categories`);
      //   }
      const list = WarehouseData.warehouses;
      setWarehouseList(list);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getAllWarehouses();
  }, [loadData]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Warehouses</div>
          <Link href="/warehouses/add-new" className="btn btn-primary">
            Add new
          </Link>
        </div>
        <div>
          {warehouseList?.length! > 0 ? (
            <>
              <WarehouseListComponent
                warehouseList={warehouseList}
                setWarehouseList={setWarehouseList}
                setSkip={setSkip}
                skip={skip}
                showSeeMore={showSeeMore}
                setLoadData={setLoadData}
                loadData={loadData}
                setShowSeeMore={setShowSeeMore}
              />
            </>
          ) : (
            'No warehouse found'
          )}
        </div>
      </main>
    </>
  );
};
export default Warehouses;
