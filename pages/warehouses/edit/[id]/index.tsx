import EditWarehouseDetails from '@/components/warehouses/edit';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import WarehouseList from '../../../../allData/warehouse.json';

const EditWarehousePage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [warehouseToEdit, setWarehouseToEdit] = useState<any>();

  const getWarehouse = async () => {
    try {
      const res = WarehouseList.warehouses.find((item) => item.id === id);
      setWarehouseToEdit(res);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getWarehouse();
    }
  }, [router.isReady]);
  return (
    <div className="bg-light px-5">
      <main>
        {warehouseToEdit ? (
          <EditWarehouseDetails warehouseToEdit={warehouseToEdit} />
        ) : (
          'Nothing Found'
        )}
      </main>
    </div>
  );
};
export default EditWarehousePage;
