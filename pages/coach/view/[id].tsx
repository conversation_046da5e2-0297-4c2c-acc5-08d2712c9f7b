import { userAPI } from '@/APIs';
import ViewCoach from '@/components/coach/viewCoach';
import { GetCoachProfile } from 'models';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const ViewCoachPage: NextPage = () => {
  const router = useRouter();
  const id = String(router.query.id);
  const [ready, setReady] = useState(false);
  const [coach, setCoach] = useState<GetCoachProfile>();
  const [showModal, setShowModal] = useState(false);
  const [reviewComment, setReviewComment] = useState('');
  const [actionType, setActionType] = useState<'approve' | 'reject' | null>(
    null
  );

  const getCoach = useCallback(async () => {
    try {
      const res = await userAPI.getSingleCoachProfile(id);
      if ('data' in res) {
        setCoach(res.data);
      } else {
        toast.error('Coach not found');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch coach');
    }
  }, [id]);

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getCoach();
    }
  }, [router.isReady, getCoach]);

  const handleAction = async () => {
    try {
      if (!actionType) return;

      const data = { reviewComment };
      let res;

      if (actionType === 'approve') {
        res = await userAPI.approvePendingCoachProfile(id, data);
      } else {
        res = await userAPI.rejectPendingCoachProfile(id, data);
      }

      if ('data' in res) {
        toast.success(`Coach profile ${actionType}d successfully`);
        getCoach(); // Refresh coach data
      }
    } catch (error: any) {
      toast.error(error.message || `Failed to ${actionType} coach profile`);
    }
    setShowModal(false);
    setReviewComment('');
    setActionType(null);
  };

  const openModal = (type: 'approve' | 'reject') => {
    setActionType(type);
    setShowModal(true);
  };

  return (
    <div className="bg-light px-5">
      {coach?.reviewStatus === 'undecided' && (
        <div className="d-flex justify-content-end align-items-center mt-3">
          <button
            className="btn btn-success me-2"
            onClick={() => openModal('approve')}
          >
            Approve
          </button>
          <button
            className="btn btn-danger"
            onClick={() => openModal('reject')}
          >
            Reject
          </button>
        </div>
      )}
      <main>{coach ? <ViewCoach coach={coach} /> : 'Nothing Found'}</main>

      {/* Review Comment Modal */}
      {showModal && (
        <div className="modal" style={{ display: 'block' }}>
          <div
            className="modal-backdrop"
            style={{ backgroundColor: 'rgba(0, 0, 0, 0.1)' }}
            onClick={() => setShowModal(false)}
          >
            <div
              className="modal-content"
              onClick={(e) => e.stopPropagation()}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h5>Enter Review Comment</h5>
                <hr />
                <textarea
                  className="form-control"
                  rows={4}
                  value={reviewComment}
                  onChange={(e) => setReviewComment(e.target.value)}
                  placeholder="Enter your review comment..."
                />
                <div className="d-flex justify-content-end mt-3">
                  <button
                    className="btn btn-secondary me-2"
                    onClick={() => setShowModal(false)}
                  >
                    Cancel
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={handleAction}
                    disabled={!reviewComment.trim()}
                  >
                    Submit
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ViewCoachPage;
