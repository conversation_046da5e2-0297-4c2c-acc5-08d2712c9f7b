import { userAPI } from '@/APIs';
import CoachCategoryList from '@/components/coach/category/categoryLIst';
import type { NextPage } from 'next';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const CoachCategoryPage: NextPage = () => {
  const [coachCategories, setCoachCategories] = useState<any>();

  useEffect(() => {
    const getCoachCategoriesList = async () => {
      try {
        const res = await userAPI.getCoachCategories();
        console.log(res);
        if ('data' in res) setCoachCategories(res.data);
        else {
          toast.error(res?.error.message!);
        }
      } catch (error) {}
    };
    getCoachCategoriesList();
  }, []);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Coach Categories</div>
          <Link href="/coach/category/create" className="btn btn-primary">
            Add new
          </Link>
        </div>
        <div>
          {coachCategories?.length! > 0 ? (
            <>
              <CoachCategoryList
                coachCategoryList={coachCategories}
                setCoachCategories={setCoachCategories}
              />
            </>
          ) : (
            'There is no coach category'
          )}
        </div>
      </main>
    </>
  );
};

export default CoachCategoryPage;
