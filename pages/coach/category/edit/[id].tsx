import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import EditCategory from '@/components/coach/category/editCategory';
import { CoachCategory } from 'models';
import { toast } from 'react-toastify';

const EditCoachCategoryPage: NextPage = () => {
  const router = useRouter();
  const id = router.query.id as string;
  const [ready, setReady] = useState(false);
  const [category, setCategory] = useState<CoachCategory>();

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      const getCategory = async () => {
        try {
          const response = await userAPI.getSingleCoachCategory(id);
          if ('data' in response) {
            setCategory(response.data);
          } else {
            toast.error(
              response?.error?.message || "Can't get selected category"
            );
          }
        } catch (error) {
          toast.error("Can't get selected category");
        }
      };
      getCategory();
    }
  }, [id, router.isReady]);

  return (
    <div className="bg-light px-5">
      <main>
        {category ? <EditCategory category={category} /> : 'Nothing Found'}
      </main>
    </div>
  );
};

export default EditCoachCategoryPage;
