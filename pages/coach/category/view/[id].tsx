import { userAPI } from '@/APIs';
import ViewCoachCategory from '@/components/coach/category/viewCatrgory';
import { getCoachSubCategoriesRest } from 'APIs/restApi';
import { CoachCategory, CoachSubCategory } from 'models';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const ViewCoachCategoryPage: NextPage = () => {
  const router = useRouter();
  const id = String(router.query.id);
  const [ready, setReady] = useState(false);
  const [category, setCategory] = useState<CoachCategory>();
  const [subCategories, setSubCategories] = useState<CoachSubCategory[]>([]);

  const getCategory = useCallback(async () => {
    try {
      const res = await userAPI.getSingleCoachCategory(id);
      if ('data' in res) {
        setCategory(res.data);
      } else {
        toast.error('Category not found');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch category');
    }
  }, [id]);

  const getSubCategories = useCallback(async () => {
    try {
      const res = await getCoachSubCategoriesRest(id);
      console.log(res);
      if ('data' in res) {
        setSubCategories(res.data);
      } else {
        toast.error('Failed to fetch subcategories');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch subcategories');
    }
  }, [id]);

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getCategory();
      getSubCategories();
    }
  }, [router.isReady, getCategory, getSubCategories]);

  return (
    <div className="bg-light px-5">
      <main>
        {category ? (
          <ViewCoachCategory
            category={category}
            subCategories={subCategories}
          />
        ) : (
          'Nothing Found'
        )}
      </main>
    </div>
  );
};

export default ViewCoachCategoryPage;
