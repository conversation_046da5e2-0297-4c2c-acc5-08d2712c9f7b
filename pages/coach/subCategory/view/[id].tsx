import { getSingleCoachSubCategoryRest } from 'APIs/restApi';
import ViewSubCategory from '@/components/coach/subCategory/viewSubCategory';
import { CoachSubCategory } from 'models';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const ViewSubCategoryPage: NextPage = () => {
  const router = useRouter();
  const id = String(router.query.id);
  const [ready, setReady] = useState(false);
  const [subCategory, setSubCategory] = useState<CoachSubCategory>();

  const getSubCategory = useCallback(async () => {
    try {
      const res = await getSingleCoachSubCategoryRest(id);
      console.log('Subcategory response:', res);
      if ('data' in res) {
        setSubCategory(res.data);
      } else {
        toast.error('Subcategory not found');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch subcategory');
    }
  }, [id]);

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getSubCategory();
    }
  }, [router.isReady, getSubCategory]);

  return (
    <div className="bg-light px-5">
      <main>
        {subCategory ? (
          <ViewSubCategory subCategory={subCategory} />
        ) : (
          'Nothing Found'
        )}
      </main>
    </div>
  );
};

export default ViewSubCategoryPage;
