import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import EditSubCategory from '@/components/coach/subCategory/editSubCategory';
import { useAppSelector } from '@/redux-hooks';
import { getSingleCoachSubCategoryRest } from 'APIs/restApi';
import { CoachSubCategory } from 'models';

const EditSubCategoryPage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [subCategory, setSubCategory] = useState<CoachSubCategory>();
  const token = useAppSelector((state) => state.persistedReducer.auth.token);

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      const getSubCategory = async () => {
        try {
          const response = await getSingleCoachSubCategoryRest(id);
          if ('data' in response) {
            setSubCategory(response.data);
          } else {
            toast.error('Failed to fetch subcategory');
            router.push('/coach/subCategory');
          }
        } catch (error) {
          console.error('Error fetching subcategory:', error);
          toast.error("Can't Get Selected Subcategory");
          router.push('/coach/subCategory');
        }
      };
      getSubCategory();
    }
  }, [id, router.isReady, token, router]);

  return (
    <div className="bg-light">
      <main>
        {subCategory ? (
          <EditSubCategory subCategoryToEdit={subCategory} />
        ) : (
          'Loading...'
        )}
      </main>
    </div>
  );
};

export default EditSubCategoryPage;
