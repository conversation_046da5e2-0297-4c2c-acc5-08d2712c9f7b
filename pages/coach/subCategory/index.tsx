import CoachSubCategoryList from '@/components/coach/subCategory/subCategoryList';
import { getCoachCategoryMapRest } from 'APIs/restApi';
import { CoachCategoryMap } from 'models';
import type { NextPage } from 'next';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const CoachSubCategoryPage: NextPage = () => {
  const [categoryMaps, setCategoryMaps] = useState<CoachCategoryMap[]>([]);

  useEffect(() => {
    getCategoryMapsList();
  }, []);

  const getCategoryMapsList = async () => {
    try {
      const res = await getCoachCategoryMapRest();
      if ('data' in res) {
        setCategoryMaps(res.data);
      } else {
        toast.error(res?.error.message!);
      }
    } catch (error) {
      console.error('Error fetching category maps:', error);
      toast.error('Failed to fetch category maps');
    }
  };

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Coach Sub-Categories</div>
          <Link href="/coach/subCategory/create" className="btn btn-primary">
            Add new
          </Link>
        </div>
        <div className="mt-3">
          {categoryMaps?.length > 0 ? (
            <CoachSubCategoryList
              categoryMapList={categoryMaps}
              setCategoryMaps={setCategoryMaps}
            />
          ) : (
            'There are no categories'
          )}
        </div>
      </main>
    </>
  );
};

export default CoachSubCategoryPage;
