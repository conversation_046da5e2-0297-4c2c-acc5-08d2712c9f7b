import RefundView from '@/components/coach/refund/refundView';
import { approveRefundRest, getSingleRefundRest, rejectRefundRest } from 'APIs/restApi';
import { GetSingleRefundSuccessResponse, approveRejectRefundInterface } from 'models/coach/refund.interface';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const ViewRefundPage: NextPage = () => {
  const router = useRouter();
  const id = String(router.query.id);
  const [ready, setReady] = useState(false);
  const [refund, setRefund] = useState<GetSingleRefundSuccessResponse['data']>();
  const [showModal, setShowModal] = useState(false);
  const [reviewComment, setReviewComment] = useState('');
  const [actionType, setActionType] = useState<'APPROVED' | 'REJECTED' | null>(null);
  const getRefund = useCallback(async () => {
    try {
      const res = await getSingleRefundRest(id);
      console.log(res);
      if ('data' in res) {
        setRefund(res.data);
      } else {
        toast.error('Refund request not found');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch refund request');
    }
  }, [id]);

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getRefund();
    }
  }, [router.isReady, getRefund]);

  const handleAction = async () => {
    try {
      if (!actionType) return;

      const data: approveRejectRefundInterface = { reviewComment };
      let res;

      if (actionType === 'APPROVED') {
        res = await approveRefundRest(id, data);
      } else {
        res = await rejectRefundRest(id, data);
      }

      if ('data' in res) {
        toast.success(`Refund request ${actionType}d successfully`);
        getRefund(); // Refresh refund data
      }
    } catch (error: any) {
      toast.error(error.message || `Failed to ${actionType} refund request`);
    }
    setShowModal(false);
    setReviewComment('');
    setActionType(null);
  };

  const openModal = (type: 'APPROVED' | 'REJECTED') => {
    setActionType(type);
    setShowModal(true);
  };

  return (
    <div className="bg-light px-5">
      {refund?.reviewStatus === 'pending' && (
        <div className="d-flex justify-content-end align-items-center mt-3">
          <button
            className="btn btn-success me-2"
            onClick={() => openModal('APPROVED')}
          >
            Approve
          </button>
          <button
            className="btn btn-danger"
            onClick={() => openModal('REJECTED')}
          >
            Reject
          </button>
        </div>
      )}
      <main>{refund ? <RefundView refund={refund} /> : 'Nothing Found'}</main>

      {/* Review Comment Modal */}
      {showModal && (
        <div className="modal" style={{ display: 'block' }}>
          <div
            className="modal-backdrop"
            style={{ backgroundColor: 'rgba(0, 0, 0, 0.1)' }}
            onClick={() => setShowModal(false)}
          >
            <div
              className="modal-content"
              onClick={(e) => e.stopPropagation()}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h5>Enter Review Comment</h5>
                <hr />
                <textarea
                  className="form-control"
                  rows={4}
                  value={reviewComment}
                  onChange={(e) => setReviewComment(e.target.value)}
                  placeholder="Enter your review comment..."
                />
                <div className="d-flex justify-content-end mt-3">
                  <button
                    className="btn btn-secondary me-2"
                    onClick={() => setShowModal(false)}
                  >
                    Cancel
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={handleAction}
                    disabled={!reviewComment.trim()}
                  >
                    Submit
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ViewRefundPage;