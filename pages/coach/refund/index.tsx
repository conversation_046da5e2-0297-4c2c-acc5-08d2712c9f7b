import RefundList from '@/components/coach/refund/refundList';
import PrevNextPagination from '@/components/common/newPagination';
import { getMultipleRefundRest } from 'APIs/restApi';
import { config } from 'config';
import { Refunds } from 'models/coach/refund.interface';
import type { NextPage } from 'next';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const CoachRefunds: NextPage = () => {
  const [refunds, setRefunds] = useState<Refunds[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  const [disableNext, setDisableNext] = useState(false);
  const [reviewStatus, setReviewStatus] = useState('any');

  useEffect(() => {
    const getRefundList = async () => {
      try {
        const res = await getMultipleRefundRest(skip, limit, reviewStatus);
        if ('data' in res) {
          if (res.data.length === 0) {
            setDisableNext(true);
            if (skip === 0) setRefunds(res.data);
          } else {
            setDisableNext(false);
            setRefunds(res.data);
          }
        } else {
          toast.error(res.error?.message || 'Error fetching refunds');
        }
      } catch (error) {
        console.error('Error fetching refunds:', error);
      }
    };

    getRefundList();
  }, [skip, limit, reviewStatus]);

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setReviewStatus(e.target.value);
    setSkip(0); // Reset pagination when filter changes
  };

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3 mb-4">
          <h2 className="fs-2 mb-0">Refund Requests</h2>
          <div style={{ width: '200px' }}>
            <select
              className="form-select shadow-sm"
              onChange={handleStatusChange}
              value={reviewStatus}
            >
              <option value="any">Any</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </div>
        <div>
          {refunds?.length! > 0 ? (
            <>
              <RefundList
                refundList={refunds}
                setRefunds={setRefunds}
                setSkip={setSkip}
                skip={skip}
                reviewStatus={reviewStatus}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'There are no refund requests'
          )}
        </div>
      </main>
    </>
  );
};

export default CoachRefunds;