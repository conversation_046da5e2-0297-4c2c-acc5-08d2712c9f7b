import { userAPI } from '@/APIs';
import CoachList from '@/components/coach/coachList';
import PrevNextPagination from '@/components/common/newPagination';
import { config } from 'config';
import { GetCoachProfiles } from 'models';
import type { NextPage } from 'next';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const Coaches: NextPage = () => {
  const [coaches, setCoaches] = useState<GetCoachProfiles[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  const [disableNext, setDisableNext] = useState(false);
  const [adminReviewStatus, setAdminReviewStatus] = useState('undecided');

  useEffect(() => {
    const getCoachList = async () => {
      try {
        const res = await userAPI.getMultipleCoachProfiles(
          skip,
          limit,
          adminReviewStatus
        );
        if ('data' in res) {
          if (res.data.length === 0) {
            setDisableNext(true);
            if (skip === 0) setCoaches(res.data);
          } else {
            setDisableNext(false);
            setCoaches(res.data);
          }
        } else {
          toast.error(res.error.message);
        }
      } catch (error) {}
    };

    getCoachList();
  }, [skip, limit, adminReviewStatus]);

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setAdminReviewStatus(e.target.value);
    setSkip(0); // Reset pagination when filter changes
  };

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3 mb-4">
          <h2 className="fs-2 mb-0">Coaches</h2>
          <div style={{ width: '200px' }}>
            <select
              className="form-select shadow-sm"
              onChange={handleStatusChange}
              value={adminReviewStatus}
            >
              <option value="approved">Approved</option>
              <option value="undecided">Undecided</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </div>
        <div>
          {coaches?.length! > 0 ? (
            <>
              <CoachList
                coachList={coaches}
                setCoaches={setCoaches}
                setSkip={setSkip}
                skip={skip}
                adminReviewStatus={adminReviewStatus}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'There are no coaches'
          )}
        </div>
      </main>
    </>
  );
};

export default Coaches;
