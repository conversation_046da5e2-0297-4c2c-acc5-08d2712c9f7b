import WithdrawList from '@/components/coach/withdraw/withdrawList';
import PrevNextPagination from '@/components/common/newPagination';
import { getMultipleWithdrawRest } from 'APIs/restApi';
import { config } from 'config';
import { Withdraws } from 'models/coach/withdraw.interface';
import type { NextPage } from 'next';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const CoachWithdraws: NextPage = () => {
  const [withdraws, setWithdraws] = useState<Withdraws[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  const [disableNext, setDisableNext] = useState(false);
  const [reviewStatus, setReviewStatus] = useState('any');

  useEffect(() => {
    const getWithdrawList = async () => {
      try {
        const res = await getMultipleWithdrawRest(skip, limit, reviewStatus);
        if ('data' in res) {
          if (res.data.length === 0) {
            setDisableNext(true);
            if (skip === 0) setWithdraws(res.data);
          } else {
            setDisableNext(false);
            setWithdraws(res.data);
          }
        } else {
          toast.error(res.error?.message || 'Error fetching withdraws');
        }
      } catch (error) {
        console.error('Error fetching withdraws:', error);
      }
    };

    getWithdrawList();
  }, [skip, limit, reviewStatus]);

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setReviewStatus(e.target.value);
    setSkip(0); // Reset pagination when filter changes
  };

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3 mb-4">
          <h2 className="fs-2 mb-0">Coach Withdrawals</h2>
          <div style={{ width: '200px' }}>
            <select
              className="form-select shadow-sm"
              onChange={handleStatusChange}
              value={reviewStatus}
            >
              <option value="any">Any</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </div>
        <div>
          {withdraws?.length! > 0 ? (
            <>
              <WithdrawList
                withdrawList={withdraws}
                setWithdraws={setWithdraws}
                setSkip={setSkip}
                skip={skip}
                reviewStatus={reviewStatus}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'There are no withdrawal requests'
          )}
        </div>
      </main>
    </>
  );
};

export default CoachWithdraws;
