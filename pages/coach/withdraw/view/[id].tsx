import WithdrawView from '@/components/coach/withdraw/withdrawView';
import { approveWithdrawRest, getSingleWithdrawRest, rejectWithdrawRest } from 'APIs/restApi';
import { GetSingleWithdrawSuccessResponse, approveRejectWithdrawInterface } from 'models/coach/withdraw.interface';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const ViewWithdrawPage: NextPage = () => {
  const router = useRouter();
  const id = String(router.query.id);
  const [ready, setReady] = useState(false);
  const [withdraw, setWithdraw] = useState<GetSingleWithdrawSuccessResponse['data']>();
  const [showModal, setShowModal] = useState(false);
  const [reviewComment, setReviewComment] = useState('');
  const [bKashTrxId, setBKashTrxId] = useState('');
  const [actionType, setActionType] = useState<'APPROVED' | 'REJECTED' | null>(null);
  const getWithdraw = useCallback(async () => {
    try {
      const res = await getSingleWithdrawRest(id);
      console.log(res);
      if ('data' in res) {
        setWithdraw(res.data);
      } else {
        toast.error('Withdraw request not found');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch withdraw request');
    }
  }, [id]);

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getWithdraw();
    }
  }, [router.isReady, getWithdraw]);

  const handleAction = async () => {
    try {
      if (!actionType) return;

      const data: approveRejectWithdrawInterface = {
        reviewComment
      };
      let res;

      if (actionType === 'APPROVED') {
        data.trxId = bKashTrxId;
        res = await approveWithdrawRest(id, data);
      } else {
        res = await rejectWithdrawRest(id, data);
      }

      if ('data' in res) {
        toast.success(`Withdraw request ${actionType}d successfully`);
        getWithdraw(); // Refresh withdraw data
      }
    } catch (error: any) {
      toast.error(error.message || `Failed to ${actionType} withdraw request`);
    }
    setShowModal(false);
    setReviewComment('');
    setBKashTrxId('');
    setActionType(null);
  };

  const openModal = (type: 'APPROVED' | 'REJECTED') => {
    setActionType(type);
    setShowModal(true);
  };

  return (
    <div className="bg-light px-5">
      {withdraw?.reviewStatus === 'pending' && (
        <div className="d-flex justify-content-end align-items-center mt-3">
          <button
            className="btn btn-success me-2"
            onClick={() => openModal('APPROVED')}
          >
            Approve
          </button>
          <button
            className="btn btn-danger"
            onClick={() => openModal('REJECTED')}
          >
            Reject
          </button>
        </div>
      )}
      <main>{withdraw ? <WithdrawView withdraw={withdraw} /> : 'Nothing Found'}</main>

      {/* Review Comment Modal */}
      {showModal && (
        <div className="modal" style={{ display: 'block' }}>
          <div
            className="modal-backdrop"
            style={{ backgroundColor: 'rgba(0, 0, 0, 0.1)' }}
            onClick={() => setShowModal(false)}
          >
            <div
              className="modal-content"
              onClick={(e) => e.stopPropagation()}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h5>{actionType === 'APPROVED' ? 'Approve Withdrawal' : 'Reject Withdrawal'}</h5>
                <hr />
                {actionType === 'APPROVED' && (
                  <div className="mb-3">
                    <label className="form-label">Bkash Transaction ID</label>
                    <input
                      type="text"
                      className="form-control"
                      value={bKashTrxId}
                      onChange={(e) => setBKashTrxId(e.target.value)}
                      placeholder="Enter bKash TrxId"
                    />
                  </div>
                )}
                <div className="mb-3">
                  <label className="form-label">Review Comment</label>
                  <textarea
                    className="form-control"
                    rows={4}
                    value={reviewComment}
                    onChange={(e) => setReviewComment(e.target.value)}
                    placeholder="Enter your review comment..."
                  />
                </div>
                <div className="d-flex justify-content-end mt-3">
                  <button
                    className="btn btn-secondary me-2"
                    onClick={() => setShowModal(false)}
                  >
                    Cancel
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={handleAction}
                    disabled={!reviewComment.trim() || (actionType === 'APPROVED' && !bKashTrxId.trim())}
                  >
                    Submit
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ViewWithdrawPage;