import { userAPI } from '@/APIs';
import AllQuestionList from '@/components/QA/allQuestion';
import PrevNextPagination from '@/components/common/newPagination';
import { config } from 'config';
import { ProductQuestionsWithAnswerForAdmin } from 'models';
import { NextPage } from 'next';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const AnsweredQuestionsPage: NextPage = () => {
  const [allQuestionedList, setAllQuestionsList] = useState<
    ProductQuestionsWithAnswerForAdmin[]
  >([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getAllQuestions = async () => {
    try {
      const res = await userAPI.getAnsweredQuestionList(skip, limit);
      if ('data' in res) {
        if (res.data.length === 0) {
          setDisableNext(true);
        } else {
          setDisableNext(false);
          setAllQuestionsList(res.data);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getAllQuestions();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex gap-2 align-items-center mt-3">
          <div className="fs-2">All Questions-Answers</div>
          <Link href="/QA" className="text-decoration-none">
            <i className="bi bi-arrow-left-circle-fill p-2" />
            <span style={{ fontSize: '14px' }}>Back to Q&A</span>
          </Link>
        </div>
        <div>
          {allQuestionedList?.length! > 0 ? (
            <>
              <AllQuestionList
                questionList={allQuestionedList}
                setSkip={setSkip}
                skip={skip}
                // showSeeMore={showSeeMore}
                // setLoadData={setLoadData}
                // loadData={loadData}
                // setShowSeeMore={setShowSeeMore}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'No questions found'
          )}
        </div>
      </main>
    </>
  );
};

export default AnsweredQuestionsPage;
