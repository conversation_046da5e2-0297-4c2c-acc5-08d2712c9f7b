import ListBlock from '@/components/QA/listBlock';
import { NextPage } from 'next';

const QAPage: NextPage = () => {
  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="d-flex align-items-center">
            <div className="fs-2 me-2">Question Answer</div>
          </div>
        </div>
        <br />
        <div className="d-flex flex-wrap flex-column">
          <ListBlock
            label="View Unanswered Question List"
            extraClass="btn-outline-primary"
            status="unanswered-questions"
          />

          <ListBlock
            label="View All Question-Answer List"
            extraClass="btn-outline-success"
            status="all-questions"
          />
        </div>
      </main>
    </>
  );
};

export default QAPage;
