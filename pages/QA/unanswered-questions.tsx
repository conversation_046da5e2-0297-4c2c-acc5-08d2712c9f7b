import { userAPI } from '@/APIs';
import UnansweredQuestionList from '@/components/QA/unansweredQuestion';
import PrevNextPagination from '@/components/common/newPagination';
import { config } from 'config';
import { ProductQuestionsForAdmin } from 'models';
import { NextPage } from 'next';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const UnansweredQuestionsPage: NextPage = () => {
  const [unansweredList, setUnansweredList] = useState<
    ProductQuestionsForAdmin[]
  >([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getUnansweredQuestions = async () => {
    try {
      const res = await userAPI.getUnansweredQuestionList(skip, limit);
      if ('data' in res) {
        if (res.data.length === 0) {
          setDisableNext(true);
        } else {
          setDisableNext(false);
          setUnansweredList(res.data);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getUnansweredQuestions();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex gap-2 align-items-center mt-3">
          <div className="fs-2">Unanswered Questions</div>
          <Link href="/QA" className="text-decoration-none">
            <i className="bi bi-arrow-left-circle-fill p-2" />
            <span style={{ fontSize: '14px' }}>Back to Q&A</span>
          </Link>
        </div>
        <div>
          {unansweredList?.length! > 0 ? (
            <>
              <UnansweredQuestionList
                questionList={unansweredList}
                setSkip={setSkip}
                skip={skip}
                // showSeeMore={showSeeMore}
                // setLoadData={setLoadData}
                // loadData={loadData}
                // setShowSeeMore={setShowSeeMore}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'No unanswered questions found'
          )}
        </div>
      </main>
    </>
  );
};

export default UnansweredQuestionsPage;
