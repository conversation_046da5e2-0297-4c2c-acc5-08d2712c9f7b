import AnswerForm from '@/components/QA/answer';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

const AnswerQuestionPage: NextPage = () => {
  const router = useRouter();

  const [ready, setReady] = useState(false);

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
    }
  }, [router.isReady]);

  if (!ready) return null;

  return (
    <div className="bg-light px-5">
      <main>
        <AnswerForm />
      </main>
    </div>
  );
};
export default AnswerQuestionPage;
