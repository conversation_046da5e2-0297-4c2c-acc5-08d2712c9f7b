import { userAPI } from '@/APIs';
import PrevNextPagination from '@/components/common/newPagination';
import List from '@/components/waitingList';
import { config } from 'config';
import { ISubscriber } from 'models';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const WaitingList = () => {
  const [mails, setMails] = useState<ISubscriber[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getAllMails = async () => {
    try {
      const res = await userAPI.getWaitList(skip, limit);
      if ('data' in res) {
        if (res.data.length === 0) {
          setDisableNext(true);
        } else {
          setDisableNext(false);
          setMails(res.data);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getAllMails();
  }, [skip]);
  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Waiting List</div>
        </div>
        <div>
          {mails.length > 0 ? (
            <>
              <List
                setMails={setMails}
                setSkip={setSkip}
                skip={skip}
                // showSeeMore={showSeeMore}
                // setLoadData={setLoadData}
                // loadData={loadData}
                mails={mails}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'No record found'
          )}
        </div>
      </main>
    </>
  );
};

export default WaitingList;
