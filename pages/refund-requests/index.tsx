import RefuncRequestList from '@/components/refundRequests';
import { config } from 'config';
import type { NextPage } from 'next';
import { useEffect, useState } from 'react';
import refundRequestData from '../../allData/refund.json';

const RefundRequests: NextPage = () => {
  const [refundRequestList, setRefundRequestList] = useState<any>([]);

  const [refundRequestStatus, setRefundRequestStatus] =
    useState<string>('Requested');

  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  const [showSeeMore, setShowSeeMore] = useState(true);
  const [loadData, setLoadData] = useState(false);

  const getRefundRequestList = async (filterBy?: string) => {
    try {
      const status = filterBy ? filterBy : 'Requested';
      const res = refundRequestData.refundRequests.filter(
        (data) => data.status === status
      );
      console.log(res);
      setRefundRequestList(res);
    } catch (error) {
      console.log(error);
    }
  };

  const handleFilter = () => {
    const filterBy = (document.getElementById('status') as HTMLInputElement)
      .value;
    setRefundRequestStatus(filterBy);
    getRefundRequestList(filterBy);
  };

  useEffect(() => {
    getRefundRequestList();
  }, [loadData]);
  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Refund Requests</div>
        </div>
        <div className="d-flex flex-wrap mt-2 justify-content-center align-items-center">
          <p className="fs-4">Status</p>
          <select
            className="form-select text-center w-25 mb-md-3 my-md-3 mx-3"
            id="status"
            onChange={handleFilter}
          >
            <option value="Requested">Requested</option>
            <option value="Rejected">Rejected</option>
          </select>
          {/* <button className="btn btn-primary" onClick={handleFilter}>
            Filter
          </button> */}
        </div>
        <div>
          {refundRequestList?.length! > 0 ? (
            <RefuncRequestList
              refundStatus={refundRequestStatus}
              refundRequestList={refundRequestList!}
              setRefundRequestList={setRefundRequestList}
              getRefundRequestList={getRefundRequestList}
              setSkip={setSkip}
              skip={skip}
              showSeeMore={showSeeMore}
              setLoadData={setLoadData}
              loadData={loadData}
              setShowSeeMore={setShowSeeMore}
            />
          ) : (
            'There is no refund request'
          )}
        </div>
      </main>
    </>
  );
};
export default RefundRequests;
