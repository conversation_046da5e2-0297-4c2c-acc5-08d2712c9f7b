import ViewRefundRequest from '@/components/refundRequests/view';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import refundRequestData from '../../allData/refund.json';

const ViewRefundRequestPage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [refundRequest, setRefundRequest] = useState<any>();

  const getRefundRequest = async () => {
    try {
      let res;
      res = refundRequestData.refundRequests.find((data) => data.id === id);
      setRefundRequest(res);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getRefundRequest();
    }
  }, [router.isReady]);
  return (
    <div className="bg-light px-5">
      <main>
        {refundRequest ? (
          <ViewRefundRequest refundRequest={refundRequest} />
        ) : (
          'Nothing Found'
        )}
      </main>
    </div>
  );
};
export default ViewRefundRequestPage;
