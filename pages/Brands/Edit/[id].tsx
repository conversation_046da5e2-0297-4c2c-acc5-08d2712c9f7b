import { userAPI } from '@/APIs';
import UpdateBrand from '@/components/brands/updateBrand';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const UpdateBrandPage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [brand, setBrand] = useState<any>();
  const [ready, setReady] = useState(false);

  const getBrand = async () => {
    try {
      const res = await userAPI.getBrand({ brandId: `${id}` });
      if (res) setBrand(res);
      else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };
  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getBrand();
    }
  }, [router.isReady]);

  if (!ready) {
    return null;
  }

  return (
    <>
      <div className="mt-2 px-5">
        <UpdateBrand brand={brand} />
      </div>
    </>
  );
};

export default UpdateBrandPage;
