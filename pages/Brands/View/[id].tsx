import ViewBrand from '@/components/brands/viewBrand';
import { userAPI } from 'APIs';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const ViewBrandPage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [brand, setbrand] = useState<any>();

  const getAllBrands = async () => {
    const res = await userAPI.getBrand({ brandId: `${id}` });
    if ('data' in res) res ? setbrand(res) : '';
    else {
      toast.error(res.error.message);
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getAllBrands();
    }
  }, [router.isReady]);

  return (
    <div className="bg-light px-5">
      <main>{brand ? <ViewBrand brand={brand} /> : 'Nothing Found'}</main>
    </div>
  );
};

export default ViewBrandPage;
