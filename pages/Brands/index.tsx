import { userAPI } from '@/APIs';
import BrandsList from '@/components/brands/brandsList';
import PrevNextPagination from '@/components/common/newPagination';
import { config } from 'config';
import { Brand } from 'models';
import type { NextPage } from 'next';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const Brands: NextPage = () => {
  const [brands, setBrands] = useState<Brand[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  // const [showSeeMore, setShowSeeMore] = useState(true);
  // const [loadData, setLoadData] = useState(false);
  const [disableNext, setDisableNext] = useState(false);

  const getAllBrands = async () => {
    try {
      const res = await userAPI.getBrands(skip, limit);
      if ('data' in res!) {
        if (res.data.brands.length === 0) {
          setDisableNext(true);
          if (skip === 0) {
            setBrands(res.data.brands);
          }
        } else {
          setDisableNext(false);
          setBrands(res.data.brands);
        }
      } else {
        toast.error(res.error.message);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getAllBrands();
  }, [skip]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Brands</div>
          <Link href="/Brands/Create" className="btn btn-primary">
            Add new
          </Link>
        </div>

        <div>
          {brands ? (
            <>
              <BrandsList
                brandsList={brands}
                setBrands={setBrands}
                setSkip={setSkip}
                skip={skip}
                // showSeeMore={showSeeMore}
                // setShowSeeMore={setShowSeeMore}
                // setLoadData={setLoadData}
                // loadData={loadData}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'There is no brands'
          )}
        </div>
      </main>
    </>
  );
};

export default Brands;
