import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import EditPoll from '@/components/polls/editPollDetails';
import { userAPI } from 'APIs';
import { Poll } from 'models';
import { toast } from 'react-toastify';

const EditPollPage: NextPage = () => {
  const router = useRouter();
  const id = '' + `${router.query.id}`;
  const [ready, setReady] = useState(false);
  const [poll, setPoll] = useState<Poll>();

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      const getPoll = async () => {
        try {
          const polls = await userAPI.getPolls(0, 100);
          if ('data' in polls) {
            console.log(polls);
            const pollToUpdate = polls.data.find(
              (poll: Poll) => poll.id === id
            );
            setPoll(pollToUpdate);
          }
        } catch (error) {
          toast.error("Can't Get Selected Poll");
        }
      };
      getPoll();
    }
  }, [id, router.isReady]);

  return (
    <div className="bg-light px-5">
      <main>{poll ? <EditPoll poll={poll} /> : 'Nothing Found'}</main>
    </div>
  );
};
export default EditPollPage;
