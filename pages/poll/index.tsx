import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import PrevNextPagination from '@/components/common/newPagination';
import PollList from '@/components/polls/pollsList';
import { config } from 'config';
import { Poll } from 'models';
import Link from 'next/link';
import { toast } from 'react-toastify';

const Polls: NextPage = () => {
  const [polls, setPolls] = useState<Poll[]>([]);
  const [total, setTotal] = useState<number>();

  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(config.limit);
  const [disableNext, setDisableNext] = useState(false);

  useEffect(() => {
    const getPollList = async () => {
      try {
        const res = await userAPI.getPolls(skip, limit);
        if (res.data) {
          if (res.data.length === 0) {
            setDisableNext(true);
            if (skip === 0) setPolls(res.data);
          } else {
            setDisableNext(false);
            setPolls(res.data);
          }
        } else {
          toast.error(res.error.message);
        }
      } catch (error) {}
    };

    getPollList();
  }, [skip, limit]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Polls</div>
          <Link href="/poll/create" className="btn btn-primary">
            Add new
          </Link>
        </div>
        <div>
          {polls?.length! > 0 ? (
            <>
              <PollList
                pollList={polls!}
                setPolls={setPolls}
                setSkip={setSkip}
                skip={skip}
              />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'There is no poll'
          )}
        </div>
      </main>
    </>
  );
};
export default Polls;
