{"logData": [{"id": 1, "logLevel": "Error", "shortMsg": "Tax.Avalara error. Tax provider is not configured", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 2, "logLevel": "Information", "shortMsg": "Application Started", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 3, "logLevel": "Error", "shortMsg": "Timeout expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 4, "logLevel": "Error", "shortMsg": "Tax.Avalara error. Tax provider is not configured", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 5, "logLevel": "Warning", "shortMsg": "Resource string (admin.configuration.settings.news.blocktitle.newscomments) is not found. Language ID = 1", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 6, "logLevel": "Error", "shortMsg": "Tax.Avalara error. Tax provider is not configured", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 7, "logLevel": "Error", "shortMsg": "Tax.Avalara error. Tax provider is not configured", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 8, "logLevel": "Error", "shortMsg": "Tax.Avalara error. Tax provider is not configured", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 9, "logLevel": "Error", "shortMsg": "Tax.Avalara error. Tax provider is not configured", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 10, "logLevel": "Error", "shortMsg": "Timeout expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 11, "logLevel": "Error", "shortMsg": "Tax.Avalara error. Tax provider is not configured", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 12, "logLevel": "Warning", "shortMsg": "Resource string (admin.configuration.settings.news.blocktitle.newscomments) is not found. Language ID = 1", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 13, "logLevel": "Error", "shortMsg": "Tax.Avalara error. Tax provider is not configured", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 15, "logLevel": "Error", "shortMsg": "Tax.Avalara error. Tax provider is not configured", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 16, "logLevel": "Error", "shortMsg": "Tax.Avalara error. Tax provider is not configured", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 17, "logLevel": "Error", "shortMsg": "Tax.Avalara error. Tax provider is not configured", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 18, "logLevel": "Error", "shortMsg": "Tax.Avalara error. Tax provider is not configured", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}, {"id": 19, "logLevel": "Error", "shortMsg": "Tax.Avalara error. Tax provider is not configured", "fullMsg": "", "ip": "**************", "customer": "<EMAIL>", "pageURL": "https://admin-demo.nopcommerce.com/Admin/Order/OrderList", "refURL": "https://admin-demo.nopcommerce.com/admin/", "createdOn": "06/10/2022 2:47:52 AM"}]}