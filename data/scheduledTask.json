{"scheduledTask": [{"id": 1, "name": "Download tax rates (Tax.Avalara)", "seconds": "604800", "enabled": true, "stopOnError": true, "startDate": "6/10/2022 6:47:40 AM", "endDate": "6/10/2022 6:47:41 AM", "successDate": "6/10/2022 6:47:41 AM"}, {"id": 2, "name": "Download tax rates (Tax.Avalara)", "seconds": "604800", "enabled": false, "stopOnError": false, "startDate": "6/10/2022 6:47:40 AM", "endDate": "6/10/2022 6:47:41 AM", "successDate": "6/10/2022 6:47:41 AM"}, {"id": 3, "name": "Download tax rates (Tax.Avalara)", "seconds": "604800", "enabled": false, "stopOnError": false, "startDate": "6/10/2022 6:47:40 AM", "endDate": "6/10/2022 6:47:41 AM", "successDate": "6/10/2022 6:47:41 AM"}, {"id": 4, "name": "Download tax rates (Tax.Avalara)", "seconds": "604800", "enabled": false, "stopOnError": false, "startDate": "6/10/2022 6:47:40 AM", "endDate": "6/10/2022 6:47:41 AM", "successDate": "6/10/2022 6:47:41 AM"}, {"id": 5, "name": "Download tax rates (Tax.Avalara)", "seconds": "604800", "enabled": false, "stopOnError": false, "startDate": "6/10/2022 6:47:40 AM", "endDate": "6/10/2022 6:47:41 AM", "successDate": "6/10/2022 6:47:41 AM"}, {"id": 6, "name": "Download tax rates (Tax.Avalara)", "seconds": "604800", "enabled": false, "stopOnError": false, "startDate": "", "endDate": "", "successDate": ""}, {"id": 7, "name": "Download tax rates (Tax.Avalara)", "seconds": "604800", "enabled": false, "stopOnError": false, "startDate": "", "endDate": "", "successDate": ""}, {"id": 8, "name": "Download tax rates (Tax.Avalara)", "seconds": "604800", "enabled": false, "stopOnError": false, "startDate": "", "endDate": "", "successDate": ""}]}