import { SuccessResponse } from "models/common";

interface PackageInfo {
  title: string;
  name: string;
  type: string;
}

interface UserInfo {
  name: string;
  email: string;
}
export interface ISubscriptionList {
  userId: string;
  packageId: string;
  expireAt: string;
  createdAt: string;
  packageInfo: PackageInfo;
  userInfo: UserInfo;
}

export interface ISubscription {
  id?: string;
  userId: string;
  packageId: string;
  isActive: boolean;
  price: number;
  isPaid: boolean;
  paymentType: PackagePaymentTypeEnum;
  paidAt: Date;
  expireAt: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export enum PackageFeatureEnum {
  SPOT_REQUEST = 'SPOT_REQUEST',
  PROFILE_VIEW = 'PROFILE_VIEW',
}

export enum PackagePaymentTypeEnum {
  GOOGLE = 'GOOGLE',
  APPLE = 'APPLE',
}
export interface ISubscriptionReq {
  transactionId: string;
  paymentType: PackagePaymentTypeEnum;
  googlePaymentToken?: string;
  packageId: string;
  packageName?: string;
  userId?: string;
}

export enum SubscriptionResEnum {
  PAYMENT_NOT_VERIFIED = 'Payment not verified',
  SUBSCRIPTION_COMPLETED = 'Subscription completed',
  ERROR_IN_COMPLETING_SUBSCRIPTION = 'Error in completing subscription',
  YOU_HAVE_NO_SUBSCRIBED_PACKAGE = 'You have no subscribed package',
  DAILY_LIMIT_REACHED = 'Daily limit reached!',
  ERROR_IN_VERIFYING_PACKAGE = 'Error in verifying package',
  FREE_TRIAL_IS_OVER = 'Your free trial is over',
}

export interface GetAllSubscriberListSuccessResponse extends SuccessResponse {
  data: ISubscriptionList[];
}
