import { SuccessResponse } from "models/common";

export enum DurationUnitEnum {
  MONTHS = 'Months',
  DAYS = 'Days',
}
export interface ISubscriptionPackage {
  id?: string;
  name: string;
  type: string;
  title: string;
  description: string;
  price: number;
  duration: number;
  durationInDays: number;
  durationUnit: DurationUnitEnum;
  currency: string;
  currencySymbol: string;
  isActive: boolean;
}

export enum PackageErrorEnum {
  CAN_NOT_CREATE = 'Can not create',
  CAN_NOT_UPDATE = 'Can not update',
  PACKAGE_NOT_FOUND = 'Package not found',
}

export interface CreateSubscriptionPackageResponse extends SuccessResponse {
  data: ISubscriptionPackage;
}

export interface GetAllSubscriptionPackageResponse extends SuccessResponse {
  data: ISubscriptionPackage[];
}