export interface IGooglePayVerifyRes {
  kind: string;
  startTime: string;
  regionCode: string;
  subscriptionState: string;
  latestOrderId: string;
  canceledStateContext: CanceledStateContext;
  testPurchase: TestPurchase;
  acknowledgementState: string;
  lineItems: LineItem[];
}

export interface CanceledStateContext {
  systemInitiatedCancellation: SystemInitiatedCancellation;
}

export interface SystemInitiatedCancellation {}

export interface TestPurchase {}

export interface LineItem {
  productId: string;
  expiryTime: Date;
  autoRenewingPlan: AutoRenewingPlan;
  offerDetails: OfferDetails;
}

export interface AutoRenewingPlan {}

export interface OfferDetails {
  basePlanId: string;
  offerTags: string[];
}

export interface IVerificationRes {
  success: boolean;
  verifiedData: {
    expireAt: Date;
  };
}
