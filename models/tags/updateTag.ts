import { SuccessResponse } from '../common/index';
import { Tag } from './tags';

/**
 * API Path: /tags/:tagId
 * method: PATCH
 * params: tagId
 * response: UpdateTagResponse
 */

export interface UpdateTagRequest {
  name?: string;
  isHomePageProductsTag?: boolean;
}

export interface UpdateTagParams {
  tagId: string;
}

export const enum UpdateTagErrorMessages {
  TAG_NAME_EXISTS = 'Tag name exists',
  CAN_NOT_UPDATE_TAG = 'Can not update tag',
}

export interface UpdateTagSuccessResponse extends SuccessResponse {
  data: Tag;
}
