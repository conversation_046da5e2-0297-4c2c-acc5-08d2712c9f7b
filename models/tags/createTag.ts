import { SuccessResponse } from '../common/index';
import { Tag } from './tags';

/**
 * API Path: /tags/create-tag
 * method: POST
 * body: CreateTagRequestBody
 * response: CreateTagResponse
 */

export interface CreateTagRequestBody {
  name: string;
  isHomePageProductsTag?: boolean;
}

export interface CreateTagSuccessResponse extends SuccessResponse {
  data: Tag;
}

export const enum CreateTagErrorMessages {
  TAG_NAME_EXISTS = 'Tag name exists',
  CAN_NOT_CREATE_TAG = 'Can not create tag',
}
