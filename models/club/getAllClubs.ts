import { SuccessResponse } from '../common/index';
import { Club } from './club';

/**
 * API Path: /api/clubs
 * method: GET
 * response: GetAllClubsResponse
 */
export interface GetAllClubsQuery {
  offset?: number;
  limit?: number;
}
export interface GetAllClubsSuccessResponse extends SuccessResponse {
  data: Club[];
}

export const enum GetAllClubsErrorMessages {
  CAN_NOT_GET_ALL_CLUBS = 'Can not get all clubs',
}
