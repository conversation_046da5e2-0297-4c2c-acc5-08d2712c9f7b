import { SuccessResponse } from '../common/index';
import { Club } from './club';

/**
 * API Path: /api/clubs/nearby-clubs
 * method: GET
 * response: SearchNearbyClubsResponse
 */
export interface SearchNearbyClubsQuery {
  offset?: number;
  limit?: number;
  longitude: number;
  latitude: number;
  maxDistance?: number;
}

export interface SearchNearbyClubsSuccessResponse extends SuccessResponse {
  data: Club[];
}

export const enum SearchNearbyClubsErrorMessages {
  CAN_NOT_SEARCH_NEARBY_CLUBS = 'Can not search nearby clubs',
}

export type SearchNearbyClubResponse = SearchNearbyClubsSuccessResponse;
