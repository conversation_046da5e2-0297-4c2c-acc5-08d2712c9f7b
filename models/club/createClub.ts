import { Address } from 'models/admin/address';
import { SuccessResponse } from '../common/index';
import { Image, Location, Club } from './club';

/**
 * API Path: /api/clubs
 * method: POST
 * body: CreateClubRequestBody
 * response: CreateClubResponse
 */

export interface CreateClubRequestBody {
  name: string;
  image: Image;
  location: Location;
  address?: Address;
  description?: string;
}

export interface CreateClubSuccessResponse extends SuccessResponse {
  data: Club;
}

export const enum CreateClubErrorMessages {
  CLUB_EXIST = 'Club exist',
  CAN_NOT_CREATE_CLUB = 'Can not create club',
}
