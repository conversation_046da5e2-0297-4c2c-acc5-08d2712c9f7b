import { SuccessResponse } from '../../common/index';

/**
 * API Path: /api/clubs/member-approval/{id}
 * method: PATCH
 * params: ApproveMemberParamsBody
 * response: ApproveMemberResponse
 */
export interface ApproveMemberParamsBody {
  id: string;
}

export interface ApproveMemberWithExpireDate {
  date: Date;
}

export const enum ApproveMemberSuccessMessages {
  SUCCESSFULLY_CLUB_MEMBER_APPROVED = 'SUCCESSFULLY_CLUB_MEMBER_APPROVED',
}

export const enum ApproveMemberErrorMessages {
  ALREADY_APPROVED = 'ALREADY_APPROVED',
  CAN_NOT_APPROVE_CLUB_MEMBER = 'CAN_NOT_APPROVE_CLUB_MEMBER',
  NO_PENDING_REQUEST_EXIST = 'NO_PENDING_REQUEST_EXIST',
}

export interface ApproveMemberSuccessResponse extends SuccessResponse {
  data: {
    message: ApproveMemberSuccessMessages;
  };
}

export type ApproveMemberResponse = ApproveMemberSuccessResponse;
