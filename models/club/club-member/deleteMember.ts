import { SuccessResponse } from '../../common/index';

/**
 * API Path: /api/clubs/{clubId}/members/{id}
 * method: DELETE
 * params: DeleteMemberParamsBody
 * response: DeleteMemberResponse
 */
export interface DeleteMemberParamsBody {
  id: string;
  clubId: string;
}

export const enum DeleteMemberSuccessMessages {
  SUCCESSFULLY_CLUB_MEMBER_DELETED = 'Successfully club member deleted',
}

export const enum DeleteMemberErrorMessages {
  NO_CLUB_MEMBER_EXIST = 'No club member exist',
  CAN_NOT_DELETE_CLUB_MEMBER = 'Can not delete club member',
}

export interface DeleteMemberSuccessResponse extends SuccessResponse {
  data: {
    message: DeleteMemberSuccessMessages;
  };
}

export type DeleteMemberResponse = DeleteMemberSuccessResponse;
