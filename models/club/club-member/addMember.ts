import { SuccessResponse } from 'models/common';

/**
 * API Path: /api/clubs/{clubId}/admin/add-member/{userId}
 * method: POST
 * Param: AddMemberParam
 * response: AddMemberSuccessResponse
 */

export interface AddMemberParam {
  userId: string;
  clubId: string;
}

export interface AddMemberWithExpireDate {
  date: Date;
}

export const enum AddMemberSuccessMessage {
  SUCCESSFULLY_CLUB_MEMBER_ADDED = 'Successfully club member added',
}

export const enum AddMemberErrorMessages {
  NO_CLUB_EXIST = 'No club exist',
  USER_NOT_FOUND = 'User not found',
  ALREADY_A_FOLLOWER_OF_THIS_CLUB = 'Already a follower of this club',
  ALREADY_A_MEMBER_OF_THIS_CLUB = 'Already a member of this club',
  CAN_NOT_ADD_CLUB_MEMBER = 'Can not add club member',
}

export interface AddMemberSuccessResponse extends SuccessResponse {
  data: {
    message?: AddMemberSuccessMessage;
  };
}
