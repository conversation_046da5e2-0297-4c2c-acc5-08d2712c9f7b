import { SuccessResponse } from 'models/common';

/**
 * API Path: /api/clubs/{clubId}/leave-club
 * method: DELETE
 * Param: LeaveClubParam
 * response: LeaveClubResponse
 */
export interface LeaveClubParam {
  clubId: string;
}

export const enum LeaveClubSuccessMessage {
  SUCCESSFULLY_LEFT_CLUB = 'Successfully left club',
}

export interface LeaveClubResponse extends SuccessResponse {
  data: {
    message?: LeaveClubSuccessMessage;
  };
}

export const enum LeaveClubErrorMessages {
  ERROR_FETCHING_TO_LEAVE_CLUB = 'Error fetching to leave club',
}
