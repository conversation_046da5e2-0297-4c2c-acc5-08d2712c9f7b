import { SuccessResponse } from 'models/common';

/**
 * API Path: /api/clubs/{clubId}/join-club
 * method: POST
 * Param: JoinClubParam
 * response: JoinClubResponse
 */

export interface JoinClubParam {
  clubId: string;
}

export const enum JoinClubSuccessMessage {
  SUCCESSFULLY_JOINED_TO_A_CLUB = 'Successfully joined to a club',
}

export interface JoinClubSuccessResponse extends SuccessResponse {
  data: {
    message?: JoinClubSuccessMessage;
  };
}

export const enum JoinClubErrorMessages {
  SOMETHING_WENT_WRONG = 'Something went wrong',
  CAN_NOT_JOIN_TO_THIS_CLUB = 'Can not join to this club',
}
