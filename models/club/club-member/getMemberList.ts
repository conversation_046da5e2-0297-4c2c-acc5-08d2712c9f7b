import { SuccessResponse } from 'models/common';
import { ClubMember } from './clubMember';

/**
 * API Path: /api/clubs/{clubId}/members
 * method: GET
 * Query Parameter: ClubMemberListParam
 * response: ClubMemberListResponse
 */

export interface ClubMemberListParam {
  clubId: string;
}

export interface ClubMemberListSuccessResponse extends SuccessResponse {
  data: ClubMember[];
}

export const enum ClubMemberListErrorMessages {
  YOU_DO_NOT_BELONG_ANY_CLUB = 'You do not belong any club',
  CAN_NOT_GET_CLUB_MEMBER_LIST = 'Can not get club member list',
  YOUR_ARE_NOT_A_REGULAR_MEMBER = 'You are not a regular member',
}
