import { SuccessResponse } from 'models/common';

/**
 * API Path: /api/clubs/{clubId}/nearby-club-memberlist
 * method: GET
 * Param: NearbyMemberlistParam
 * response: NearbyMemberlistSuccessResponse
 */

export interface NearbyMemberlistParam {
  clubId: string;
}

export interface NearbyMemberlistQuery {
  skip?: number;
  limit?: number;
  longitude: number;
  latitude: number;
  maxDistance?: number;
}

export interface NearestClubMemberList {
  id: string;
  name: string;
  gender?: string;
  maxBench?: number;
  maxSquat?: number;
  location?: {
    type: string;
    coordinates: number[];
  };
  image?: {
    profile: string;
  };
}

export interface NearbyMemberlistSuccessResponse extends SuccessResponse {
  data: NearestClubMemberList[];
}

export const enum NearbyMemberlistErrorMessages {
  CAN_NOT_GET_CLUB_MEMBER_LIST = 'Can not get club member list',
  YOUR_ARE_NOT_A_REGULAR_MEMBER_OF_ANY_CLUB = 'You are not a regular member of any club',
}
