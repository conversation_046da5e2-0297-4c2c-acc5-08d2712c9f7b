import { SuccessResponse } from 'models/common';

/**
 * API Path: /api/clubs/{clubId}/member-request
 * method: POST
 * Param: ClubMemberRequestParam
 * response: ClubMemberRequestResponse
 */

export interface ClubMemberRequestParam {
  clubId: string;
}

export const enum ClubMemberRequestSuccessMessage {
  SUCCESSFULLY_YOUR_REQUEST_SENT_TO_THE_CLUB = 'SUCCESSFULLY_YOUR_REQUEST_SENT_TO_THE_CLUB',
}

export interface ClubMemberRequestSuccessResponse extends SuccessResponse {
  data: {
    message?: ClubMemberRequestSuccessMessage;
  };
}

export const enum ClubMemberRequestErrorMessages {
  NO_CLUB_EXIST = 'NO_CLUB_EXIST',
  ALREADY_YOU_ARE_A_FOLLOWER_OF_THIS_CLUB = 'ALREADY_YOU_ARE_A_FOLLOWER_OF_THIS_CLUB',
  CAN_NOT_SEND_CLUB_MEMBER_REQUEST = 'CAN_NOT_SEND_CLUB_MEMBER_REQUEST',
  ALREADY_YOU_ARE_A_MEMBER_OF_THIS_CLUB = 'ALREADY_YOU_ARE_A_MEMBER_OF_THIS_CLUB',
}
