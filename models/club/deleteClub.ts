import { SuccessResponse } from '../common/index';

/**
 * API Path: /api/clubs/{id}
 * method: DELETE
 * params: DeleteClubParamsBody
 * response: DeleteClubResponse
 */
export interface DeleteClubParamsBody {
  id: string;
}

export const enum DeleteClubSuccessMessages {
  CLUB_DELETED_SUCCESSFULLY = 'Club deleted successfully',
}

export const enum DeleteClubErrorMessages {
  CAN_NOT_DELETE_CLUB = 'Can not delete club',
  NO_CLUB_EXIST = 'No club exist',
}

export interface DeleteClubSuccessResponse extends SuccessResponse {
  data: {
    message?: DeleteClubSuccessMessages;
  };
}
