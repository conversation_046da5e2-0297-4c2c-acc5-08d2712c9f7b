import { SuccessResponse } from '../common/successResponse';

export interface CreateBrandRequest {
  info: BrandInfo;
  meta?: BrandMeta;
}

export interface BrandInfo {
  name: string;
  description?: string;
  allowToSelectPageSize?: boolean;
  published?: boolean;
  displayOrder?: number;
  pageSizeOptions?: number[];
}

export interface BrandMeta {
  keywords?: string;
  description?: string;
  title?: string;
  SEFN?: string;
}

export interface CreateBrandSuccessResponse extends SuccessResponse {
  data: CreateBrandRequest;
}

export const enum CreateBrandErrorMessage {
  CANNOT_CREATE_BRAND = 'Can not create brand',
  BRAND_ALREADY_EXISTS = 'Brand already exists',
}
