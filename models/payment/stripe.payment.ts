import {
  PaymentMethodEnum,
  PaymentStatusEnum,
} from './enums.payment.interface';

export interface IStripePaymentRequest {
  orderId: string;
}
export interface IPaymentDoc {
  id?: string;
  userId: string;
  orderId: string;
  sessionId: string; // for stripe
  paymentStatus: PaymentStatusEnum;
  paymentMethod: PaymentMethodEnum;
  totalCost: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IUpdatePaymentStatusMethod {
  paymentStatus: PaymentStatusEnum;
  paymentMethod: PaymentMethodEnum;
}

export enum PaymentErrorEnum {
  ERRON_IN_PAYMENT_VALIDATION = 'Erron in payment validation',
}
