import { SuccessResponse } from '../common/successResponse';
import { TransactionType, ReferenceType, ReferenceEntityType, ReviewStatus } from './enums.accounting.interface';

// Base Accounting Interface
export interface IAccountingEntry {
  id: string;
  transactionType: TransactionType;
  reference: ReferenceType;
  referenceId: string;
  referenceType: ReferenceEntityType;
  amount: number;
  createdAt?: Date;
  updatedAt?: Date;
}

// Create Accounting Request (if needed for manual entries)
export interface ICreateAccountingEntryReq {
  transactionType: TransactionType;
  reference: ReferenceType;
  referenceId: string;
  referenceType: ReferenceEntityType;
  amount: number;
}

// Get Accounting Entries Response
export interface IGetAccountingEntriesRes {
  data: IAccountingEntry[];
}

export interface IGetAccountingEntryRes {
  data: IAccountingEntry;
}

// Refund Interface
export interface IRefundEntry {
  id: string;
  userId: string;
  coachId: string;
  programId: string;
  subscriptionId: string;
  requestReason?: string;
  reviewStatus: ReviewStatus;
  reviewComment?: string;
  contactMobileNumber: string;
  contactEmail: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// Create Refund Request
export interface ICreateRefundReq {
  requestReason?: string;
  contactMobileNumber?: string;
  contactEmail?: string;
}

// Update Refund Request (Admin)
export interface IUpdateRefundReq {
  reviewComment: string;
}

// Refund Response Interfaces
export interface IGetRefundEntriesRes {
  data: IRefundEntry[];
}

export interface IGetRefundEntryRes {
  data: IRefundEntry;
}

export interface ICreateRefundRes extends SuccessResponse {
  data: IRefundEntry;
}

export interface IUpdateRefundRes extends SuccessResponse {
  data: IRefundEntry;
}

// Success Response Interfaces
export interface IAccountingSuccessRes extends SuccessResponse {
  data: IAccountingEntry;
}

export interface IAccountingListSuccessRes extends SuccessResponse {
  data: IAccountingEntry[];
}

// Error Messages
export const enum AccountingErrorMessages {
  CANNOT_CREATE_ENTRY = 'Cannot create accounting entry',
  CANNOT_UPDATE_ENTRY = 'Cannot update accounting entry',
  CANNOT_DELETE_ENTRY = 'Cannot delete accounting entry',
  ENTRY_NOT_FOUND = 'Accounting entry not found',
  INVALID_TRANSACTION_TYPE = 'Invalid transaction type',
  INVALID_REFERENCE_TYPE = 'Invalid reference type',
  INSUFFICIENT_PERMISSIONS = 'Insufficient permissions',
}
