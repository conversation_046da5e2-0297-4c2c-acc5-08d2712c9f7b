export enum TransactionType {
  CREDIT = 'credit',
  DEBIT = 'debit',
}

export enum ReferenceType {
  FITMARKET = 'fitmarket',
  COACH = 'coach',
}

export enum ReferenceEntityType {
  WITHDRAW = 'withdraw',
  REFUND = 'refund',
  SUBSCRIPTION = 'subscription',
  PLATFORM_FEE = 'platformfree',
}

export enum ReviewStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  SUCCESS = 'success',
  FAILED = 'failed',
}
