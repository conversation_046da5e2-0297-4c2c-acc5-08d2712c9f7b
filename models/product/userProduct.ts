export interface UserProductInfo {
  name: string;
  shortDescription?: string;
  fullDescription?: string;
  sku: string;
  price: number;
  oldPrice: number;
  publishDate?: Date;
}

export interface UserProductMeta {
  keywords?: string[];
  title?: string;
  description?: string;
  friendlyPageName?: string;
}

export interface UserProductPhoto {
  url?: string;
  id?: string;
  title?: string;
  alt?: string;
  displayOrder?: number;
}

export interface UserProductManufacturer {
  id?: string;
  name?: string;
}

export interface UserProductCategory {
  id: string;
  name: string;
  displayOrder?: number;
}

export interface UserProduct {
  id?: string;
  info: UserProductInfo;
  meta: UserProductMeta;
  tags?: string[];
  photos?: UserProductPhoto[];
  brands?: string[];
  manufacturer?: UserProductManufacturer;
  categories: UserProductCategory[];
}
