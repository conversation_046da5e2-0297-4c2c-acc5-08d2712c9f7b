import { SuccessResponse } from '../common/index';
import { Product } from './product';
/**
 * API Path: user/product/url/:url
 * method: GET
 * params: sku
 * response: GetUserProductByURLResponse
 */
export interface GetUserProductByURLParams {
  url: string;
}

export interface GetUserProductByURLSuccessResponse extends SuccessResponse {
  data: Product;
}

export declare const enum GetUserProductByURLErrorMessages {
  CAN_NOT_GET_PRODUCT = 'Can not get product',
}
