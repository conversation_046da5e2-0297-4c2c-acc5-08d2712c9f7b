import { SuccessResponse } from '../common/index';

export interface AdvertisingProductRequest {
  productId: string;
}

interface AdvertisingProductResponseMessage {
  message: string;
}

export interface AdvertisingProductRequestResponse extends SuccessResponse {
  data: AdvertisingProductResponseMessage;
}

export const enum AdvertisingProductMessages {
  ADVERTISING_PRODUCT_ADDED_SUCCESSFUL = 'Advertising product added successfully',
  CANNOT_ADD_ADVERTISING_PRODUCT = 'Can not add advertising product',
  INVALID_PRODUCT_ID = 'Invalid product id',
  ADVERTISING_PRODUCT_DELETED_SUCCESSFUL = 'Advertising product deleted',
  CANNOT_DELETE_ADVERTISING_PRODUCT = 'Can not delete advertising product',
  ADVERTISING_PRODUCTS_NOT_FOUND = 'Advertising products not found',
}
