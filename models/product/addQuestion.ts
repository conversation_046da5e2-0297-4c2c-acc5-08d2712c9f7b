import { SuccessResponse } from '../common/index';

export interface AddProductQuestionRequest {
  question: string;
}

export const enum AddProductQuestionSuccessMessages {
  QUESTION_SUCCESSFULLY_ADDED = 'Question successfully added',
}

export interface AddProductQuestionSuccessResponse extends SuccessResponse {
  data: {
    message: AddProductQuestionSuccessMessages.QUESTION_SUCCESSFULLY_ADDED;
  };
}

export const enum AddProductQuestionErrorMessages {
  INVALID_PRODUCT_ID = 'Invalid product Id',
  CAN_NOT_ADD_QUESTION = 'Can not add question',
}
