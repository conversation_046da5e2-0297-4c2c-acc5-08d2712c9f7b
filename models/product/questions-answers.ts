import { PartialUserInfo } from 'models/user';
import { SuccessResponse } from '../common/index';
import { ProductPhoto, ProductQuestionAnswer } from './product';

type productInfo = {
  info: {
    name: string;
    price: number;
    oldPrice: number;
  };
  photos: ProductPhoto[];
};

export type ProductQuestionsForAdmin = Omit<ProductQuestionAnswer, 'answer'> & {
  userInfo: PartialUserInfo;
  productInfo: productInfo;
};

export type ProductQuestionsWithAnswerForAdmin = ProductQuestionAnswer & {
  userInfo: PartialUserInfo;
  productInfo: productInfo;
};

export type ProductQuestionsWithAnswerForUser = ProductQuestionAnswer & {
  userInfo: PartialUserInfo;
};

export interface ProductQuestionsForAdminSuccessResponse
  extends SuccessResponse {
  data: ProductQuestionsForAdmin[];
}

export interface ProductQuestionsWithAnswerForAdminSuccessResponse
  extends SuccessResponse {
  data: ProductQuestionsWithAnswerForAdmin[];
}

export interface ProductQuestionsWithAnswerForUserSuccessResponse
  extends SuccessResponse {
  data: ProductQuestionsWithAnswerForUser;
}

export const enum ProductQuestionsAndAnswersErrorMessages {
  INVALID_PRODUCT_ID = 'Invalid product Id',
}
