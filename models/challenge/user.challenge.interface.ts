export interface IUserChallengeDetails {
  name: string;
  description: string;
  mechanics: string;
  type: string;
  category: string[];
  forceType: string[];
  primaryMuscles: string[];
  secondaryMuscles: string[];
  equipments: string[];
  preview: string;
  difficulty: string;
  duration: number;
  loop: number;
  points: number;
}

export interface IUserChallengeHistoryRes {
  lastStatus: string;
  expireAt: Date;
  id: string;
  videoUrl?: string;
  challengeDetails: IUserChallengeDetails;
}

export interface IUserChallengeHistorySuccessRes {
  data: IUserChallengeHistoryRes[];
}
