// -------- error message enum ------------
export const enum IChallengeErrorEnum {
  ERROR_IN_CREATING_NEW_CHALLENGE = 'Error in creating new challenge',
  ERROR_IN_UPDATING_CHALLENGE = 'Error in cupdating challenge',
  ERROR_IN_CHALLENGE_REVIEW_AND_UPDATE = 'Error in challenge review and update',
  NO_EXERCISE_IS_FOUND = 'No exercise is found',
  INVALID_STATUS = 'Invalid status',
  NOT_FOUND = 'Not found',
}

// -------- common enum ------------
export enum IChallengeDifficultyEnum {
  BEGINNER = 'Beginner',
  INTERMEDIATE = 'Intermediate',
  ADVANCE = 'Advance',
}

export enum ISortByTimeEnum {
  LATEST = 'LATEST',
  OLDEST = 'OLDEST,',
}

//-------- admin enum ------------
export enum IUpdateChallengeStatusEnum {
  COMPLETED = 'Completed',
  DECLINED = 'Decliend',
}

// ------ users enum --------
export enum IUserChallengeStatusEnum {
  IN_PROGRESS = 'In progress',
  PENDING = 'Pending',
  EXPIRED = 'Expired',
  COMPLETED = 'Completed',
  DECLINED = 'Decliend',
}
