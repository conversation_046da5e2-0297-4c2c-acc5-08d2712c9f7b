import { SuccessResponse } from 'models/common';

export interface GetBaseExerciseListQuery {
  categoryName: string;
}

export interface BaseExercise {
  id?: string;
  name: string;
  description?: string;
  mechanics?: string;
  type: string;
  category: string[];
  forceType?: string[];
  primaryMuscles: string[];
  secondaryMuscles?: string[];
  equipments?: string[];
  preview: string;
}

export interface GetBaseExerciseListResponse extends SuccessResponse {
  data: BaseExercise[];
}
