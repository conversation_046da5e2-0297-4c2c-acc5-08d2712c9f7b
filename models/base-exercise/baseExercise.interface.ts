import { SuccessResponse } from 'models/common';

export interface ICreateBaseExcercise {
  id?: string;
  name: string;
  description?: string;
  mechanics?: string;
  type: string;
  category: string[];
  forceType?: string[];
  primaryMuscles: string[];
  secondaryMuscles?: string[];
  equipments?: string[];
  previewKey?: string;
  preview: string;
}

export interface ICreateBaseExcerciseReq {
  id?: string;
  name: string;
  description?: string;
  mechanics?: string;
  type: string;
  category: string[];
  forceType?: string[];
  primaryMuscles: string[];
  secondaryMuscles?: string[];
  equipments?: string[];
  preview: string;
}

export interface IUpdateBaseExcerciseReq {
  description?: string;
  mechanics?: string;
  type?: string;
  category?: string[];
  forceType?: string[];
  primaryMuscles?: string[];
  secondaryMuscles?: string[];
  equipments?: string[];
  preview?: string;
}

export interface ICreateBaseExcerciseRes {
  id: string;
  name: string;
  description: string;
  mechanics: string;
  type: string;
  category: string[];
  forceType: string[];
  primaryMuscles: string[];
  secondaryMuscles: string[];
  equipments: string[];
  preview: string;
}

export interface IGetBaseExcerciseSuccessRes extends SuccessResponse {
  data: ICreateBaseExcerciseRes[];
}

export interface ICreateBaseExcerciseSuccessRes extends SuccessResponse {
  data: ICreateBaseExcerciseRes;
}
