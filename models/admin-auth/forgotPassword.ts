import { SuccessResponse } from '../common/index';

/**
 * API Path: /admin-auth/forgot-password
 * method: POST
 * body: ForgotPasswordRequest
 * response: ForgotPasswordSuccessResponse
 */

export interface ForgotPasswordRequest {
  username: string;
}

export interface ForgotMessageResponse {
  message?: string;
}

export interface ForgotPasswordSuccessResponse extends SuccessResponse {
  data: ForgotMessageResponse;
}

export const enum ForgotPasswordErrorMessages {
  CAN_NOT_GET_ADMIN = 'Can not get admin',
  CAN_NOT_UPDATE_ADMIN_PASSWORD = 'Can not update admin password',
  SIGNED_UP_USING_YOUR_LOCAL_ACCOUNT = 'Signed up using your local account',
}
