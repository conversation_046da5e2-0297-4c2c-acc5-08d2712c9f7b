export interface Refund {
  id: string;
  userId: string;
  coachId: string;
  programId: string;
  subscriptionId: string;
  requestReason: string;
  contactMobileNumber: string;
  contactEmail: string;
  reviewStatus: string;
  reviewComment: string;
}

export interface Refunds {
  id: string;
  userId: string;
  coachId: string;
  programId: string;
  subscriptionId: string;
  requestReason: string;
  contactMobileNumber: string;
  contactEmail: string;
  reviewStatus: string;
  reviewComment: string;
}

export interface GetSingleRefundSuccessResponse {
  data: Refund;
}
  
export interface GetRefundsSuccessResponse {
  data: Refunds[];
}

export interface approveRejectRefundInterface { 
  reviewComment: string;
}
