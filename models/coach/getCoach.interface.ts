export interface GetCoachProfiles {
  id: string;
  userId: string;
  legalName: string;
  about: string;
  reviewStatus: string;
  reviewComment: string;
}

interface Media {
  mediaType: string;
  url: string;
}

export interface GetCoachProfile {
  id: string;
  userId: string;
  userEmail:string
  userName: string;
  legalName: string;
  profileName: string;
  coachCategoryId: string;
  coachCategoryName: string;
  expertise: string;
  highlights: string;
  skillLevel: string;
  experienceInYear: number;
  about: string;
  achievements: string[];
  media: Media[];
  credentials: Media[];
  identifications: Media[];
  reviewStatus: string;
  reviewComment: string;
}

export interface GetSingleCoachSuccessResponse {
  data: GetCoachProfile;
}

export interface GetMultipleCoachSuccessResponse {
  data: GetCoachProfiles[];
}

export interface CoachCategory {
  id?: string;
  title: string;
}

export interface GetCoachCategoriesSuccessResponse {
  data: CoachCategory[];
}

export interface GetSingleCoachCategorySuccessResponse {
  data: CoachCategory;
}

export interface DeleteCoachCategoryMessage {
  message: string;
}

export interface DeleteCoachCategorySuccessRes {
  data: DeleteCoachCategoryMessage;
}

export interface CoachSubCategory {
  id?: string;
  parentId: string;
  title: string;
  description: string;
  media: {
    type: string;
    url: string;
  }[];
  totalCoach?: number;
}

export interface GetCoachSubCategoriesSuccessResponse {
  data: CoachSubCategory[];
}

export interface GetSingleCoachSubCategorySuccessResponse {
  data: CoachSubCategory;
}

export interface DeleteCoachSubCategorySuccessRes {
  data: {
    message: string;
  };
}
