import { CoachCategory, CoachSubCategory } from './getCoach.interface';

export interface CreateCoachRequestBody {
  title: string;
}

export interface CreateCoachSuccessResponse {
  data: CoachCategory;
}

export interface CreateSubCategoryRequestBody {
  parentId: string;
  title: string;
  description: string;
  media: {
    type: string;
    url: string;
  }[];
}

export interface CreateCoachSubCategorySuccessResponse {
  data: CoachSubCategory;
}

export interface CoachCategoryMap {
  id: string;
  title: string;
  subCategories: {
    id: string;
    title: string;
    description: string;
    media: {
      type: string;
      url: string;
    }[];
    totalCoach: number;
  }[];
}

export interface GetCoachCategoryMapSuccessResponse {
  data: CoachCategoryMap[];
}
