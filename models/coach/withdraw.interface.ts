export interface Withdraw {
  id: string;
  mobileNumber: string;
  moneyWithdraw: number;
  coachId: string;
  coachName: string;
  reviewStatus: string;
  reviewComment: string;
  coachShare: number;
  fitsomniaShare: number;
}

export interface Withdraws {
  id: string;
  mobileNumber: string;
  moneyWithdraw: number;
  coachId: string;
  reviewStatus: string;
  reviewComment: string;
}

export interface GetSingleWithdrawSuccessResponse {
  data: Withdraw;
}

export interface GetMultipleWithdrawsSuccessResponse {
  data: Withdraws[];
}

export interface approveRejectWithdrawInterface {
  reviewComment: string;
  trxId?: string;
}

