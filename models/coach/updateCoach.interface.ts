import { CoachSubCategory } from './getCoach.interface';

export interface approveRejectCoachInterface {
  reviewComment: string;
}

export interface updateCoachCategoryRequestBody {
  title: string;
}

export interface UpdateCoachCategorySuccessResponse {
  data: {
    id: string;
    title: string;
  };
}

export interface UpdateCoachSubCategoryRequestBody {
  parentId?: string;
  title?: string;
  description?: string;
  media?: {
    type?: string;
    url?: string;
  }[];
}

export interface UpdateCoachSubCategorySuccessResponse {
  data: CoachSubCategory;
}
