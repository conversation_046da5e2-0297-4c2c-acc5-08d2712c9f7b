import { SuccessResponse } from '../common';
import { Event } from './getAllEvent.interface';

export interface IUpdateBaseEventReq {
  name?: string;
  description?: string;
  media?: string[];
  startDate?: Date;
  endDate?: Date;
  registrationFee?: number;
  amountInCurrency?: string;
  locationTitle?: string;
  actualLocation?: string;
  registrationStartTime?: Date;
  registrationEndTime?: Date;
  registrationEmailSubject?: string;
  registrationEmailBody?: string;
  unregistrationEmailSubject?: string;
  unregistrationEmailBody?: string;
}

export interface IUpdateEventSuccessRes extends SuccessResponse {
  data: Event;
}

export const enum UpdateEventErrorMessage {
  CAN_NOT_UPDATE_EVENT = 'Can not update event',
  CAN_NOT_FIND_EVENT = 'Can not find event',
}
