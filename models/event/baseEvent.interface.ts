export interface ICreateBaseEventReq {
  name: string;
  description: string;
  media: string[];
  startDate: Date;
  endDate: Date;
  registrationFee: number;
  amountInCurrency: string;
  locationTitle: string;
  actualLocation: string;
  registrationStartTime: Date;
  registrationEndTime: Date;
  registrationEmailSubject?: string;
  registrationEmailBody?: string;
  unregistrationEmailSubject?: string;
  unregistrationEmailBody?: string;
}

export interface ICreateBaseEventRes {
  id: string;
  name: string;
  description: string;
  media: string[];
  startDate: Date;
  endDate: Date;
  registrationFee: number;
  amountInCurrency: string;
  locationTitle: string;
  actualLocation: string;
  registrationStartTime: Date;
  registrationEndTime: Date;
  registrationEmailSubject?: string;
  registrationEmailBody?: string;
  unregistrationEmailSubject?: string;
  unregistrationEmailBody?: string;
}

export interface ICreateEventSuccessRes {
  data: ICreateBaseEventRes;
}
