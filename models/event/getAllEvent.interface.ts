export interface Event {
  id: string;
  name: string;
  description: string;
  media: string[];
  startDate: Date;
  endDate: Date;
  registrationFee: number;
  amountInCurrency: string;
  locationTitle: string;
  actualLocation: string;
  isRegistered?: boolean;
  registrationStartTime: Date;
  registrationEndTime: Date;
  registrationEmailSubject?: string;
  registrationEmailBody?: string;
  unregistrationEmailSubject?: string;
  unregistrationEmailBody?: string;
}

export interface EventUserDetails {
  id: string;
  name: string;
  phone: string;
  email: string;
  session: string;
  bkashNumber: string;
  trxId: string;
  studentId: string;
  jerseySize: string;
}

export interface GetEventUserDetailsSuccessResponse {
  data: EventUserDetails[];
}

export interface GetAllEventSuccessResponse {
  data: Event[];
}
