import { SuccessResponse } from '../common/index';
import { Address } from './address';
import { Admin } from './admin';

/**
 * API Path: /admin
 * method: PATCH
 * body: UpdatedAdminRequest
 * response: UpdateAdminSuccessResponse
 */

export interface UpdatedAdminRequest {
  firstName?: string;
  lastName?: string;
  provider?: string;
  providerData?: object;
  additionalProviderData?: object;
  phone?: string;
  address?: Address;
  active?: boolean;
  gender?: string;
  status?: string;
}

export interface UpdateAdminSuccessResponse extends SuccessResponse {
  data: Admin;
}

export const enum UpdateAdminErrorMessages {
  CAN_NOT_GET_ADMIN = 'Can not get admin',
  CAN_NOT_UPDATE_ADMIN_ADDRESS = 'Con not update admin address',
  CAN_NOT_ADD_ADMIN_NEW_ADDRESS = 'Can not add admin new address',
  CAN_NOT_UPDATE_ADMIN = 'Can not update admin',
}
