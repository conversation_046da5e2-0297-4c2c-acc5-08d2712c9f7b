import { SuccessResponse } from '../common/index';

/**
 * API Path: /admin/change-password
 * method: PATCH
 * body: ChangePasswordRequest
 * response: ChangePasswordSuccessResponse
 */

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export const enum ChangePasswordSuccessMessage {
  CHANGE_PASSWORD_SUCCESSFUL = 'Password changed successfully',
}

export interface ChangePasswordSuccessResponse extends SuccessResponse {
  data: {
    message?: ChangePasswordSuccessMessage;
  };
}

export const enum ChangePasswordErrorMessages {
  CAN_NOT_GET_ADMIN = 'Can not get admin',
  CURRENT_PASSWORD_IS_INCORRECT = 'Current password is incorrect',
  CAN_NOT_CHANGE_PASSWORD = 'Can not change password',
}
