import { SuccessResponse } from '../common/successResponse';

export interface DeleteCartRequest {
  cartId: string;
}

export const enum Message {
  REMOVE_CART_SUCCESSFULLY = 'Removed cart successfully',
}

export interface DeleteMessage {
  message: Message;
}

export interface DeleteCartSuccessResponse extends SuccessResponse {
  data: DeleteMessage;
}

export const enum DeleteCartErrorMessage {
  CAN_NOT_REMOVE_CART = 'Can not remove cart',
}
