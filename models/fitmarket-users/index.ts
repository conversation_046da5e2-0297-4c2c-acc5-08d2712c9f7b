import { SuccessResponse } from '../common/index';

/**
 * API Path: /users-list
 * method: GET
 * query: FindUsersListQuery
 * response: FindUsersListResponse
 */

export interface FindUsersListQuery {
  offset?: number;
  limit?: number;
  name?: string;
}

class UserImage {
  profile: string;
}

export class UserListUserInfo {
  id: string;
  name: string;
  image: UserImage;
}

export const enum FindUsersListErrorMessages {
  USERS_NOT_FOUND = 'Users not found',
}

export interface FindUsersListSuccessResponse extends SuccessResponse {
  data: UserListUserInfo[];
}
