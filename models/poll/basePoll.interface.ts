import { PollStatus, PollType } from './enums.poll.interface';

export interface IPollOption {
  id?: string;
  title: string;
  description: string;
  image: string;
  voteCount?: number;
}

export interface Poll {
  id: string;
  pollType: PollType;
  title: string;
  subTitle: string;
  status: PollStatus;
  isPinned: boolean;
  options: IPollOption[];
  description: string;
  image: string;
  expirationDate: Date;
  winnerCount: number;
}

export interface ICreateBasePollReq {
  pollType: PollType;
  title: string;
  subTitle: string;
  status: PollStatus;
  isPinned: boolean;
  options: IPollOption[];
  description: string;
  winnerCount: number;
  image: string;
  expirationDate: Date;
}

export interface ICreateBasePollRes {
  id: string;
  title: string;
  pollType: PollType;
  subTitle: string;
  status: PollStatus;
  isPinned: boolean;
  options: IPollOption[];
  description: string;
  image: string;
  expirationDate: Date;
  totalVotes: number;
}

export interface ICreateBasePollSuccessRes {
  data: ICreateBasePollRes;
}

export interface IGetAllPollsSuccessRes {
  data: ICreateBasePollRes[];
}

export interface IDeletePollMessage {
  message: string;
}

export interface IDeletePollSuccessRes {
  data: IDeletePollMessage;
}

export interface ISelectPollWinnerReq {
  pollId: string;
  pollOptionId: string | undefined;
  winnerCount: number;
}
