import { SuccessResponse } from '../common/successResponse';
import { IPollOption, Poll } from './basePoll.interface';
import { PollStatus, PollType } from './enums.poll.interface';

export interface UpdatePollRequestBody {
  pollType?: PollType;
  title?: string;
  subTitle?: string;
  status?: PollStatus;
  options?: IPollOption[];
  description?: string;
  image?: string;
  expirationDate?: Date;
}

export interface UpdatePollResponse extends SuccessResponse {
  data: Poll;
}

export const enum UpdatePollErrorMessage {
  CAN_NOT_UPDATE_POLL = 'Can not update poll',
  CAN_NOT_FIND_POLL = 'Can not find poll',
}
