import { SuccessResponse } from '../common/index';
import { User } from './user';

/**
 * API Path: /user/add-address
 * method: PUT
 * body: UserAddress
 * response: AddUserNewAddressResponse
 */

export interface AddUserNewAddressSuccessResponse extends SuccessResponse {
  data: User;
}

export const enum AddUserNewAddressErrorMessages {
  CAN_NOT_ADD_USER_NEW_ADDRESS = 'Can not add user new address',
}
