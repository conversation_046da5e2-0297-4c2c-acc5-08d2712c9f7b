import { SuccessResponse } from 'models/common';
import { DayType } from './enum.training.interface';
import { ITraining } from './training';

/**
 * API Path: /api/training-exercise/weekly-list
 * method: GET
 * query: GetWeeklyExerciseListQuery
 * response: GetWeeklyExerciseListResponse
 */

export interface GetWeeklyExerciseListParam {
  categoryId: string;
}
export interface GetWeeklyExerciseListQuery {
  week: number;
  day: DayType;
}

export interface GetWeeklyExerciseListSuccessResponse extends SuccessResponse {
  data: ITraining[];
}

export type GetWeeklyExerciseListResponse =
  GetWeeklyExerciseListSuccessResponse;
