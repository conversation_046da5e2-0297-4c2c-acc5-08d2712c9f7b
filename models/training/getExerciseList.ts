import { SuccessResponse } from 'models/common';
import { ITraining } from './training';

/**
 * API Path: /api/training/list
 * method: GET
 * query: GetExerciseListQuery
 * response: GetExerciseListResponse
 */

export enum QueryType {
  CATEGORY = 'CATEGORY',
  MUSCLE_GROUP = 'MUSCLE_GROUP',
}
export interface GetExerciseListQuery {
  type?: QueryType;
  name?: string;
}

export interface GetExerciseListSuccessResponse extends SuccessResponse {
  data: ITraining[];
}

export type GetExerciseListResponse = GetExerciseListSuccessResponse;
