import { SuccessResponse } from 'models/common';
import { ITraining } from './training';

/**
 * API Path: /api/training/{id}
 * method: DELETE
 * Param: DeleteTrainingParam
 * response: DeleteTrainingResponse
 */

export class DeleteTrainingParam {
  id: string;
}

export interface DeleteTrainingSuccessResponse extends SuccessResponse {
  data: ITraining;
}

export type DeleteTrainingResponse = DeleteTrainingSuccessResponse;
