import { SuccessResponse } from 'models/common';
import { DayType } from '../training';
import { IIntermediateTraining } from './intermediateTraining';

/**
 * API Path: /api/training/intermediate/list
 * method: GET
 * Query: GetIntermediateTrainingListQuery
 * response: GetIntermediateTrainingListResponse
 */
export interface GetIntermediateTrainingListQuery {
  programId: string;
  week: number;
  day: DayType;
}

export interface GetIntermediateTrainingListSuccessResponse
  extends SuccessResponse {
  data: IIntermediateTraining[];
}

export type GetIntermediateTrainingListResponse =
  GetIntermediateTrainingListSuccessResponse;
