import { SuccessResponse } from 'models/common';
import { IIntermediateTraining } from './intermediateTraining';
/**
 * API Path: /api/training/intermediate/{id}
 * method: GET
 * Query: GetIntermediateTrainingListQuery
 * response: GetIntermediateTrainingListResponse
 */

export interface GetIntermediateTrainingParam {
  id: string; // training id
}

export interface GetIntermediateTrainingSuccessResponse
  extends SuccessResponse {
  data: IIntermediateTraining;
}

export type GetIntermediateTrainingResponse =
  GetIntermediateTrainingSuccessResponse;
