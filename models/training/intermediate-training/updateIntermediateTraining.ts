import { SuccessResponse } from 'models/common';
import { BaseExercise, IDayType, IIntermediateTraining } from 'models';

/**
 * API Path: /api/training/intermediate/{id}
 * method: PATCH
 * body: UpdateIntermediateTrainingBody
 * response: UpdateIntermediateTrainingResponse
 */

export class UpdateIntermediateTrainingParam {
  id: string;
}

export class UpdateIntermediateTrainingBody implements BaseExercise {
  name: string;
  description?: string;
  mechanics?: string;
  type: string;
  category: string[];
  forceType?: string[];
  primaryMuscles: string[];
  secondaryMuscles?: string[];
  equipments?: string[];
  preview: string;
  week: number;
  day: IDayType;
  duration: number;
}

export interface UpdateIntermediateTrainingSuccessResponse
  extends SuccessResponse {
  data: IIntermediateTraining;
}

export type UpdateIntermediateTrainingResponse =
  UpdateIntermediateTrainingSuccessResponse;
