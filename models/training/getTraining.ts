import { SuccessResponse } from 'models/common';
import { ITraining } from './training';

/**
 * API Path: /api/training/{id}
 * method: GET
 * param: GetTrainingParam
 * response: GetTrainingResponse
 */

export interface GetTrainingParam {
  id: string;
}

export interface GetTrainingSuccessResponse extends SuccessResponse {
  data: ITraining;
}

export type GetTrainingResponse = GetTrainingSuccessResponse;
