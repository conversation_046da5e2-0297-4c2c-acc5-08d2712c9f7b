import { SuccessResponse } from '../common/index';
import { Manufacturer } from './manufacturer';

/**
 * API Path: /manufacturers/{manufacturerId}
 * method: GET
 * body: null
 * response: GetManufacturerResponse
 */

export const enum GetManufacturerErrorMessages {
  MANUFACTURER_NOT_FOUND = 'Manufacturer not found',
}

export const enum GetManufacturerSuccessMessages {
  MANUFACTURER_LOADED_SUCCESSFULLY = 'Manufacturer loaded successfully',
}

export interface GetManufacturerSuccessResponse extends SuccessResponse {
  data: {
    manufacturer: Manufacturer;
    message: GetManufacturerSuccessMessages;
  };
}
