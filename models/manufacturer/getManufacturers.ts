import { SuccessResponse } from '../common/index';
import { Manufacturer } from './manufacturer';

/**
 * API Path: /manufacturers?offset={...}&limit={...}
 * method: GET
 * body: null
 * response: GetManufacturersResponse
 */

export const enum GetManufacturersErrorMessages {
  MANUFACTURERS_NOT_FOUND = 'Manufacturers not found',
}

export const enum GetManufacturersSuccessMessages {
  MANUFACTURERS_LOADED_SUCCESSFULLY = 'Manufacturers loaded successfully',
  MANUFACTURER_IS_EMPTY = 'Manufacturer is empty',
}

export interface GetManufacturersQuery {
  offset?: number;
  limit?: number;
}

export interface GetManufacturersSuccessResponse extends SuccessResponse {
  data: {
    manufacturers: Manufacturer[];
    total: number;
    message: GetManufacturersSuccessMessages;
  };
}
