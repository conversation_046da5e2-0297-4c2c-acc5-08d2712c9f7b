import { SuccessResponse } from '../common/index';
import { Manufacturer } from './manufacturer';
import { ManufacturerSeo } from './manufacturerSeo';

/**
 * API Path: /manufacturer/create-manufacture
 * method: POST
 * body: createManufacturerRequest
 * response: CreateManufacturerResponse
 */

export interface CreateManufacturerRequest {
  name: string;
  description?: string;
  picture?: string;
  isPublished?: boolean;
  displayOrder?: number;
  seo?: ManufacturerSeo;
}

export const enum CreateManufacturerErrorMessages {
  MANUFACTURER_ALREADY_EXISTS = 'Manufacturer already exists',
  MANUFACTURER_NOT_CREATED_SUCCESSFULLY = 'Manufacturer not create successfully',
}

export const enum CreateManufacturerSuccessMessages {
  MANUFACTURER_CREATED_SUCCESSFULLY = 'Manufacturer created successfully',
}

export interface CreateManufacturerSuccessResponse extends SuccessResponse {
  data: {
    manufacturer: Manufacturer;
    message: CreateManufacturerSuccessMessages;
  };
}
