export interface IParcel {
  length?: number;
  width?: number;
  height?: number;
  weight: number;
}

export interface IWebhook {
  id: string;
  object: string;
  mode: string;
  url: string;
  created_at: Date;
  disabled_at?: null;
}

//easypost webhook
export interface IWebhookTrackerEvent {
  id: string;
  object: string;
  mode: string;
  tracking_code: string;
  status: string;
  status_detail: string;
  created_at: string;
  updated_at: string;
  signed_by?: any;
  weight?: any;
  est_delivery_date: string;
  shipment_id: string;
  carrier: string;
  tracking_details: Trackingdetail[];
  carrier_detail: Carrierdetail;
  finalized: boolean;
  is_return: boolean;
  public_url: string;
}

export interface Carrierdetail {
  object: string;
  service: string;
  container_type?: any;
  est_delivery_date_local?: any;
  est_delivery_time_local?: any;
  origin_location: string;
  origin_tracking_location: Origintrackinglocation;
  destination_location: string;
  destination_tracking_location?: any;
  guaranteed_delivery_date?: any;
  alternate_identifier?: any;
  initial_delivery_attempt?: any;
}

export interface Origintrackinglocation {
  object: string;
  city: string;
  state: string;
  country?: string;
  zip: string;
}

export interface Trackingdetail {
  object: string;
  message: string;
  description?: any;
  status: string;
  status_detail: string;
  datetime: string;
  source: string;
  carrier_code?: any;
  tracking_location: Origintrackinglocation[];
}

export interface TrackShipment {
  id: string;
  shippingId: string;
  orderId: string;
  rate: any;
  tracker: any;
}
