import { SuccessResponse } from '../common/index';
import { IOrderProduct } from './order.product.response.interface';

export const enum ErrorMessageReOrder {
  CANNOT_CREATE_CART = 'Can not create cart',
  INVALID_ID = 'Invalid id',
  OVERWRITE_CART = 'Overwrite cart', // YOUR ITEMS IN THE CART WILL BE REPLACED. DO YOU WANT TO CONTINUE?
  INVALID_ITEMS = 'Invalid items', //SOME PRODUCTS ARE NOT AVAILABLE. DO YOU WISH TO CONTINUE?
  CANNOT_CLEAR_CART = 'Can not clear cart',
  ALL_ITEMS_INVALID = 'All items invalid',
  CANNOT_ADD_ITEMS = 'Can not add items',
  CART_NOT_FOUND = 'Cart not found',
}
export interface ReOrderData {
  id?: string;
  userId?: string;
  products?: IOrderProduct[] | null;
  reDirectHome?: boolean;
  message?: string;
}
export interface ReOrderSuccessResponse extends SuccessResponse {
  data: ReOrderData;
}
