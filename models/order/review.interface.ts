import { PartialUserInfo } from 'models/user';

export class ReviewPhoto {
  url: string;
}
export class Review {
  id: string;
  productId: string;
  orderId: string;
  text?: string;
  image?: ReviewPhoto[];
  userId: string;
  rating: number;
}

export type CreateReviewRequest = Omit<Review, 'id' | 'userId'>;

export type CreateReviewResponse = { data: Review };

export type ReviewListWithUserInfo = Review & {
  userInfo: PartialUserInfo;
};
export type ReviewListResponse = { data: ReviewListWithUserInfo[] };

export type DeleteReviewListResponse = { data: string };

export interface GetProductReviewQuery {
  skip?: number;
  limit?: number;
}

export declare const enum ReviewErrorMessage {
  ALREADY_REVIEWED = 'Already reviewed',
  INVALID_PRODUCT_ID = 'Invalid product Id',
  CAN_NOT_ADD_REVIEW = 'Can not add review',
  INVALID_ORDER_ID = 'Invalid order Id',
  CAN_NOT_FIND_REVIEWS = 'Can not find reviews',
}
