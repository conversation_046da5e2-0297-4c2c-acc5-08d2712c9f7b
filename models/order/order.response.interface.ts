import { IOrderProduct } from './order.product.response.interface';

export interface IOrderAddress {
  firstName: string;
  lastName: string;
  email: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  country: string;
  postCode?: string;
  phone: string;
}

export interface BaseOrderModel {
  billingAddress: IOrderAddress;
  shippingAddress: IOrderAddress;
  paymentMethod: string;
  productCost: number;
  shippingCost: number;
}

export interface OrderResponseData extends BaseOrderModel {
  userId: string;
  orderId: string;
  orderedDate: Date;
  orderStatus: string;
  shippingStatus: string;
  paymentStatus: string;
  totalCost: number;
  products: IOrderProduct[];
}
