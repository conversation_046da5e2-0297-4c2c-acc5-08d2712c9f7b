import { SuccessResponse } from 'models/common';
import { About } from './about';

/**
 * API Path: /api/about
 * method: DELETE
 * response: DeleteAboutResponse
 */
export const enum DeleteAboutErrorMessage {
  CAN_NOT_DELETE_ABOUT_SECTION = 'Can not delete about section',
}

export interface DeleteAboutSuccessResponse extends SuccessResponse {
  data: About;
}
export type DeleteAboutResponse = DeleteAboutSuccessResponse;
