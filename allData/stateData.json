{"data": [{"country_code": "US", "postal_code": "99502", "place_name": "Anchorage", "admin_name1": "Alaska", "admin_code1": "AK", "admin_name2": "Anchorage Municipality", "admin_code2": "020", "admin_name3": null, "admin_code3": null, "latitude": 61.1661, "longitude": -149.96, "accuracy": 1, "coordinates": {"lon": -149.96, "lat": 61.1661}}, {"country_code": "US", "postal_code": "99695", "place_name": "Anchorage", "admin_name1": "Alaska", "admin_code1": "AK", "admin_name2": "Anchorage Municipality", "admin_code2": "020", "admin_name3": null, "admin_code3": null, "latitude": 61.1089, "longitude": -149.4403, "accuracy": 1, "coordinates": {"lon": -149.4403, "lat": 61.1089}}, {"country_code": "US", "postal_code": "99551", "place_name": "<PERSON><PERSON><PERSON><PERSON>", "admin_name1": "Alaska", "admin_code1": "AK", "admin_name2": "Bethel Census Area", "admin_code2": "050", "admin_name3": null, "admin_code3": null, "latitude": 60.9094, "longitude": -161.4314, "accuracy": 4, "coordinates": {"lon": -161.4314, "lat": 60.9094}}, {"country_code": "US", "postal_code": "99641", "place_name": "<PERSON><PERSON><PERSON><PERSON>", "admin_name1": "Alaska", "admin_code1": "AK", "admin_name2": "Bethel Census Area", "admin_code2": "050", "admin_name3": null, "admin_code3": null, "latitude": 60.8969, "longitude": -162.4594, "accuracy": 1, "coordinates": {"lon": -162.4594, "lat": 60.8969}}, {"country_code": "US", "postal_code": "99690", "place_name": "Nightmute", "admin_name1": "Alaska", "admin_code1": "AK", "admin_name2": "Bethel Census Area", "admin_code2": "050", "admin_name3": null, "admin_code3": null, "latitude": 60.4794, "longitude": -164.7239, "accuracy": 1, "coordinates": {"lon": -164.7239, "lat": 60.4794}}, {"country_code": "US", "postal_code": "99702", "place_name": "<PERSON><PERSON><PERSON> Afb", "admin_name1": "Alaska", "admin_code1": "AK", "admin_name2": "Fairbanks North Star", "admin_code2": "090", "admin_name3": null, "admin_code3": null, "latitude": 64.6735, "longitude": -147.0805, "accuracy": 1, "coordinates": {"lon": -147.0805, "lat": 64.6735}}, {"country_code": "US", "postal_code": "99625", "place_name": "Levelock", "admin_name1": "Alaska", "admin_code1": "AK", "admin_name2": "Lake and Peninsula", "admin_code2": "164", "admin_name3": null, "admin_code3": null, "latitude": 59.2905, "longitude": -156.6503, "accuracy": 1, "coordinates": {"lon": -156.6503, "lat": 59.2905}}, {"country_code": "US", "postal_code": "99659", "place_name": "<PERSON>", "admin_name1": "Alaska", "admin_code1": "AK", "admin_name2": "Nome (CA)", "admin_code2": "180", "admin_name3": null, "admin_code3": null, "latitude": 63.4776, "longitude": -162.1091, "accuracy": 1, "coordinates": {"lon": -162.1091, "lat": 63.4776}}, {"country_code": "US", "postal_code": "99918", "place_name": "Coffman Cove", "admin_name1": "Alaska", "admin_code1": "AK", "admin_name2": "Prince of Wales-<PERSON><PERSON>", "admin_code2": "198", "admin_name3": null, "admin_code3": null, "latitude": 55.9524, "longitude": -132.7503, "accuracy": 4, "coordinates": {"lon": -132.7503, "lat": 55.9524}}, {"country_code": "US", "postal_code": "99720", "place_name": "Allakaket", "admin_name1": "Alaska", "admin_code1": "AK", "admin_name2": "Yukon-<PERSON><PERSON><PERSON><PERSON> (CA)", "admin_code2": "290", "admin_name3": null, "admin_code3": null, "latitude": 66.5656, "longitude": -152.6456, "accuracy": 1, "coordinates": {"lon": -152.6456, "lat": 66.5656}}, {"country_code": "US", "postal_code": "36530", "place_name": "Elberta", "admin_name1": "Alabama", "admin_code1": "AL", "admin_name2": "<PERSON>", "admin_code2": "003", "admin_name3": null, "admin_code3": null, "latitude": 30.3942, "longitude": -87.589, "accuracy": 4, "coordinates": {"lon": -87.589, "lat": 30.3942}}, {"country_code": "US", "postal_code": "36536", "place_name": "<PERSON>", "admin_name1": "Alabama", "admin_code1": "AL", "admin_name2": "<PERSON>", "admin_code2": "003", "admin_name3": null, "admin_code3": null, "latitude": 30.4222, "longitude": -87.7064, "accuracy": 4, "coordinates": {"lon": -87.7064, "lat": 30.4222}}, {"country_code": "US", "postal_code": "35188", "place_name": "Woodstock", "admin_name1": "Alabama", "admin_code1": "AL", "admin_name2": "<PERSON><PERSON><PERSON>", "admin_code2": "007", "admin_name3": null, "admin_code3": null, "latitude": 33.2068, "longitude": -87.15, "accuracy": 4, "coordinates": {"lon": -87.15, "lat": 33.2068}}, {"country_code": "US", "postal_code": "36202", "place_name": "<PERSON><PERSON><PERSON>", "admin_name1": "Alabama", "admin_code1": "AL", "admin_name2": "<PERSON>", "admin_code2": "015", "admin_name3": null, "admin_code3": null, "latitude": 33.7622, "longitude": -85.8378, "accuracy": 4, "coordinates": {"lon": -85.8378, "lat": 33.7622}}, {"country_code": "US", "postal_code": "36028", "place_name": "<PERSON><PERSON>", "admin_name1": "Alabama", "admin_code1": "AL", "admin_name2": "<PERSON><PERSON><PERSON>", "admin_code2": "041", "admin_name3": null, "admin_code3": null, "latitude": 31.5066, "longitude": -86.3663, "accuracy": 4, "coordinates": {"lon": -86.3663, "lat": 31.5066}}, {"country_code": "US", "postal_code": "36374", "place_name": "Skipperville", "admin_name1": "Alabama", "admin_code1": "AL", "admin_name2": "<PERSON>", "admin_code2": "045", "admin_name3": null, "admin_code3": null, "latitude": 31.5748, "longitude": -85.537, "accuracy": 4, "coordinates": {"lon": -85.537, "lat": 31.5748}}, {"country_code": "US", "postal_code": "35022", "place_name": "Bessemer", "admin_name1": "Alabama", "admin_code1": "AL", "admin_name2": "<PERSON>", "admin_code2": "073", "admin_name3": null, "admin_code3": null, "latitude": 33.3224, "longitude": -86.9657, "accuracy": 4, "coordinates": {"lon": -86.9657, "lat": 33.3224}}, {"country_code": "US", "postal_code": "35064", "place_name": "Fairfield", "admin_name1": "Alabama", "admin_code1": "AL", "admin_name2": "<PERSON>", "admin_code2": "073", "admin_name3": null, "admin_code3": null, "latitude": 33.4735, "longitude": -86.9183, "accuracy": 4, "coordinates": {"lon": -86.9183, "lat": 33.4735}}, {"country_code": "US", "postal_code": "35207", "place_name": "Birmingham", "admin_name1": "Alabama", "admin_code1": "AL", "admin_name2": "<PERSON>", "admin_code2": "073", "admin_name3": null, "admin_code3": null, "latitude": 33.5594, "longitude": -86.8153, "accuracy": 4, "coordinates": {"lon": -86.8153, "lat": 33.5594}}, {"country_code": "US", "postal_code": "35211", "place_name": "Birmingham", "admin_name1": "Alabama", "admin_code1": "AL", "admin_name2": "<PERSON>", "admin_code2": "073", "admin_name3": null, "admin_code3": null, "latitude": 33.4816, "longitude": -86.859, "accuracy": 4, "coordinates": {"lon": -86.859, "lat": 33.4816}}, {"country_code": "US", "postal_code": "35292", "place_name": "Birmingham", "admin_name1": "Alabama", "admin_code1": "AL", "admin_name2": "<PERSON>", "admin_code2": "073", "admin_name3": null, "admin_code3": null, "latitude": 33.5207, "longitude": -86.8025, "accuracy": 4, "coordinates": {"lon": -86.8025, "lat": 33.5207}}, {"country_code": "US", "postal_code": "74055", "place_name": "<PERSON><PERSON><PERSON>", "admin_name1": "Oklahoma", "admin_code1": "OK", "admin_name2": "Tulsa", "admin_code2": "143", "admin_name3": null, "admin_code3": null, "latitude": 36.2863, "longitude": -95.8222, "accuracy": 4, "coordinates": {"lon": -95.8222, "lat": 36.2863}}, {"country_code": "US", "postal_code": "74117", "place_name": "Tulsa", "admin_name1": "Oklahoma", "admin_code1": "OK", "admin_name2": "Tulsa", "admin_code2": "143", "admin_name3": null, "admin_code3": null, "latitude": 36.2393, "longitude": -95.8979, "accuracy": 4, "coordinates": {"lon": -95.8979, "lat": 36.2393}}, {"country_code": "US", "postal_code": "74152", "place_name": "Tulsa", "admin_name1": "Oklahoma", "admin_code1": "OK", "admin_name2": "Tulsa", "admin_code2": "143", "admin_name3": null, "admin_code3": null, "latitude": 36.1398, "longitude": -96.0297, "accuracy": 4, "coordinates": {"lon": -96.0297, "lat": 36.1398}}, {"country_code": "US", "postal_code": "74014", "place_name": "Broken Arrow", "admin_name1": "Oklahoma", "admin_code1": "OK", "admin_name2": "<PERSON><PERSON><PERSON>", "admin_code2": "145", "admin_name3": null, "admin_code3": null, "latitude": 36.0544, "longitude": -95.7223, "accuracy": 4, "coordinates": {"lon": -95.7223, "lat": 36.0544}}, {"country_code": "US", "postal_code": "74454", "place_name": "<PERSON>", "admin_name1": "Oklahoma", "admin_code1": "OK", "admin_name2": "<PERSON><PERSON><PERSON>", "admin_code2": "145", "admin_name3": null, "admin_code3": null, "latitude": 35.8567, "longitude": -95.5082, "accuracy": 4, "coordinates": {"lon": -95.5082, "lat": 35.8567}}, {"country_code": "US", "postal_code": "73647", "place_name": "<PERSON><PERSON>", "admin_name1": "Oklahoma", "admin_code1": "OK", "admin_name2": "<PERSON><PERSON>", "admin_code2": "149", "admin_name3": null, "admin_code3": null, "latitude": 35.4545, "longitude": -99.1698, "accuracy": 4, "coordinates": {"lon": -99.1698, "lat": 35.4545}}, {"country_code": "US", "postal_code": "73842", "place_name": "Freedom", "admin_name1": "Oklahoma", "admin_code1": "OK", "admin_name2": "<PERSON>", "admin_code2": "151", "admin_name3": null, "admin_code3": null, "latitude": 36.809, "longitude": -99.1319, "accuracy": 4, "coordinates": {"lon": -99.1319, "lat": 36.809}}, {"country_code": "US", "postal_code": "73860", "place_name": "Waynoka", "admin_name1": "Oklahoma", "admin_code1": "OK", "admin_name2": "<PERSON>", "admin_code2": "151", "admin_name3": null, "admin_code3": null, "latitude": 36.5858, "longitude": -98.8487, "accuracy": 4, "coordinates": {"lon": -98.8487, "lat": 36.5858}}, {"country_code": "US", "postal_code": "73853", "place_name": "Mutual", "admin_name1": "Oklahoma", "admin_code1": "OK", "admin_name2": "<PERSON>", "admin_code2": "153", "admin_name3": null, "admin_code3": null, "latitude": 36.214, "longitude": -99.1145, "accuracy": 4, "coordinates": {"lon": -99.1145, "lat": 36.214}}, {"country_code": "US", "postal_code": "97459", "place_name": "North Bend", "admin_name1": "Oregon", "admin_code1": "OR", "admin_name2": "Coos", "admin_code2": "011", "admin_name3": null, "admin_code3": null, "latitude": 43.4327, "longitude": -124.2131, "accuracy": 4, "coordinates": {"lon": -124.2131, "lat": 43.4327}}, {"country_code": "US", "postal_code": "97464", "place_name": "<PERSON><PERSON>", "admin_name1": "Oregon", "admin_code1": "OR", "admin_name2": "<PERSON>", "admin_code2": "015", "admin_name3": null, "admin_code3": null, "latitude": 42.5632, "longitude": -124.3829, "accuracy": 4, "coordinates": {"lon": -124.3829, "lat": 42.5632}}, {"country_code": "US", "postal_code": "97467", "place_name": "Reedsport", "admin_name1": "Oregon", "admin_code1": "OR", "admin_name2": "<PERSON>", "admin_code2": "019", "admin_name3": null, "admin_code3": null, "latitude": 43.7023, "longitude": -124.0968, "accuracy": 4, "coordinates": {"lon": -124.0968, "lat": 43.7023}}, {"country_code": "US", "postal_code": "97499", "place_name": "<PERSON><PERSON><PERSON><PERSON>", "admin_name1": "Oregon", "admin_code1": "OR", "admin_name2": "<PERSON>", "admin_code2": "019", "admin_name3": null, "admin_code3": null, "latitude": 43.6043, "longitude": -123.2926, "accuracy": 4, "coordinates": {"lon": -123.2926, "lat": 43.6043}}, {"country_code": "US", "postal_code": "97825", "place_name": "Dayville", "admin_name1": "Oregon", "admin_code1": "OR", "admin_name2": "<PERSON>", "admin_code2": "023", "admin_name3": null, "admin_code3": null, "latitude": 44.4663, "longitude": -119.5312, "accuracy": 4, "coordinates": {"lon": -119.5312, "lat": 44.4663}}, {"country_code": "US", "postal_code": "97497", "place_name": "Wolf Creek", "admin_name1": "Oregon", "admin_code1": "OR", "admin_name2": "<PERSON>", "admin_code2": "033", "admin_name3": null, "admin_code3": null, "latitude": 42.6522, "longitude": -123.425, "accuracy": 4, "coordinates": {"lon": -123.425, "lat": 42.6522}}, {"country_code": "US", "postal_code": "97621", "place_name": "<PERSON><PERSON>", "admin_name1": "Oregon", "admin_code1": "OR", "admin_name2": "<PERSON><PERSON><PERSON>", "admin_code2": "035", "admin_name3": null, "admin_code3": null, "latitude": 42.4369, "longitude": -121.22, "accuracy": 4, "coordinates": {"lon": -121.22, "lat": 42.4369}}, {"country_code": "US", "postal_code": "97733", "place_name": "Crescent", "admin_name1": "Oregon", "admin_code1": "OR", "admin_name2": "<PERSON><PERSON><PERSON>", "admin_code2": "035", "admin_name3": null, "admin_code3": null, "latitude": 43.4442, "longitude": -121.7109, "accuracy": 4, "coordinates": {"lon": -121.7109, "lat": 43.4442}}, {"country_code": "US", "postal_code": "97430", "place_name": "Deadwood", "admin_name1": "Oregon", "admin_code1": "OR", "admin_name2": "Lane", "admin_code2": "039", "admin_name3": null, "admin_code3": null, "latitude": 44.1462, "longitude": -123.6816, "accuracy": 4, "coordinates": {"lon": -123.6816, "lat": 44.1462}}, {"country_code": "US", "postal_code": "97375", "place_name": "<PERSON><PERSON>", "admin_name1": "Oregon", "admin_code1": "OR", "admin_name2": "<PERSON>", "admin_code2": "047", "admin_name3": null, "admin_code3": null, "latitude": 44.9897, "longitude": -122.6187, "accuracy": 4, "coordinates": {"lon": -122.6187, "lat": 44.9897}}, {"country_code": "US", "postal_code": "97213", "place_name": "Portland", "admin_name1": "Oregon", "admin_code1": "OR", "admin_name2": "Multnomah", "admin_code2": "051", "admin_name3": null, "admin_code3": null, "latitude": 45.5373, "longitude": -122.5987, "accuracy": 4, "coordinates": {"lon": -122.5987, "lat": 45.5373}}, {"country_code": "US", "postal_code": "97065", "place_name": "Wasco", "admin_name1": "Oregon", "admin_code1": "OR", "admin_name2": "<PERSON>", "admin_code2": "055", "admin_name3": null, "admin_code3": null, "latitude": 45.5974, "longitude": -120.7304, "accuracy": 4, "coordinates": {"lon": -120.7304, "lat": 45.5974}}, {"country_code": "US", "postal_code": "97149", "place_name": "Neskowin", "admin_name1": "Oregon", "admin_code1": "OR", "admin_name2": "Tillamook", "admin_code2": "057", "admin_name3": null, "admin_code3": null, "latitude": 45.097, "longitude": -123.9434, "accuracy": 4, "coordinates": {"lon": -123.9434, "lat": 45.097}}, {"country_code": "US", "postal_code": "97810", "place_name": "<PERSON>", "admin_name1": "Oregon", "admin_code1": "OR", "admin_name2": "Umatilla", "admin_code2": "059", "admin_name3": null, "admin_code3": null, "latitude": 45.7674, "longitude": -118.5625, "accuracy": 4, "coordinates": {"lon": -118.5625, "lat": 45.7674}}, {"country_code": "US", "postal_code": "17372", "place_name": "York Springs", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON>", "admin_code2": "001", "admin_name3": null, "admin_code3": null, "latitude": 40.0084, "longitude": -77.1061, "accuracy": 4, "coordinates": {"lon": -77.1061, "lat": 40.0084}}, {"country_code": "US", "postal_code": "15096", "place_name": "Warrendale", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Allegheny", "admin_code2": "003", "admin_name3": null, "admin_code3": null, "latitude": 40.6534, "longitude": -80.0795, "accuracy": 4, "coordinates": {"lon": -80.0795, "lat": 40.6534}}, {"country_code": "US", "postal_code": "15133", "place_name": "Mckeesport", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Allegheny", "admin_code2": "003", "admin_name3": null, "admin_code3": null, "latitude": 40.3478, "longitude": -79.8642, "accuracy": 4, "coordinates": {"lon": -79.8642, "lat": 40.3478}}, {"country_code": "US", "postal_code": "15142", "place_name": "Presto", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Allegheny", "admin_code2": "003", "admin_name3": null, "admin_code3": null, "latitude": 40.3847, "longitude": -80.1209, "accuracy": 4, "coordinates": {"lon": -80.1209, "lat": 40.3847}}, {"country_code": "US", "postal_code": "15146", "place_name": "Monroeville", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Allegheny", "admin_code2": "003", "admin_name3": null, "admin_code3": null, "latitude": 40.429, "longitude": -79.7623, "accuracy": 4, "coordinates": {"lon": -79.7623, "lat": 40.429}}, {"country_code": "US", "postal_code": "15211", "place_name": "Pittsburgh", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Allegheny", "admin_code2": "003", "admin_name3": null, "admin_code3": null, "latitude": 40.4295, "longitude": -80.0144, "accuracy": 4, "coordinates": {"lon": -80.0144, "lat": 40.4295}}, {"country_code": "US", "postal_code": "15240", "place_name": "Pittsburgh", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Allegheny", "admin_code2": "003", "admin_name3": null, "admin_code3": null, "latitude": 40.4344, "longitude": -80.0248, "accuracy": 4, "coordinates": {"lon": -80.0248, "lat": 40.4344}}, {"country_code": "US", "postal_code": "15074", "place_name": "Rochester", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Beaver", "admin_code2": "007", "admin_name3": null, "admin_code3": null, "latitude": 40.7157, "longitude": -80.2603, "accuracy": 4, "coordinates": {"lon": -80.2603, "lat": 40.7157}}, {"country_code": "US", "postal_code": "16664", "place_name": "New Enterprise", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Bedford", "admin_code2": "009", "admin_name3": null, "admin_code3": null, "latitude": 40.2, "longitude": -78.4259, "accuracy": 4, "coordinates": {"lon": -78.4259, "lat": 40.2}}, {"country_code": "US", "postal_code": "19526", "place_name": "Hamburg", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Berks", "admin_code2": "011", "admin_name3": null, "admin_code3": null, "latitude": 40.5488, "longitude": -75.9874, "accuracy": 4, "coordinates": {"lon": -75.9874, "lat": 40.5488}}, {"country_code": "US", "postal_code": "19535", "place_name": "Limekiln", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Berks", "admin_code2": "011", "admin_name3": null, "admin_code3": null, "latitude": 40.3356, "longitude": -75.801, "accuracy": 4, "coordinates": {"lon": -75.801, "lat": 40.3356}}, {"country_code": "US", "postal_code": "16017", "place_name": "<PERSON><PERSON>", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON>", "admin_code2": "019", "admin_name3": null, "admin_code3": null, "latitude": 40.9211, "longitude": -79.9276, "accuracy": 4, "coordinates": {"lon": -79.9276, "lat": 40.9211}}, {"country_code": "US", "postal_code": "15955", "place_name": "<PERSON><PERSON>", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Cambria", "admin_code2": "021", "admin_name3": null, "admin_code3": null, "latitude": 40.3299, "longitude": -78.746, "accuracy": 4, "coordinates": {"lon": -78.746, "lat": 40.3299}}, {"country_code": "US", "postal_code": "16613", "place_name": "Ashville", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Cambria", "admin_code2": "021", "admin_name3": null, "admin_code3": null, "latitude": 40.5513, "longitude": -78.5346, "accuracy": 4, "coordinates": {"lon": -78.5346, "lat": 40.5513}}, {"country_code": "US", "postal_code": "18210", "place_name": "Albrightsville", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Carbon", "admin_code2": "025", "admin_name3": null, "admin_code3": null, "latitude": 40.9748, "longitude": -75.5842, "accuracy": 4, "coordinates": {"lon": -75.5842, "lat": 40.9748}}, {"country_code": "US", "postal_code": "18230", "place_name": "<PERSON><PERSON>", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Carbon", "admin_code2": "025", "admin_name3": null, "admin_code3": null, "latitude": 40.9234, "longitude": -75.9416, "accuracy": 4, "coordinates": {"lon": -75.9416, "lat": 40.9234}}, {"country_code": "US", "postal_code": "16841", "place_name": "<PERSON>", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Centre", "admin_code2": "027", "admin_name3": null, "admin_code3": null, "latitude": 41.0203, "longitude": -77.6702, "accuracy": 4, "coordinates": {"lon": -77.6702, "lat": 41.0203}}, {"country_code": "US", "postal_code": "19311", "place_name": "Avondale", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Chester", "admin_code2": "029", "admin_name3": null, "admin_code3": null, "latitude": 39.8219, "longitude": -75.7687, "accuracy": 4, "coordinates": {"lon": -75.7687, "lat": 39.8219}}, {"country_code": "US", "postal_code": "19320", "place_name": "Coatesville", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Chester", "admin_code2": "029", "admin_name3": null, "admin_code3": null, "latitude": 39.9843, "longitude": -75.8253, "accuracy": 4, "coordinates": {"lon": -75.8253, "lat": 39.9843}}, {"country_code": "US", "postal_code": "15801", "place_name": "<PERSON>", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Clearfield", "admin_code2": "033", "admin_name3": null, "admin_code3": null, "latitude": 41.126, "longitude": -78.7527, "accuracy": 1, "coordinates": {"lon": -78.7527, "lat": 41.126}}, {"country_code": "US", "postal_code": "16692", "place_name": "Westover", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Clearfield", "admin_code2": "033", "admin_name3": null, "admin_code3": null, "latitude": 40.7615, "longitude": -78.7355, "accuracy": 4, "coordinates": {"lon": -78.7355, "lat": 40.7615}}, {"country_code": "US", "postal_code": "16833", "place_name": "Curwensville", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Clearfield", "admin_code2": "033", "admin_name3": null, "admin_code3": null, "latitude": 40.966, "longitude": -78.5272, "accuracy": 4, "coordinates": {"lon": -78.5272, "lat": 40.966}}, {"country_code": "US", "postal_code": "17751", "place_name": "Mill Hall", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON>", "admin_code2": "035", "admin_name3": null, "admin_code3": null, "latitude": 41.0867, "longitude": -77.4836, "accuracy": 4, "coordinates": {"lon": -77.4836, "lat": 41.0867}}, {"country_code": "US", "postal_code": "17764", "place_name": "Renovo", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON>", "admin_code2": "035", "admin_name3": null, "admin_code3": null, "latitude": 41.3334, "longitude": -77.7448, "accuracy": 4, "coordinates": {"lon": -77.7448, "lat": 41.3334}}, {"country_code": "US", "postal_code": "16388", "place_name": "Meadville", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON>", "admin_code2": "039", "admin_name3": null, "admin_code3": null, "latitude": 41.6596, "longitude": -80.1576, "accuracy": 4, "coordinates": {"lon": -80.1576, "lat": 41.6596}}, {"country_code": "US", "postal_code": "17015", "place_name": "Carlisle", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Cumberland", "admin_code2": "041", "admin_name3": null, "admin_code3": null, "latitude": 40.1772, "longitude": -77.2312, "accuracy": 4, "coordinates": {"lon": -77.2312, "lat": 40.1772}}, {"country_code": "US", "postal_code": "17033", "place_name": "<PERSON><PERSON><PERSON>", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON><PERSON><PERSON>", "admin_code2": "043", "admin_name3": null, "admin_code3": null, "latitude": 40.2638, "longitude": -76.6545, "accuracy": 4, "coordinates": {"lon": -76.6545, "lat": 40.2638}}, {"country_code": "US", "postal_code": "17107", "place_name": "Harrisburg", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON><PERSON><PERSON>", "admin_code2": "043", "admin_name3": null, "admin_code3": null, "latitude": 40.297, "longitude": -76.8764, "accuracy": 4, "coordinates": {"lon": -76.8764, "lat": 40.297}}, {"country_code": "US", "postal_code": "19029", "place_name": "<PERSON><PERSON><PERSON>", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Delaware", "admin_code2": "045", "admin_name3": null, "admin_code3": null, "latitude": 39.8621, "longitude": -75.2971, "accuracy": 4, "coordinates": {"lon": -75.2971, "lat": 39.8621}}, {"country_code": "US", "postal_code": "19037", "place_name": "<PERSON>", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Delaware", "admin_code2": "045", "admin_name3": null, "admin_code3": null, "latitude": 39.934, "longitude": -75.406, "accuracy": 1, "coordinates": {"lon": -75.406, "lat": 39.934}}, {"country_code": "US", "postal_code": "19043", "place_name": "<PERSON>", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Delaware", "admin_code2": "045", "admin_name3": null, "admin_code3": null, "latitude": 39.9003, "longitude": -75.3087, "accuracy": 4, "coordinates": {"lon": -75.3087, "lat": 39.9003}}, {"country_code": "US", "postal_code": "19078", "place_name": "Ridley Park", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Delaware", "admin_code2": "045", "admin_name3": null, "admin_code3": null, "latitude": 39.8784, "longitude": -75.3215, "accuracy": 4, "coordinates": {"lon": -75.3215, "lat": 39.8784}}, {"country_code": "US", "postal_code": "19085", "place_name": "Villanova", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Delaware", "admin_code2": "045", "admin_name3": null, "admin_code3": null, "latitude": 40.0399, "longitude": -75.3459, "accuracy": 4, "coordinates": {"lon": -75.3459, "lat": 40.0399}}, {"country_code": "US", "postal_code": "16546", "place_name": "Erie", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Erie", "admin_code2": "049", "admin_name3": null, "admin_code3": null, "latitude": 42.1827, "longitude": -80.0649, "accuracy": 4, "coordinates": {"lon": -80.0649, "lat": 42.1827}}, {"country_code": "US", "postal_code": "15847", "place_name": "<PERSON>", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON>", "admin_code2": "065", "admin_name3": null, "admin_code3": null, "latitude": 41.0862, "longitude": -79.0264, "accuracy": 4, "coordinates": {"lon": -79.0264, "lat": 41.0862}}, {"country_code": "US", "postal_code": "17021", "place_name": "East Waterford", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON><PERSON><PERSON>", "admin_code2": "067", "admin_name3": null, "admin_code3": null, "latitude": 40.3542, "longitude": -77.6528, "accuracy": 4, "coordinates": {"lon": -77.6528, "lat": 40.3542}}, {"country_code": "US", "postal_code": "17086", "place_name": "Richfield", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON><PERSON><PERSON>", "admin_code2": "067", "admin_name3": null, "admin_code3": null, "latitude": 40.6884, "longitude": -77.1223, "accuracy": 4, "coordinates": {"lon": -77.1223, "lat": 40.6884}}, {"country_code": "US", "postal_code": "18510", "place_name": "Scranton", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Lackawanna", "admin_code2": "069", "admin_name3": null, "admin_code3": null, "latitude": 41.408, "longitude": -75.6484, "accuracy": 4, "coordinates": {"lon": -75.6484, "lat": 41.408}}, {"country_code": "US", "postal_code": "17521", "place_name": "Elm", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Lancaster", "admin_code2": "071", "admin_name3": null, "admin_code3": null, "latitude": 40.2044, "longitude": -76.3464, "accuracy": 4, "coordinates": {"lon": -76.3464, "lat": 40.2044}}, {"country_code": "US", "postal_code": "17527", "place_name": "Gap", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Lancaster", "admin_code2": "071", "admin_name3": null, "admin_code3": null, "latitude": 40.002, "longitude": -75.9978, "accuracy": 4, "coordinates": {"lon": -75.9978, "lat": 40.002}}, {"country_code": "US", "postal_code": "17602", "place_name": "Lancaster", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Lancaster", "admin_code2": "071", "admin_name3": null, "admin_code3": null, "latitude": 40.0335, "longitude": -76.2844, "accuracy": 4, "coordinates": {"lon": -76.2844, "lat": 40.0335}}, {"country_code": "US", "postal_code": "16142", "place_name": "New Wilmington", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON>", "admin_code2": "073", "admin_name3": null, "admin_code3": null, "latitude": 41.1382, "longitude": -80.3245, "accuracy": 4, "coordinates": {"lon": -80.3245, "lat": 41.1382}}, {"country_code": "US", "postal_code": "17087", "place_name": "Richland", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Lebanon", "admin_code2": "075", "admin_name3": null, "admin_code3": null, "latitude": 40.3806, "longitude": -76.2654, "accuracy": 4, "coordinates": {"lon": -76.2654, "lat": 40.3806}}, {"country_code": "US", "postal_code": "18060", "place_name": "Limeport", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Lehigh", "admin_code2": "077", "admin_name3": null, "admin_code3": null, "latitude": 40.509, "longitude": -75.4471, "accuracy": 4, "coordinates": {"lon": -75.4471, "lat": 40.509}}, {"country_code": "US", "postal_code": "16725", "place_name": "Custer City", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON><PERSON><PERSON><PERSON>", "admin_code2": "083", "admin_name3": null, "admin_code3": null, "latitude": 41.9059, "longitude": -78.6517, "accuracy": 4, "coordinates": {"lon": -78.6517, "lat": 41.9059}}, {"country_code": "US", "postal_code": "16730", "place_name": "East Smethport", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON><PERSON><PERSON><PERSON>", "admin_code2": "083", "admin_name3": null, "admin_code3": null, "latitude": 41.8087, "longitude": -78.4195, "accuracy": 4, "coordinates": {"lon": -78.4195, "lat": 41.8087}}, {"country_code": "US", "postal_code": "16743", "place_name": "Port Allegany", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON><PERSON><PERSON><PERSON>", "admin_code2": "083", "admin_name3": null, "admin_code3": null, "latitude": 41.8169, "longitude": -78.2799, "accuracy": 4, "coordinates": {"lon": -78.2799, "lat": 41.8169}}, {"country_code": "US", "postal_code": "18347", "place_name": "Pocono Lake", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON>", "admin_code2": "089", "admin_name3": null, "admin_code3": null, "latitude": 41.1187, "longitude": -75.5559, "accuracy": 4, "coordinates": {"lon": -75.5559, "lat": 41.1187}}, {"country_code": "US", "postal_code": "19044", "place_name": "<PERSON><PERSON><PERSON>", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON>", "admin_code2": "091", "admin_name3": null, "admin_code3": null, "latitude": 40.1821, "longitude": -75.1479, "accuracy": 4, "coordinates": {"lon": -75.1479, "lat": 40.1821}}, {"country_code": "US", "postal_code": "19095", "place_name": "Wyncote", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON>", "admin_code2": "091", "admin_name3": null, "admin_code3": null, "latitude": 40.0867, "longitude": -75.1524, "accuracy": 4, "coordinates": {"lon": -75.1524, "lat": 40.0867}}, {"country_code": "US", "postal_code": "19430", "place_name": "Creamery", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON>", "admin_code2": "091", "admin_name3": null, "admin_code3": null, "latitude": 40.1851, "longitude": -75.42, "accuracy": 4, "coordinates": {"lon": -75.42, "lat": 40.1851}}, {"country_code": "US", "postal_code": "19435", "place_name": "<PERSON>", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON>", "admin_code2": "091", "admin_name3": null, "admin_code3": null, "latitude": 40.3275, "longitude": -75.5692, "accuracy": 4, "coordinates": {"lon": -75.5692, "lat": 40.3275}}, {"country_code": "US", "postal_code": "19455", "place_name": "North Wales", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON>", "admin_code2": "091", "admin_name3": null, "admin_code3": null, "latitude": 40.2109, "longitude": -75.2782, "accuracy": 4, "coordinates": {"lon": -75.2782, "lat": 40.2109}}, {"country_code": "US", "postal_code": "18055", "place_name": "Hellertown", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Northampton", "admin_code2": "095", "admin_name3": null, "admin_code3": null, "latitude": 40.5817, "longitude": -75.3255, "accuracy": 4, "coordinates": {"lon": -75.3255, "lat": 40.5817}}, {"country_code": "US", "postal_code": "18088", "place_name": "Walnutport", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Northampton", "admin_code2": "095", "admin_name3": null, "admin_code3": null, "latitude": 40.7615, "longitude": -75.5657, "accuracy": 4, "coordinates": {"lon": -75.5657, "lat": 40.7615}}, {"country_code": "US", "postal_code": "19130", "place_name": "Philadelphia", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Philadelphia", "admin_code2": "101", "admin_name3": null, "admin_code3": null, "latitude": 39.9677, "longitude": -75.1735, "accuracy": 4, "coordinates": {"lon": -75.1735, "lat": 39.9677}}, {"country_code": "US", "postal_code": "19131", "place_name": "Philadelphia", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Philadelphia", "admin_code2": "101", "admin_name3": null, "admin_code3": null, "latitude": 39.9845, "longitude": -75.2282, "accuracy": 4, "coordinates": {"lon": -75.2282, "lat": 39.9845}}, {"country_code": "US", "postal_code": "19136", "place_name": "Philadelphia", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Philadelphia", "admin_code2": "101", "admin_name3": null, "admin_code3": null, "latitude": 40.0422, "longitude": -75.0244, "accuracy": 4, "coordinates": {"lon": -75.0244, "lat": 40.0422}}, {"country_code": "US", "postal_code": "19175", "place_name": "Philadelphia", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Philadelphia", "admin_code2": "101", "admin_name3": null, "admin_code3": null, "latitude": 39.9906, "longitude": -75.1296, "accuracy": 4, "coordinates": {"lon": -75.1296, "lat": 39.9906}}, {"country_code": "US", "postal_code": "18371", "place_name": "Tamiment", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Pike", "admin_code2": "103", "admin_name3": null, "admin_code3": null, "latitude": 41.1423, "longitude": -75.0271, "accuracy": 4, "coordinates": {"lon": -75.0271, "lat": 41.1423}}, {"country_code": "US", "postal_code": "16915", "place_name": "Coudersport", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON>", "admin_code2": "105", "admin_name3": null, "admin_code3": null, "latitude": 41.7762, "longitude": -77.9567, "accuracy": 4, "coordinates": {"lon": -77.9567, "lat": 41.7762}}, {"country_code": "US", "postal_code": "17930", "place_name": "Cumbola", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Schuylkill", "admin_code2": "107", "admin_name3": null, "admin_code3": null, "latitude": 40.7114, "longitude": -76.1392, "accuracy": 4, "coordinates": {"lon": -76.1392, "lat": 40.7114}}, {"country_code": "US", "postal_code": "17932", "place_name": "Frackville", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Schuylkill", "admin_code2": "107", "admin_name3": null, "admin_code3": null, "latitude": 40.784, "longitude": -76.2302, "accuracy": 4, "coordinates": {"lon": -76.2302, "lat": 40.784}}, {"country_code": "US", "postal_code": "15502", "place_name": "Hidden Valley", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Somerset", "admin_code2": "111", "admin_name3": null, "admin_code3": null, "latitude": 40.046, "longitude": -79.2585, "accuracy": 1, "coordinates": {"lon": -79.2585, "lat": 40.046}}, {"country_code": "US", "postal_code": "18816", "place_name": "<PERSON><PERSON><PERSON>", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Susquehanna", "admin_code2": "115", "admin_name3": null, "admin_code3": null, "latitude": 41.7465, "longitude": -75.8983, "accuracy": 4, "coordinates": {"lon": -75.8983, "lat": 41.7465}}, {"country_code": "US", "postal_code": "16932", "place_name": "Mainesburg", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Tioga", "admin_code2": "117", "admin_name3": null, "admin_code3": null, "latitude": 41.784, "longitude": -76.9983, "accuracy": 4, "coordinates": {"lon": -76.9983, "lat": 41.784}}, {"country_code": "US", "postal_code": "15340", "place_name": "<PERSON><PERSON><PERSON>", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Washington", "admin_code2": "125", "admin_name3": null, "admin_code3": null, "latitude": 40.2925, "longitude": -80.3025, "accuracy": 4, "coordinates": {"lon": -80.3025, "lat": 40.2925}}, {"country_code": "US", "postal_code": "15347", "place_name": "Meadow Lands", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Washington", "admin_code2": "125", "admin_name3": null, "admin_code3": null, "latitude": 40.2174, "longitude": -80.2269, "accuracy": 4, "coordinates": {"lon": -80.2269, "lat": 40.2174}}, {"country_code": "US", "postal_code": "18431", "place_name": "Honesdale", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "<PERSON>", "admin_code2": "127", "admin_name3": null, "admin_code3": null, "latitude": 41.5792, "longitude": -75.2528, "accuracy": 4, "coordinates": {"lon": -75.2528, "lat": 41.5792}}, {"country_code": "US", "postal_code": "15624", "place_name": "<PERSON><PERSON><PERSON>", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Westmoreland", "admin_code2": "129", "admin_name3": null, "admin_code3": null, "latitude": 40.3623, "longitude": -79.4706, "accuracy": 4, "coordinates": {"lon": -79.4706, "lat": 40.3623}}, {"country_code": "US", "postal_code": "15641", "place_name": "Hyde Park", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Westmoreland", "admin_code2": "129", "admin_name3": null, "admin_code3": null, "latitude": 40.6311, "longitude": -79.5899, "accuracy": 4, "coordinates": {"lon": -79.5899, "lat": 40.6311}}, {"country_code": "US", "postal_code": "15655", "place_name": "Laughlintown", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Westmoreland", "admin_code2": "129", "admin_name3": null, "admin_code3": null, "latitude": 40.212, "longitude": -79.1978, "accuracy": 4, "coordinates": {"lon": -79.1978, "lat": 40.212}}, {"country_code": "US", "postal_code": "15675", "place_name": "Penn", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "Westmoreland", "admin_code2": "129", "admin_name3": null, "admin_code3": null, "latitude": 40.3301, "longitude": -79.6413, "accuracy": 4, "coordinates": {"lon": -79.6413, "lat": 40.3301}}, {"country_code": "US", "postal_code": "17356", "place_name": "Red Lion", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "York", "admin_code2": "133", "admin_name3": null, "admin_code3": null, "latitude": 39.9026, "longitude": -76.6081, "accuracy": 4, "coordinates": {"lon": -76.6081, "lat": 39.9026}}, {"country_code": "US", "postal_code": "17405", "place_name": "York", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "York", "admin_code2": "133", "admin_name3": null, "admin_code3": null, "latitude": 40.0086, "longitude": -76.5972, "accuracy": 4, "coordinates": {"lon": -76.5972, "lat": 40.0086}}, {"country_code": "US", "postal_code": "17408", "place_name": "York", "admin_name1": "Pennsylvania", "admin_code1": "PA", "admin_name2": "York", "admin_code2": "133", "admin_name3": null, "admin_code3": null, "latitude": 39.9492, "longitude": -76.8018, "accuracy": 4, "coordinates": {"lon": -76.8018, "lat": 39.9492}}, {"country_code": "US", "postal_code": "02904", "place_name": "Providence", "admin_name1": "Rhode Island", "admin_code1": "RI", "admin_name2": "Providence", "admin_code2": "007", "admin_name3": null, "admin_code3": null, "latitude": 41.8541, "longitude": -71.4378, "accuracy": 4, "coordinates": {"lon": -71.4378, "lat": 41.8541}}, {"country_code": "US", "postal_code": "02914", "place_name": "East Providence", "admin_name1": "Rhode Island", "admin_code1": "RI", "admin_name2": "Providence", "admin_code2": "007", "admin_name3": null, "admin_code3": null, "latitude": 41.8138, "longitude": -71.3688, "accuracy": 4, "coordinates": {"lon": -71.3688, "lat": 41.8138}}, {"country_code": "US", "postal_code": "02822", "place_name": "Exeter", "admin_name1": "Rhode Island", "admin_code1": "RI", "admin_name2": "Washington", "admin_code2": "009", "admin_name3": null, "admin_code3": null, "latitude": 41.574, "longitude": -71.6076, "accuracy": 4, "coordinates": {"lon": -71.6076, "lat": 41.574}}, {"country_code": "US", "postal_code": "29834", "place_name": "Langley", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "Aiken", "admin_code2": "003", "admin_name3": null, "admin_code3": null, "latitude": 33.5179, "longitude": -81.844, "accuracy": 4, "coordinates": {"lon": -81.844, "lat": 33.5179}}, {"country_code": "US", "postal_code": "29850", "place_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "Aiken", "admin_code2": "003", "admin_name3": null, "admin_code3": null, "latitude": 33.6126, "longitude": -81.8098, "accuracy": 4, "coordinates": {"lon": -81.8098, "lat": 33.6126}}, {"country_code": "US", "postal_code": "29684", "place_name": "<PERSON>", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "<PERSON>", "admin_code2": "007", "admin_name3": null, "admin_code3": null, "latitude": 34.3962, "longitude": -82.6897, "accuracy": 4, "coordinates": {"lon": -82.6897, "lat": 34.3962}}, {"country_code": "US", "postal_code": "29412", "place_name": "Charleston", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "Charleston", "admin_code2": "019", "admin_name3": null, "admin_code3": null, "latitude": 32.718, "longitude": -79.9537, "accuracy": 4, "coordinates": {"lon": -79.9537, "lat": 32.718}}, {"country_code": "US", "postal_code": "29435", "place_name": "Cottageville", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "<PERSON><PERSON>", "admin_code2": "029", "admin_name3": null, "admin_code3": null, "latitude": 32.9612, "longitude": -80.4794, "accuracy": 4, "coordinates": {"lon": -80.4794, "lat": 32.9612}}, {"country_code": "US", "postal_code": "29471", "place_name": "Reevesville", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "Dorchester", "admin_code2": "035", "admin_name3": null, "admin_code3": null, "latitude": 33.1872, "longitude": -80.6672, "accuracy": 4, "coordinates": {"lon": -80.6672, "lat": 33.1872}}, {"country_code": "US", "postal_code": "29483", "place_name": "Summerville", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "Dorchester", "admin_code2": "035", "admin_name3": null, "admin_code3": null, "latitude": 33.028, "longitude": -80.1739, "accuracy": 4, "coordinates": {"lon": -80.1739, "lat": 33.028}}, {"country_code": "US", "postal_code": "29502", "place_name": "Florence", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "Florence", "admin_code2": "041", "admin_name3": null, "admin_code3": null, "latitude": 34.201, "longitude": -79.7847, "accuracy": 4, "coordinates": {"lon": -79.7847, "lat": 34.201}}, {"country_code": "US", "postal_code": "29555", "place_name": "Johnsonville", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "Florence", "admin_code2": "041", "admin_name3": null, "admin_code3": null, "latitude": 33.8299, "longitude": -79.4783, "accuracy": 4, "coordinates": {"lon": -79.4783, "lat": 33.8299}}, {"country_code": "US", "postal_code": "29617", "place_name": "Greenville", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "Greenville", "admin_code2": "045", "admin_name3": null, "admin_code3": null, "latitude": 34.912, "longitude": -82.4666, "accuracy": 4, "coordinates": {"lon": -82.4666, "lat": 34.912}}, {"country_code": "US", "postal_code": "29045", "place_name": "Elgin", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "<PERSON><PERSON><PERSON>", "admin_code2": "055", "admin_name3": null, "admin_code3": null, "latitude": 34.162, "longitude": -80.8113, "accuracy": 4, "coordinates": {"lon": -80.8113, "lat": 34.162}}, {"country_code": "US", "postal_code": "29707", "place_name": "Fort Mill", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "Lancaster", "admin_code2": "057", "admin_name3": null, "admin_code3": null, "latitude": 34.9773, "longitude": -80.8584, "accuracy": 4, "coordinates": {"lon": -80.8584, "lat": 34.9773}}, {"country_code": "US", "postal_code": "29722", "place_name": "Lancaster", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "Lancaster", "admin_code2": "057", "admin_name3": null, "admin_code3": null, "latitude": 34.7673, "longitude": -80.6589, "accuracy": 4, "coordinates": {"lon": -80.6589, "lat": 34.7673}}, {"country_code": "US", "postal_code": "29845", "place_name": "Plum Branch", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "<PERSON>", "admin_code2": "065", "admin_name3": null, "admin_code3": null, "latitude": 33.8329, "longitude": -82.248, "accuracy": 4, "coordinates": {"lon": -82.248, "lat": 33.8329}}, {"country_code": "US", "postal_code": "29037", "place_name": "<PERSON><PERSON><PERSON>", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "Newberry", "admin_code2": "071", "admin_name3": null, "admin_code3": null, "latitude": 34.1856, "longitude": -81.8644, "accuracy": 4, "coordinates": {"lon": -81.8644, "lat": 34.1856}}, {"country_code": "US", "postal_code": "29112", "place_name": "North", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "Orangeburg", "admin_code2": "075", "admin_name3": null, "admin_code3": null, "latitude": 33.6211, "longitude": -81.0601, "accuracy": 4, "coordinates": {"lon": -81.0601, "lat": 33.6211}}, {"country_code": "US", "postal_code": "29657", "place_name": "Liberty", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "<PERSON>ens", "admin_code2": "077", "admin_name3": null, "admin_code3": null, "latitude": 34.7872, "longitude": -82.6974, "accuracy": 4, "coordinates": {"lon": -82.6974, "lat": 34.7872}}, {"country_code": "US", "postal_code": "29201", "place_name": "Columbia", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "Richland", "admin_code2": "079", "admin_name3": null, "admin_code3": null, "latitude": 34.0004, "longitude": -81.0334, "accuracy": 4, "coordinates": {"lon": -81.0334, "lat": 34.0004}}, {"country_code": "US", "postal_code": "29204", "place_name": "Columbia", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "Richland", "admin_code2": "079", "admin_name3": null, "admin_code3": null, "latitude": 34.026, "longitude": -81.0046, "accuracy": 4, "coordinates": {"lon": -81.0046, "lat": 34.026}}, {"country_code": "US", "postal_code": "29386", "place_name": "White Stone", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "Spartanburg", "admin_code2": "083", "admin_name3": null, "admin_code3": null, "latitude": 34.8882, "longitude": -81.969, "accuracy": 4, "coordinates": {"lon": -81.969, "lat": 34.8882}}, {"country_code": "US", "postal_code": "29745", "place_name": "York", "admin_name1": "South Carolina", "admin_code1": "SC", "admin_name2": "York", "admin_code2": "091", "admin_name3": null, "admin_code3": null, "latitude": 34.9947, "longitude": -81.2245, "accuracy": 4, "coordinates": {"lon": -81.2245, "lat": 34.9947}}, {"country_code": "US", "postal_code": "57426", "place_name": "<PERSON>", "admin_name1": "South Dakota", "admin_code1": "SD", "admin_name2": "<PERSON>", "admin_code2": "013", "admin_name3": null, "admin_code3": null, "latitude": 45.7325, "longitude": -98.4965, "accuracy": 4, "coordinates": {"lon": -98.4965, "lat": 45.7325}}, {"country_code": "US", "postal_code": "57441", "place_name": "<PERSON>", "admin_name1": "South Dakota", "admin_code1": "SD", "admin_name2": "<PERSON>", "admin_code2": "013", "admin_name3": null, "admin_code3": null, "latitude": 45.8493, "longitude": -98.5176, "accuracy": 4, "coordinates": {"lon": -98.5176, "lat": 45.8493}}, {"country_code": "US", "postal_code": "57339", "place_name": "Fort Thompson", "admin_name1": "South Dakota", "admin_code1": "SD", "admin_name2": "Buffalo", "admin_code2": "017", "admin_name3": null, "admin_code3": null, "latitude": 44.0517, "longitude": -99.3973, "accuracy": 4, "coordinates": {"lon": -99.3973, "lat": 44.0517}}, {"country_code": "US", "postal_code": "57648", "place_name": "Pollock", "admin_name1": "South Dakota", "admin_code1": "SD", "admin_name2": "<PERSON>", "admin_code2": "021", "admin_name3": null, "admin_code3": null, "latitude": 45.889, "longitude": -100.2875, "accuracy": 4, "coordinates": {"lon": -100.2875, "lat": 45.889}}, {"country_code": "US", "postal_code": "57245", "place_name": "Kranzburg", "admin_name1": "South Dakota", "admin_code1": "SD", "admin_name2": "Codington", "admin_code2": "029", "admin_name3": null, "admin_code3": null, "latitude": 44.8923, "longitude": -96.9174, "accuracy": 4, "coordinates": {"lon": -96.9174, "lat": 44.8923}}, {"country_code": "US", "postal_code": "57773", "place_name": "<PERSON><PERSON><PERSON>", "admin_name1": "South Dakota", "admin_code1": "SD", "admin_name2": "<PERSON><PERSON>", "admin_code2": "033", "admin_name3": null, "admin_code3": null, "latitude": 43.6086, "longitude": -103.5938, "accuracy": 4, "coordinates": {"lon": -103.5938, "lat": 43.6086}}, {"country_code": "US", "postal_code": "57422", "place_name": "Andover", "admin_name1": "South Dakota", "admin_code1": "SD", "admin_name2": "Day", "admin_code2": "037", "admin_name3": null, "admin_code3": null, "latitude": 45.4222, "longitude": -97.9175, "accuracy": 4, "coordinates": {"lon": -97.9175, "lat": 45.4222}}, {"country_code": "US", "postal_code": "57213", "place_name": "Astoria", "admin_name1": "South Dakota", "admin_code1": "SD", "admin_name2": "<PERSON><PERSON>", "admin_code2": "039", "admin_name3": null, "admin_code3": null, "latitude": 44.5735, "longitude": -96.5416, "accuracy": 4, "coordinates": {"lon": -96.5416, "lat": 44.5735}}, {"country_code": "US", "postal_code": "57237", "place_name": "<PERSON>", "admin_name1": "South Dakota", "admin_code1": "SD", "admin_name2": "<PERSON><PERSON>", "admin_code2": "039", "admin_name3": null, "admin_code3": null, "latitude": 44.827, "longitude": -96.5044, "accuracy": 4, "coordinates": {"lon": -96.5044, "lat": 44.827}}, {"country_code": "US", "postal_code": "57238", "place_name": "<PERSON>", "admin_name1": "South Dakota", "admin_code1": "SD", "admin_name2": "<PERSON><PERSON>", "admin_code2": "039", "admin_name3": null, "admin_code3": null, "latitude": 44.8754, "longitude": -96.8608, "accuracy": 4, "coordinates": {"lon": -96.8608, "lat": 44.8754}}, {"country_code": "US", "postal_code": "57782", "place_name": "<PERSON><PERSON>", "admin_name1": "South Dakota", "admin_code1": "SD", "admin_name2": "Fall River", "admin_code2": "047", "admin_name3": null, "admin_code3": null, "latitude": 43.3014, "longitude": -103.2183, "accuracy": 4, "coordinates": {"lon": -103.2183, "lat": 43.3014}}, {"country_code": "US", "postal_code": "57251", "place_name": "<PERSON>", "admin_name1": "South Dakota", "admin_code1": "SD", "admin_name2": "<PERSON>", "admin_code2": "051", "admin_name3": null, "admin_code3": null, "latitude": 45.2727, "longitude": -96.91, "accuracy": 4, "coordinates": {"lon": -96.91, "lat": 45.2727}}, {"country_code": "US", "postal_code": "57759", "place_name": "Nemo", "admin_name1": "South Dakota", "admin_code1": "SD", "admin_name2": "<PERSON>", "admin_code2": "081", "admin_name3": null, "admin_code3": null, "latitude": 44.2097, "longitude": -103.5449, "accuracy": 4, "coordinates": {"lon": -103.5449, "lat": 44.2097}}, {"country_code": "US", "postal_code": "57769", "place_name": "Piedmont", "admin_name1": "South Dakota", "admin_code1": "SD", "admin_name2": "<PERSON><PERSON>", "admin_code2": "093", "admin_name3": null, "admin_code3": null, "latitude": 44.2287, "longitude": -103.3688, "accuracy": 4, "coordinates": {"lon": -103.3688, "lat": 44.2287}}, {"country_code": "US", "postal_code": "57785", "place_name": "<PERSON><PERSON><PERSON>", "admin_name1": "South Dakota", "admin_code1": "SD", "admin_name2": "<PERSON><PERSON>", "admin_code2": "093", "admin_name3": null, "admin_code3": null, "latitude": 44.3692, "longitude": -103.3617, "accuracy": 4, "coordinates": {"lon": -103.3617, "lat": 44.3692}}, {"country_code": "US", "postal_code": "57020", "place_name": "Crooks", "admin_name1": "South Dakota", "admin_code1": "SD", "admin_name2": "<PERSON><PERSON><PERSON><PERSON>", "admin_code2": "099", "admin_name3": null, "admin_code3": null, "latitude": 43.6669, "longitude": -96.8221, "accuracy": 4, "coordinates": {"lon": -96.8221, "lat": 43.6669}}, {"country_code": "US", "postal_code": "37804", "place_name": "Maryville", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON><PERSON><PERSON>", "admin_code2": "009", "admin_name3": null, "admin_code3": null, "latitude": 35.7991, "longitude": -83.8852, "accuracy": 4, "coordinates": {"lon": -83.8852, "lat": 35.7991}}, {"country_code": "US", "postal_code": "37146", "place_name": "Pleasant View", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>eatham", "admin_code2": "021", "admin_name3": null, "admin_code3": null, "latitude": 36.3783, "longitude": -87.0395, "accuracy": 4, "coordinates": {"lon": -87.0395, "lat": 36.3783}}, {"country_code": "US", "postal_code": "37707", "place_name": "<PERSON>", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON><PERSON><PERSON>", "admin_code2": "025", "admin_name3": null, "admin_code3": null, "latitude": 36.5481, "longitude": -83.6707, "accuracy": 4, "coordinates": {"lon": -83.6707, "lat": 36.5481}}, {"country_code": "US", "postal_code": "38050", "place_name": "Maury City", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON><PERSON><PERSON>", "admin_code2": "033", "admin_name3": null, "admin_code3": null, "latitude": 35.8379, "longitude": -89.2273, "accuracy": 4, "coordinates": {"lon": -89.2273, "lat": 35.8379}}, {"country_code": "US", "postal_code": "38024", "place_name": "Dyersburg", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "045", "admin_name3": null, "admin_code3": null, "latitude": 36.0444, "longitude": -89.3836, "accuracy": 4, "coordinates": {"lon": -89.3836, "lat": 36.0444}}, {"country_code": "US", "postal_code": "37743", "place_name": "Greeneville", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "059", "admin_name3": null, "admin_code3": null, "latitude": 36.1316, "longitude": -82.8692, "accuracy": 4, "coordinates": {"lon": -82.8692, "lat": 36.1316}}, {"country_code": "US", "postal_code": "38224", "place_name": "Cottage Grove", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "079", "admin_name3": null, "admin_code3": null, "latitude": 36.3479, "longitude": -88.4614, "accuracy": 4, "coordinates": {"lon": -88.4614, "lat": 36.3479}}, {"country_code": "US", "postal_code": "38231", "place_name": "<PERSON>", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "079", "admin_name3": null, "admin_code3": null, "latitude": 36.2039, "longitude": -88.4198, "accuracy": 4, "coordinates": {"lon": -88.4198, "lat": 36.2039}}, {"country_code": "US", "postal_code": "37806", "place_name": "Mascot", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "093", "admin_name3": null, "admin_code3": null, "latitude": 36.0844, "longitude": -83.7411, "accuracy": 4, "coordinates": {"lon": -83.7411, "lat": 36.0844}}, {"country_code": "US", "postal_code": "37922", "place_name": "Knoxville", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "093", "admin_name3": null, "admin_code3": null, "latitude": 35.858, "longitude": -84.1194, "accuracy": 4, "coordinates": {"lon": -84.1194, "lat": 35.858}}, {"country_code": "US", "postal_code": "37348", "place_name": "<PERSON><PERSON><PERSON>", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "Lincoln", "admin_code2": "103", "admin_name3": null, "admin_code3": null, "latitude": 35.1024, "longitude": -86.4683, "accuracy": 4, "coordinates": {"lon": -86.4683, "lat": 35.1024}}, {"country_code": "US", "postal_code": "37380", "place_name": "South Pittsburg", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "115", "admin_name3": null, "admin_code3": null, "latitude": 35.028, "longitude": -85.7225, "accuracy": 4, "coordinates": {"lon": -85.7225, "lat": 35.028}}, {"country_code": "US", "postal_code": "37019", "place_name": "Belfast", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "117", "admin_name3": null, "admin_code3": null, "latitude": 35.4069, "longitude": -86.7095, "accuracy": 4, "coordinates": {"lon": -86.7095, "lat": 35.4069}}, {"country_code": "US", "postal_code": "38549", "place_name": "Byrdstown", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON><PERSON>", "admin_code2": "137", "admin_name3": null, "admin_code3": null, "latitude": 36.5709, "longitude": -85.1456, "accuracy": 4, "coordinates": {"lon": -85.1456, "lat": 36.5709}}, {"country_code": "US", "postal_code": "38506", "place_name": "Cookeville", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "141", "admin_name3": null, "admin_code3": null, "latitude": 36.1819, "longitude": -85.4408, "accuracy": 4, "coordinates": {"lon": -85.4408, "lat": 36.1819}}, {"country_code": "US", "postal_code": "37085", "place_name": "Lascassas", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "149", "admin_name3": null, "admin_code3": null, "latitude": 35.9495, "longitude": -86.3112, "accuracy": 4, "coordinates": {"lon": -86.3112, "lat": 35.9495}}, {"country_code": "US", "postal_code": "37127", "place_name": "<PERSON><PERSON><PERSON>sboro", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "149", "admin_name3": null, "admin_code3": null, "latitude": 35.763, "longitude": -86.3722, "accuracy": 4, "coordinates": {"lon": -86.3722, "lat": 35.763}}, {"country_code": "US", "postal_code": "37131", "place_name": "<PERSON><PERSON><PERSON>sboro", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "149", "admin_name3": null, "admin_code3": null, "latitude": 35.8596, "longitude": -86.421, "accuracy": 4, "coordinates": {"lon": -86.421, "lat": 35.8596}}, {"country_code": "US", "postal_code": "37153", "place_name": "Rockvale", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "149", "admin_name3": null, "admin_code3": null, "latitude": 35.745, "longitude": -86.5352, "accuracy": 4, "coordinates": {"lon": -86.5352, "lat": 35.745}}, {"country_code": "US", "postal_code": "38014", "place_name": "Brunswick", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "157", "admin_name3": null, "admin_code3": null, "latitude": 35.2673, "longitude": -89.7687, "accuracy": 4, "coordinates": {"lon": -89.7687, "lat": 35.2673}}, {"country_code": "US", "postal_code": "38108", "place_name": "Memphis", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "157", "admin_name3": null, "admin_code3": null, "latitude": 35.1787, "longitude": -89.9682, "accuracy": 4, "coordinates": {"lon": -89.9682, "lat": 35.1787}}, {"country_code": "US", "postal_code": "37079", "place_name": "Indian Mound", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "161", "admin_name3": null, "admin_code3": null, "latitude": 36.4946, "longitude": -87.6804, "accuracy": 4, "coordinates": {"lon": -87.6804, "lat": 36.4946}}, {"country_code": "US", "postal_code": "37075", "place_name": "Hendersonville", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "165", "admin_name3": null, "admin_code3": null, "latitude": 36.3054, "longitude": -86.6072, "accuracy": 4, "coordinates": {"lon": -86.6072, "lat": 36.3054}}, {"country_code": "US", "postal_code": "37394", "place_name": "<PERSON>", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "177", "admin_name3": null, "admin_code3": null, "latitude": 35.5384, "longitude": -85.8591, "accuracy": 4, "coordinates": {"lon": -85.8591, "lat": 35.5384}}, {"country_code": "US", "postal_code": "38425", "place_name": "Clifton", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "181", "admin_name3": null, "admin_code3": null, "latitude": 35.3819, "longitude": -87.95, "accuracy": 4, "coordinates": {"lon": -87.95, "lat": 35.3819}}, {"country_code": "US", "postal_code": "37071", "place_name": "Gladeville", "admin_name1": "Tennessee", "admin_code1": "TN", "admin_name2": "<PERSON>", "admin_code2": "189", "admin_name3": null, "admin_code3": null, "latitude": 36.1562, "longitude": -86.3049, "accuracy": 4, "coordinates": {"lon": -86.3049, "lat": 36.1562}}, {"country_code": "US", "postal_code": "79714", "place_name": "<PERSON>", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "003", "admin_name3": null, "admin_code3": null, "latitude": 32.3201, "longitude": -102.5409, "accuracy": 4, "coordinates": {"lon": -102.5409, "lat": 32.3201}}, {"country_code": "US", "postal_code": "78026", "place_name": "<PERSON><PERSON><PERSON><PERSON>", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "Atascosa", "admin_code2": "013", "admin_name3": null, "admin_code3": null, "latitude": 28.903, "longitude": -98.544, "accuracy": 4, "coordinates": {"lon": -98.544, "lat": 28.903}}, {"country_code": "US", "postal_code": "78650", "place_name": "<PERSON><PERSON> Dade", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "Bastrop", "admin_code2": "021", "admin_name3": null, "admin_code3": null, "latitude": 30.2968, "longitude": -97.2386, "accuracy": 1, "coordinates": {"lon": -97.2386, "lat": 30.2968}}, {"country_code": "US", "postal_code": "78240", "place_name": "San Antonio", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "Bexar", "admin_code2": "029", "admin_name3": null, "admin_code3": null, "latitude": 29.5189, "longitude": -98.6006, "accuracy": 4, "coordinates": {"lon": -98.6006, "lat": 29.5189}}, {"country_code": "US", "postal_code": "78663", "place_name": "Round Mountain", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "031", "admin_name3": null, "admin_code3": null, "latitude": 30.4429, "longitude": -98.4365, "accuracy": 4, "coordinates": {"lon": -98.4365, "lat": 30.4429}}, {"country_code": "US", "postal_code": "75573", "place_name": "Redwater", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "037", "admin_name3": null, "admin_code3": null, "latitude": 33.358, "longitude": -94.2571, "accuracy": 4, "coordinates": {"lon": -94.2571, "lat": 33.358}}, {"country_code": "US", "postal_code": "77805", "place_name": "<PERSON>", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "Brazos", "admin_code2": "041", "admin_name3": null, "admin_code3": null, "latitude": 30.6521, "longitude": -96.341, "accuracy": 4, "coordinates": {"lon": -96.341, "lat": 30.6521}}, {"country_code": "US", "postal_code": "76802", "place_name": "Early", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "049", "admin_name3": null, "admin_code3": null, "latitude": 31.7874, "longitude": -98.9229, "accuracy": 4, "coordinates": {"lon": -98.9229, "lat": 31.7874}}, {"country_code": "US", "postal_code": "77838", "place_name": "<PERSON><PERSON><PERSON>", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON><PERSON><PERSON><PERSON>", "admin_code2": "051", "admin_name3": null, "admin_code3": null, "latitude": 30.5994, "longitude": -96.7708, "accuracy": 4, "coordinates": {"lon": -96.7708, "lat": 30.5994}}, {"country_code": "US", "postal_code": "77982", "place_name": "Port O Connor", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "057", "admin_name3": null, "admin_code3": null, "latitude": 28.4483, "longitude": -96.4058, "accuracy": 4, "coordinates": {"lon": -96.4058, "lat": 28.4483}}, {"country_code": "US", "postal_code": "75766", "place_name": "Jacksonville", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "Cherokee", "admin_code2": "073", "admin_name3": null, "admin_code3": null, "latitude": 31.9618, "longitude": -95.2703, "accuracy": 4, "coordinates": {"lon": -95.2703, "lat": 31.9618}}, {"country_code": "US", "postal_code": "75094", "place_name": "Plano", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "085", "admin_name3": null, "admin_code3": null, "latitude": 33.0148, "longitude": -96.6189, "accuracy": 4, "coordinates": {"lon": -96.6189, "lat": 33.0148}}, {"country_code": "US", "postal_code": "78962", "place_name": "Weimar", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "Colorado", "admin_code2": "089", "admin_name3": null, "admin_code3": null, "latitude": 29.6787, "longitude": -96.755, "accuracy": 4, "coordinates": {"lon": -96.755, "lat": 29.6787}}, {"country_code": "US", "postal_code": "76468", "place_name": "Proctor", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "Comanche", "admin_code2": "093", "admin_name3": null, "admin_code3": null, "latitude": 31.9874, "longitude": -98.4298, "accuracy": 4, "coordinates": {"lon": -98.4298, "lat": 31.9874}}, {"country_code": "US", "postal_code": "76855", "place_name": "Lowake", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON><PERSON>", "admin_code2": "095", "admin_name3": null, "admin_code3": null, "latitude": 31.5663, "longitude": -100.0759, "accuracy": 4, "coordinates": {"lon": -100.0759, "lat": 31.5663}}, {"country_code": "US", "postal_code": "76866", "place_name": "Paint Rock", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON><PERSON>", "admin_code2": "095", "admin_name3": null, "admin_code3": null, "latitude": 31.5048, "longitude": -99.9139, "accuracy": 4, "coordinates": {"lon": -99.9139, "lat": 31.5048}}, {"country_code": "US", "postal_code": "75080", "place_name": "<PERSON>", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "Dallas", "admin_code2": "113", "admin_name3": null, "admin_code3": null, "latitude": 32.966, "longitude": -96.7452, "accuracy": 4, "coordinates": {"lon": -96.7452, "lat": 32.966}}, {"country_code": "US", "postal_code": "75217", "place_name": "Dallas", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "Dallas", "admin_code2": "113", "admin_name3": null, "admin_code3": null, "latitude": 32.7244, "longitude": -96.6755, "accuracy": 4, "coordinates": {"lon": -96.6755, "lat": 32.7244}}, {"country_code": "US", "postal_code": "75220", "place_name": "Dallas", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "Dallas", "admin_code2": "113", "admin_name3": null, "admin_code3": null, "latitude": 32.8681, "longitude": -96.8622, "accuracy": 4, "coordinates": {"lon": -96.8622, "lat": 32.8681}}, {"country_code": "US", "postal_code": "75376", "place_name": "Dallas", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "Dallas", "admin_code2": "113", "admin_name3": null, "admin_code3": null, "latitude": 32.7673, "longitude": -96.7776, "accuracy": 4, "coordinates": {"lon": -96.7776, "lat": 32.7673}}, {"country_code": "US", "postal_code": "79331", "place_name": "<PERSON><PERSON><PERSON>", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "115", "admin_name3": null, "admin_code3": null, "latitude": 32.7367, "longitude": -101.9569, "accuracy": 4, "coordinates": {"lon": -101.9569, "lat": 32.7367}}, {"country_code": "US", "postal_code": "79838", "place_name": "<PERSON><PERSON><PERSON>", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "El Paso", "admin_code2": "141", "admin_name3": null, "admin_code3": null, "latitude": 31.4745, "longitude": -106.1589, "accuracy": 4, "coordinates": {"lon": -106.1589, "lat": 31.4745}}, {"country_code": "US", "postal_code": "79925", "place_name": "El Paso", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "El Paso", "admin_code2": "141", "admin_name3": null, "admin_code3": null, "latitude": 31.7814, "longitude": -106.3613, "accuracy": 4, "coordinates": {"lon": -106.3613, "lat": 31.7814}}, {"country_code": "US", "postal_code": "79946", "place_name": "El Paso", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "El Paso", "admin_code2": "141", "admin_name3": null, "admin_code3": null, "latitude": 31.7587, "longitude": -106.4869, "accuracy": 4, "coordinates": {"lon": -106.4869, "lat": 31.7587}}, {"country_code": "US", "postal_code": "79952", "place_name": "El Paso", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "El Paso", "admin_code2": "141", "admin_name3": null, "admin_code3": null, "latitude": 31.7587, "longitude": -106.4869, "accuracy": 4, "coordinates": {"lon": -106.4869, "lat": 31.7587}}, {"country_code": "US", "postal_code": "88540", "place_name": "El Paso", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "El Paso", "admin_code2": "141", "admin_name3": null, "admin_code3": null, "latitude": 31.6948, "longitude": -106.3, "accuracy": 4, "coordinates": {"lon": -106.3, "lat": 31.6948}}, {"country_code": "US", "postal_code": "88543", "place_name": "El Paso", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "El Paso", "admin_code2": "141", "admin_name3": null, "admin_code3": null, "latitude": 31.6948, "longitude": -106.3, "accuracy": 4, "coordinates": {"lon": -106.3, "lat": 31.6948}}, {"country_code": "US", "postal_code": "88572", "place_name": "El Paso", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "El Paso", "admin_code2": "141", "admin_name3": null, "admin_code3": null, "latitude": 31.6948, "longitude": -106.3, "accuracy": 4, "coordinates": {"lon": -106.3, "lat": 31.6948}}, {"country_code": "US", "postal_code": "88574", "place_name": "El Paso", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "El Paso", "admin_code2": "141", "admin_name3": null, "admin_code3": null, "latitude": 31.6948, "longitude": -106.3, "accuracy": 4, "coordinates": {"lon": -106.3, "lat": 31.6948}}, {"country_code": "US", "postal_code": "88595", "place_name": "El Paso", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "El Paso", "admin_code2": "141", "admin_name3": null, "admin_code3": null, "latitude": 31.6948, "longitude": -106.3, "accuracy": 4, "coordinates": {"lon": -106.3, "lat": 31.6948}}, {"country_code": "US", "postal_code": "75490", "place_name": "Trenton", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON><PERSON>", "admin_code2": "147", "admin_name3": null, "admin_code3": null, "latitude": 33.4235, "longitude": -96.3398, "accuracy": 4, "coordinates": {"lon": -96.3398, "lat": 33.4235}}, {"country_code": "US", "postal_code": "78952", "place_name": "<PERSON><PERSON>", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON><PERSON>", "admin_code2": "149", "admin_name3": null, "admin_code3": null, "latitude": 29.9349, "longitude": -96.9675, "accuracy": 4, "coordinates": {"lon": -96.9675, "lat": 29.9349}}, {"country_code": "US", "postal_code": "79546", "place_name": "Rotan", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "151", "admin_name3": null, "admin_code3": null, "latitude": 32.8555, "longitude": -100.4705, "accuracy": 4, "coordinates": {"lon": -100.4705, "lat": 32.8555}}, {"country_code": "US", "postal_code": "79258", "place_name": "South Plains", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "153", "admin_name3": null, "admin_code3": null, "latitude": 34.2245, "longitude": -101.3096, "accuracy": 4, "coordinates": {"lon": -101.3096, "lat": 34.2245}}, {"country_code": "US", "postal_code": "77563", "place_name": "<PERSON>", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "Galveston", "admin_code2": "167", "admin_name3": null, "admin_code3": null, "latitude": 29.3398, "longitude": -94.9926, "accuracy": 4, "coordinates": {"lon": -94.9926, "lat": 29.3398}}, {"country_code": "US", "postal_code": "76245", "place_name": "Gordonville", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "Grayson", "admin_code2": "181", "admin_name3": null, "admin_code3": null, "latitude": 33.8343, "longitude": -96.8403, "accuracy": 4, "coordinates": {"lon": -96.8403, "lat": 33.8343}}, {"country_code": "US", "postal_code": "76531", "place_name": "<PERSON>", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "193", "admin_name3": null, "admin_code3": null, "latitude": 31.6781, "longitude": -98.1131, "accuracy": 4, "coordinates": {"lon": -98.1131, "lat": 31.6781}}, {"country_code": "US", "postal_code": "77027", "place_name": "Houston", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "201", "admin_name3": null, "admin_code3": null, "latitude": 29.7396, "longitude": -95.446, "accuracy": 4, "coordinates": {"lon": -95.446, "lat": 29.7396}}, {"country_code": "US", "postal_code": "77030", "place_name": "Houston", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "201", "admin_name3": null, "admin_code3": null, "latitude": 29.7041, "longitude": -95.401, "accuracy": 4, "coordinates": {"lon": -95.401, "lat": 29.7041}}, {"country_code": "US", "postal_code": "77055", "place_name": "Houston", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "201", "admin_name3": null, "admin_code3": null, "latitude": 29.7971, "longitude": -95.4958, "accuracy": 4, "coordinates": {"lon": -95.4958, "lat": 29.7971}}, {"country_code": "US", "postal_code": "77089", "place_name": "Houston", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "201", "admin_name3": null, "admin_code3": null, "latitude": 29.594, "longitude": -95.2218, "accuracy": 4, "coordinates": {"lon": -95.2218, "lat": 29.594}}, {"country_code": "US", "postal_code": "77090", "place_name": "Houston", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "201", "admin_name3": null, "admin_code3": null, "latitude": 30.0167, "longitude": -95.447, "accuracy": 4, "coordinates": {"lon": -95.447, "lat": 30.0167}}, {"country_code": "US", "postal_code": "77210", "place_name": "Houston", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "201", "admin_name3": null, "admin_code3": null, "latitude": 29.7633, "longitude": -95.3633, "accuracy": 4, "coordinates": {"lon": -95.3633, "lat": 29.7633}}, {"country_code": "US", "postal_code": "77218", "place_name": "Houston", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "201", "admin_name3": null, "admin_code3": null, "latitude": 29.834, "longitude": -95.4342, "accuracy": 4, "coordinates": {"lon": -95.4342, "lat": 29.834}}, {"country_code": "US", "postal_code": "77220", "place_name": "Houston", "admin_name1": "Texas", "admin_code1": "TX", "admin_name2": "<PERSON>", "admin_code2": "201", "admin_name3": null, "admin_code3": null, "latitude": 29.834, "longitude": -95.4342, "accuracy": 4, "coordinates": {"lon": -95.4342, "lat": 29.834}}]}