import { userAPI } from '@/APIs';
import axios, { default as Axios } from 'axios';
import { FileUploadRequestBody } from 'models';

export const handleMediaUpload = async (
  fileData: FileUploadRequestBody,
  file: any,
  token: string,
  isPublic: boolean
) => {
  try {
    let res;
    if (!isPublic) {
      res = await userAPI.uploadMedia(fileData);
      delete axios.defaults.headers.common['Authorization'];
      await axios.put(res.data[0].presignedUrl, file, {
        headers: {
          'Content-Type': file.type,
        },
      });
      Axios.defaults.headers.common = {
        Authorization: `Bearer ${token}`,
      };
    } else {
      res = await userAPI.uploadPublicMedia(file, fileData.featureName);
    }
    return res.data;
  } catch (error) {
    console.log(error);
  }
};
