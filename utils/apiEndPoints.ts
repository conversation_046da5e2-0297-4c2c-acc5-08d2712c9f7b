export const apiEndPoints = {
  getUser: `/todos`,
  product: '/admin/products',
  signin: '/admin-auth/sign-in',
  auth: '/admin-auth',
  user: '/admin',
  manufacturer: '/manufacturers/create-manufacturer',
  manufacturerList: '/manufacturers',
  orderEnum: '/admin/orders/order-enums',
  orderList: '/admin/orders/list',
  order: '/admin/orders',
  updateOrderStatus: '/admin/orders/change-status',
  category: '/categories',
  createCategory: '/category',
  brands: '/brands',
  media: '/media/upload-presigned-url',
  tags: '/tags',
  singleOrder: '/admin/orders',
  tag: '/tags',
  clubs: '/clubs',
  updateClubMember: '/clubs/member-approval',
  createBaseExercise: '/base-exercise/create',
  getBaseExercises: '/base-exercise/list',
  updateBaseExercise: '/base-exercise/update',
  deleteBaseExercise: '/base-exercise/delete',
  baseExercise: '/base-exercise',
  challenge: '/admin/challenges',
  acceptedChallenge: '/admin/challenges/accepted',
  trainingCategory: '/training/category',
  muscleGroup: '/training/muscle-group',
  training: '/training',
  bbProgram: '/training/body-building-program',
  publicMedia: '/media/public/upload/single',
  payment: '/admin/payment',
  review: '/admin/order/review',
  shipment: '/shipment',
  waitList: '/public/subscribers',
  blogs: '/admin/blogs',
  packages: '/admin/packages',
  dashboard: '/dashboard',
  userList: '/admin/users',
  deleteUser: '/admin/user/delete-account',
  wp_blogs: '/posts',
  adminPoll: '/admin/poll',
  userPoll: '/user/polls',
  adminDeletePoll: '/admin/poll/delete',
  selectPollWinner: '/admin/poll/winner-list',
  createEvent: '/events/create',
  getMultipleEvent: '/events/multiple',
  updateEvent: '/events/update',
  deleteEvent: '/events/delete',
  getSingleEvent: '/events/single',
  getEventUserDetails: '/events/user-details',
  
  getCoachProfiles: '/admin/coach/profile/pendings',
  getSingleCoachProfile: '/admin/coach/profile/pending',
  deletePendingCoachProfile: '/admin/coach/profile/pending',
  approvePendingCoachProfile: '/admin/coach/profile/pending',
  deleteCoachProfile: '/admin/coach/profile/pending',
  createCoachCategory: '/admin/coach/category',
  getCoachCategories: '/admin/coach/categories',
  getCoachCategory: '/admin/coach/category',
  updateCoachCategory: '/admin/coach/category',
  deleteCoachCategory: '/admin/coach/category',
  getCoachCategoryMap: '/admin/coach/category-map',
  createCoachSubCategory: '/admin/coach/sub-category',
  getCoachSubCategories: '/admin/coach/sub-categories',
  getCoachSubCategory: '/admin/coach/sub-category',
  updateCoachSubCategory: '/admin/coach/sub-category',
  deleteCoachSubCategory: '/admin/coach/sub-category',
  
  getMultipleWithdraw: '/admin/coach/withdraws',
  getSingleWithdraw: '/admin/coach/withdraw',
  approveWithdraw: '/admin/coach/withdraw',
  rejectWithdraw: '/admin/coach/withdraw',
  
  getMultipleRefund: '/admin/coach/refunds',
  getSingleRefund: '/admin/coach/refund',
  approveRefund: '/admin/coach/refund',
  rejectRefund: '/admin/coach/refund',
};
