import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface modalState {
  show: boolean;
  edit: boolean;
}

const initialState: modalState = {
  show: false,
  edit: false
};

export const modalSlice = createSlice({
  name: 'modal',
  initialState,
  reducers: {
    showModal: (state, action: PayloadAction<boolean>) => {
      state.show = action.payload;
    },
    enableEdit: (state, action: PayloadAction<boolean>) => {
        state.edit = action.payload;
    },
  },
});

export const { showModal, enableEdit } = modalSlice.actions;

export default modalSlice.reducer;
