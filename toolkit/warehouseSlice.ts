import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Product } from 'models';

export interface Warehouse {
  name: string;
  title: string;
  owner: string;
  email: string;
  phone: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  postCode: number;
  status: string;
  products: Product[];
}

interface WarehouseState {
  warehouse: Warehouse;
}

const initialState: WarehouseState = {
  warehouse: {
    name: '',
    title: '',
    owner: '',
    email: '',
    phone: '',
    address1: '',
    address2: '',
    city: '',
    state: '',
    postCode: 0,
    status: '',
    products: [],
  },
};

export const warehouseSlice = createSlice({
  name: 'warehouse',
  initialState,
  reducers: {
    saveWarehouse: (state, action: PayloadAction<Warehouse>) => {
      state.warehouse = action.payload;
    },
    removeWarehouse: (state) => {
      state.warehouse = {
        name: '',
        title: '',
        owner: '',
        email: '',
        phone: '',
        address1: '',
        address2: '',
        city: '',
        state: '',
        postCode: 0,
        status: '',
        products: [],
      };
    },
  },
});

export const { saveWarehouse, removeWarehouse } = warehouseSlice.actions;

export default warehouseSlice.reducer;
