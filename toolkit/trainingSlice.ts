import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ICreateBaseExcercise } from 'models';

export interface trainingState {
  level: string;
  category?: string;
  muscleGroup?: string;
  weeks?: number;
  bbprogram?: string;
  selectedWeek?: number;
  selectedDay?: string;
  selectedExercises?: ICreateBaseExcercise[];
}

const initialState: trainingState = {
  level: '',
  category: '',
  muscleGroup: '',
  weeks: 0,
  bbprogram: '',
  selectedWeek: 0,
  selectedDay: '',
  selectedExercises: [],
};

export const trainingSlice = createSlice({
  name: 'training',
  initialState,
  reducers: {
    saveTrainingInfo: (state, action: PayloadAction<trainingState>) => {
      state.level = action.payload.level;
      state.category = action.payload.category;
      state.muscleGroup = action.payload.muscleGroup;
      state.weeks = action.payload.weeks;
      state.bbprogram = action.payload.bbprogram;
      state.selectedDay = action.payload.selectedDay;
      state.selectedWeek = action.payload.selectedWeek;
      state.selectedExercises = action.payload.selectedExercises;
    },
    resetTrainingInfo: (state) => {
      state = initialState;
    },
  },
});

export const { saveTrainingInfo, resetTrainingInfo } = trainingSlice.actions;

export default trainingSlice.reducer;
