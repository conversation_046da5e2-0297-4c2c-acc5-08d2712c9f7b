{"compilerOptions": {"strictPropertyInitialization": false, "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@/components/*": ["components/*"], "@/styles/*": ["styles/*"], "@/toolkit/*": ["toolkit/*"], "@/assets/*": ["assets/*"], "@/APIs": ["APIs/index.ts"], "@/redux-hooks": ["./redux-hooks.ts"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}