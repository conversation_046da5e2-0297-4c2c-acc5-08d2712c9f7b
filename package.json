{"name": "superadmin", "version": "0.1.0", "private": true, "scripts": {"dev": "next -p 4001", "build": "next build", "start": "next start -p 4001", "lint": "tsc --noEmit && eslint .", "cypress": "cypress open", "check-types": "tsc --noEmit", "prepare": "husky install"}, "dependencies": {"@apollo/client": "^3.6.8", "@bs-commerce/models": "^2.2.2", "@react-google-maps/api": "^2.18.1", "@reduxjs/toolkit": "^1.8.2", "@types/dompurify": "^3.2.0", "@types/file-saver": "^2.0.7", "@types/googlemaps": "^3.43.3", "@types/react-color": "^3.0.6", "@types/react-redux": "^7.1.24", "@types/react-select": "^5.0.1", "axios": "^0.27.2", "bootstrap": "^5.1.3", "bootstrap-icons": "^1.8.1", "chart.js": "^3.8.0", "dompurify": "^3.2.4", "file-saver": "^2.0.5", "formik": "^2.2.9", "graphql": "^16.5.0", "jsonwebtoken": "^8.5.1", "moment": "^2.29.4", "next": "^13.0.4", "next-transpile-modules": "^10.0.0", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-chartjs-2": "^4.2.0", "react-color": "^2.19.3", "react-datepicker": "^4.8.0", "react-dom": "^18.2.0", "react-google-map-picker": "^1.2.3", "react-redux": "^8.0.2", "react-to-print": "^2.14.12", "react-toastify": "^9.0.5", "redux-hooks": "^0.1.5", "redux-persist": "^6.0.0", "suneditor": "^2.44.12", "suneditor-react": "^3.5.0", "xlsx": "^0.18.5", "xregexp": "^5.1.1", "yup": "^0.32.11"}, "devDependencies": {"@types/node": "17.0.22", "@types/react": "17.0.41", "@types/react-datepicker": "^4.4.2", "@types/react-select": "^5.0.1", "cypress": "^10.3.0", "eslint": "8.11.0", "eslint-config-next": "^13.0.4", "husky": "^8.0.0", "prettier": "^2.7.1", "react-select": "^5.4.0", "sass": "^1.49.9", "typescript": "4.6.2"}, "resolutions": {"@types/react": "17.0.2", "@types/react-dom": "17.0.2"}}